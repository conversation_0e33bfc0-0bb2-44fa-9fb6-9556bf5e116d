stages:
  - build
  - dev
  - staging

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ''

admin:
  stage: build
  tags:
    - runner-velodiva-production-idc
  image: docker:26.0.1-dind
  services:
    - name: docker:26.0.1-dind
      alias: docker
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker pull $CI_REGISTRY_IMAGE/admin:latest || true
    - docker build
      --cache-from $CI_REGISTRY_IMAGE/admin:latest
      -f apps/admin/Dockerfile --pull
      -t $CI_REGISTRY_IMAGE/admin:latest .
    - docker push $CI_REGISTRY_IMAGE/admin:latest
  only:
    refs:
      - develop
    changes:
      - 'apps/admin/**/*'
      - '**/*.json'
      - 'libs/**/*'

commercial:
  stage: build
  tags:
    - runner-velodiva-production-idc
  image: docker:26.0.1-dind
  services:
    - name: docker:26.0.1-dind
      alias: docker
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker pull $CI_REGISTRY_IMAGE/commercial:latest || true
    - docker build
      --cache-from $CI_REGISTRY_IMAGE/commercial:latest
      -f apps/commercial/Dockerfile --pull
      -t $CI_REGISTRY_IMAGE/commercial:latest .
    - docker push $CI_REGISTRY_IMAGE/commercial:latest
  only:
    refs:
      - develop
    changes:
      - 'apps/commercial/**/*'
      - '**/*.json'
      - 'libs/**/*'

atv:
  stage: build
  tags:
    - runner-velodiva-production-idc
  image: docker:26.0.1-dind
  services:
    - name: docker:26.0.1-dind
      alias: docker
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker pull $CI_REGISTRY_IMAGE/atv:latest || true
    - docker build
      --cache-from $CI_REGISTRY_IMAGE/atv:latest
      -f apps/atv/Dockerfile --pull
      -t $CI_REGISTRY_IMAGE/atv:latest .
    - docker push $CI_REGISTRY_IMAGE/atv:latest
  only:
    refs:
      - develop
    changes:
      - 'apps/atv/**/*'
      - '**/*.json'
      - 'libs/**/*'

mob:
  stage: build
  tags:
    - runner-velodiva-production-idc
  image: docker:26.0.1-dind
  services:
    - name: docker:26.0.1-dind
      alias: docker
  before_script:
    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
  script:
    - docker pull $CI_REGISTRY_IMAGE/mob:latest || true
    - docker build
      --cache-from $CI_REGISTRY_IMAGE/mob:latest
      -f apps/mob/Dockerfile --pull
      -t $CI_REGISTRY_IMAGE/mob:latest .
    - docker push $CI_REGISTRY_IMAGE/mob:latest
  only:
    refs:
      - develop
    changes:
      - 'apps/mob/**/*'
      - '**/*.json'
      - 'libs/**/*'

deploy-dev:
  stage: dev
  tags:
    - runner-velodiva-production-idc
  image: alpine:3.19.1
  environment:
    name: dev
  before_script:
    - 'which ssh-agent || ( apk update && apk add --no-cache openssh )'
    - mkdir -p ~/.ssh
    - echo "$SSH_KEY_DEV" | tr -d '\r' > ~/.ssh/id_rsa
    - chmod 700 ~/.ssh/id_rsa
    - eval "$(ssh-agent -s)"
    - ssh-add ~/.ssh/id_rsa
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
  script:
    - ssh -o StrictHostKeyChecking=no ${SSH_USER_DEV}@${SERVER_DEV} -p ${SSH_PORT_SERVER_DEV} "export APP_IMAGE_STG=$CI_REGISTRY_IMAGE && export APP_IMAGE_STG_TAG=$TAG_IMAGE && docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY && cd ~/app-old/backend-5-service-velodiva && git pull && docker compose -f compose.dev.yml pull && docker compose -f compose.dev.yml pull && docker compose -f compose.dev.yml up -d"
  only:
    - develop
# deploy-staging:
# stage: staging
# image: alpine:3.19.1
# environment:
# name: staging
# before_script:
# - "which ssh-agent || ( apk update && apk add --no-cache openssh )"
# - mkdir -p ~/.ssh
# - echo "$SSH_KEY_STAGING" | tr -d '\r' > ~/.ssh/id_rsa
# - chmod 700 ~/.ssh/id_rsa
# - eval "$(ssh-agent -s)"
# - ssh-add ~/.ssh/id_rsa
# - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config'
# script:
# - ssh -o StrictHostKeyChecking=no ${SSH_USER_STAGING}@${SERVER_STAGING} -p 21222 "export APP_IMAGE_STG=$CI_REGISTRY_IMAGE && export APP_IMAGE_STG_TAG=$TAG_IMAGE && docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY && cd ~/api && git pull && docker compose -f compose.stg.yml pull && docker compose -f compose.stg.yml pull && docker compose -f compose.stg.yml up -d"
# only:
# - main
# when: manual
