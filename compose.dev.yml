version: '3.9'
services:
  admin:
    container_name: api-admin
    image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/admin:latest
    restart: on-failure
    environment:
      - APP_ADMIN_SERVER=https://api-internal-stg.velodiva.com
      - APP_ADMIN_PORT=8000
      - DATABASE_URL=*******************************************************/velodiva_revamp?connection_limit=50&pool_timeout=0&sslmode=require&sslcert=/app/certs/node.crt&sslkey=/app/certs/node.key&sslrootcert=/app/certs/ca.crt
      - AT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - RT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - DEFAULT_IMAGE_64_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
      - DEFAULT_IMAGE_300_URL=https://bucket.velodiva.com/public/default-thumbnail/300x300.svg
      - DEFAULT_IMAGE_640_URL=https://bucket.velodiva.com/public/default-thumbnail/640x640.svg
      - GMI_BASE_URL=https://api-integration-dsp.gerbangmusik.co.id/v1
      - MINIO_ENDPOINT=bucket.velodiva.com
      - CDN_URL=https://cdn.velodiva.com/
      - GRPC_URL=0.0.0.0:9000
      - MINIO_PORT=443
      - MINIO_USE_SSL=true
      - MINIO_ACCESS_KEY=sw0aTB827hiw36xFkY7E
      - MINIO_SECRET_KEY=xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
      - MINIO_BUCKET_NAME=public
      - STORAGE_PROVIDER=minio
      - PLAYLIST_THUMBNAIL_FOLDER=playlist-thumbnails
      - DEFAULT_THUMBNAIL_FOLDER=default-thumbnail
      - REPOSITORY_FOLDER=repository
      - LOGO_BACKGROUND_FOLDER=logo-background
      - REDIS_HOST=*************
      - REDIS_PORT=6379
      - REDIS_PASS=Jaguar123
      - REDIS_TTL=5
      - RMS_HOST=https://api-integration-dev.royalti.co.id/v1
      - RMS_CLIENT_ID=gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
      - RMS_CLIENT_SECRET=WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
      - DLP_HOST=http://***************:9001/v1
      - DLP_CLIENT_ID=CLIENT-26f27029b6af016e484189d4297d4119-1742104340212
      - DLP_CLIENT_SECRET=b37c143cf431460168ee5236fb470d1c0d391d63a28384d167d0ee1629729f90
      - ELASTIC_NODE=http://************:9201
      - MEBERSHIP_GRPC_HOST=*************:50052
      - BANNER_FOLDER=banner
      - GREETING_FOLDER=greeting
      - JINGLE_GRPC_HOST=*************:7000
      - PATH_KEY=6lmrJv07r71X5TBj1QKFWo43d7EPyKji
      - MAINTENANCE_BASE_URL=https://api-maintenance.velodiva.com
      - INTERNAL_API_KEY=ZGOFMVZVTFKKMURGWPKOQZKJCDGVYBBC
      - NODE_ENV=development
    ports:
      - '8000:8000'
      - '9000:9000'
    depends_on:
      - mainredis
    volumes:
      - melodiva:/app/storage
      - /srv/certs:/app/certs

  commercial:
    container_name: api-commercial
    image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/commercial:latest
    restart: on-failure
    environment:
      - APP_COMMERCIAL_SERVER=https://api-commercial-stg.velodiva.com
      - APP_COMMERCIAL_PORT=8001
      - DATABASE_URL=*******************************************************/velodiva_revamp?connection_limit=50&pool_timeout=0&pool_size=100&sslmode=require&sslcert=/app/certs/node.crt&sslkey=/app/certs/node.key&sslrootcert=/app/certs/ca.crt
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-commercial-stg.velodiva.com/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - GRPC_URL=0.0.0.0:50051
      - GRPC_PORT=50051
      - CDN_URL=https://cdn.velodiva.com/
      - GMI_BASE_URL=https://api-integration-dsp.gerbangmusik.co.id/v1
      - DEFAULT_IMAGE_64_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
      - DEFAULT_IMAGE_300_URL=https://bucket.velodiva.com/public/default-thumbnail/300x300.svg
      - DEFAULT_IMAGE_640_URL=https://bucket.velodiva.com/public/default-thumbnail/640x640.svg
      - MINIO_ENDPOINT=bucket.velodiva.com
      - MINIO_PORT=443
      - MINIO_USE_SSL=true
      - MINIO_BUCKET_NAME=public
      - MINIO_ACCESS_KEY=sw0aTB827hiw36xFkY7E
      - MINIO_SECRET_KEY=xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
      - STORAGE_PROVIDER=minio
      - PLAYLIST_THUMBNAIL_FOLDER=playlist-thumbnails
      - DEFAULT_THUMBNAIL_FOLDER=default-thumbnail
      - REPOSITORY_FOLDER=repository
      - REDIS_HOST=*************
      - REDIS_PORT=6379
      - REDIS_PASS=Jaguar123
      - REDIS_TTL=5
      - RMS_HOST=https://api-integration-dev.royalti.co.id/v1
      - RMS_CLIENT_ID=gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
      - RMS_CLIENT_SECRET=WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
      - DLP_HOST=http://***************:9001/v1
      - DLP_CLIENT_ID=CLIENT-26f27029b6af016e484189d4297d4119-1742104340212
      - DLP_CLIENT_SECRET=b37c143cf431460168ee5236fb470d1c0d391d63a28384d167d0ee1629729f90
      - ELASTIC_NODE=http://************:9201
      - SPOTIFY_CLIENT_ID=6b3e6488fcac4e2c910f6de3cc1d146f
      - SPOTIFY_CLIENT_SECRET=74ca5f15a61e4ec5a6f61163f41334d4
      - SPOTIFY_REDIRECT_URI=https://api-commercial-stg.velodiva.com/v1/spotify/callback
      - SPOTIFY_SCOPES=user-read-private user-read-email playlist-read-private
      - SPOTIFY_CLIENT_REDIRECT_URL=https://commercial-stg.velodiva.com
      - MEBERSHIP_GRPC_HOST=*************:50052
      - BANNER_FOLDER=banner
      - AVATAR_FOLDER=avatar
      - JINGLE_GRPC_HOST=*************:7000
      - PATH_KEY=6lmrJv07r71X5TBj1QKFWo43d7EPyKji
      - CLIENT_HOST=.velodiva.com
      - COOKIES_SECRET=Nvl7PedahgSCg7XZjNT4tTVyKfRqvjQo
      - NODE_ENV=development
      - LOCAL_CLIENT_URL=http://localhost:3000
      - CLIENT_URL=https://commercial-stg.velodiva.com
      - MAINTENANCE_BASE_URL=https://api-maintenance.velodiva.com
      - COMMERCIAL_API_KEY=UZEPOMJFMTAFCYTQIENUYTMWQPPEDRYT
    ports:
      - '8001:8001'
      - '50051:50051'
    depends_on:
      - mainredis
    volumes:
      - melodiva:/app/storage
      - /srv/certs:/app/certs

  # commercial-two:
  #   container_name: api-commercial-two
  #   image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/commercial:latest
  #   restart: on-failure
  #   environment:
  #     - APP_COMMERCIAL_SERVER=https://api-commercial-stg.velodiva.com
  #     - APP_COMMERCIAL_PORT=8002
  #     - DATABASE_URL=*******************************************************/velodiva_revamp?connection_limit=50&pool_timeout=0&pool_size=100&sslmode=require&sslcert=/app/certs/node.crt&sslkey=/app/certs/node.key&sslrootcert=/app/certs/ca.crt
  #     - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
  #     - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
  #     - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
  #     - GOOGLE_CLIENT_SECRET=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
  #     - CALLBACK_URL=https://api-commercial-stg.velodiva.com/v1/auth/google/callback
  #     - MAIL_HOST=inmail.vnt.net.id
  #     - MAIL_USER=<EMAIL>
  #     - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
  #     - MAIL_FROM=<EMAIL>
  #     - REDIS=redis://default:Jaguar123@mainredis:6379
  #     - GRPC_URL=0.0.0.0:50052
  #     - GRPC_PORT=50052
  #     - CDN_URL=https://cdn.velodiva.com/
  #     - GMI_BASE_URL=https://api-integration-dsp.gerbangmusik.co.id/v1
  #     - DEFAULT_IMAGE_64_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
  #     - DEFAULT_IMAGE_300_URL=https://bucket.velodiva.com/public/default-thumbnail/300x300.svg
  #     - DEFAULT_IMAGE_640_URL=https://bucket.velodiva.com/public/default-thumbnail/640x640.svg
  #     - MINIO_ENDPOINT=bucket.velodiva.com
  #     - MINIO_PORT=443
  #     - MINIO_USE_SSL=true
  #     - MINIO_BUCKET_NAME=public
  #     - STORAGE_PROVIDER=minio
  #     - MINIO_ACCESS_KEY=sw0aTB827hiw36xFkY7E
  #     - MINIO_SECRET_KEY=xssOVt9jupA35Ygluja82KkKmO3bZdC2yPEjysh3
  #     - PLAYLIST_THUMBNAIL_FOLDER=playlist-thumbnails
  #     - DEFAULT_THUMBNAIL_FOLDER=default-thumbnail
  #     - REPOSITORY_FOLDER=repository
  #     - REDIS_HOST=*************
  #     - REDIS_PORT=6379
  #     - REDIS_PASS=Jaguar123
  #     - REDIS_TTL=5
  #     - RMS_HOST=https://api-integration-dev.royalti.co.id/v1
  #     - RMS_CLIENT_ID=gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
  #     - RMS_CLIENT_SECRET=WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
  #     - DLP_HOST=http://***************:9001/v1
  #     - DLP_CLIENT_ID=CLIENT-26f27029b6af016e484189d4297d4119-1742104340212
  #     - DLP_CLIENT_SECRET=b37c143cf431460168ee5236fb470d1c0d391d63a28384d167d0ee1629729f90
  #     - ELASTIC_NODE=http://************:9201
  #     - SPOTIFY_CLIENT_ID=6b3e6488fcac4e2c910f6de3cc1d146f
  #     - SPOTIFY_CLIENT_SECRET=74ca5f15a61e4ec5a6f61163f41334d4
  #     - SPOTIFY_REDIRECT_URI=https://api-commercial-stg.velodiva.com/v1/spotify/callback
  #     - SPOTIFY_SCOPES=user-read-private user-read-email playlist-read-private
  #     - SPOTIFY_CLIENT_REDIRECT_URL=https://commercial-stg.velodiva.com
  #     - MEBERSHIP_GRPC_HOST=*************:50052
  #     - BANNER_FOLDER=banner
  #     - AVATAR_FOLDER=avatar
  #     - JINGLE_GRPC_HOST=*************:7000
  #     - PATH_KEY=6lmrJv07r71X5TBj1QKFWo43d7EPyKji
  #     - COOKIES_SECRET=Nvl7PedahgSCg7XZjNT4tTVyKfRqvjQo
  #     - NODE_ENV=development
  #     - CLIENT_HOST=.velodiva.com
  #     - LOCAL_CLIENT_URL=http://localhost:3000
  #     - CLIENT_URL=https://commercial-stg.velodiva.com
  #     - MAINTENANCE_BASE_URL=https://api-maintenance.velodiva.com
  #     - COMMERCIAL_API_KEY=UZEPOMJFMTAFCYTQIENUYTMWQPPEDRYT
  #   ports:
  #     - '8002:8002'
  #     - '50052:50052'
  #     - '7000:7000'
  #   depends_on:
  #     - mainredis
  #   volumes:
  #     - melodiva:/app/storage
  #     - /srv/certs:/app/certs

  atv:
    container_name: atv
    image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/atv:latest
    restart: on-failure
    environment:
      - APP_ATV_SERVER=https://api-atv-stg.velodiva.com
      - APP_ATV_PORT=5050
      - DATABASE_URL=*******************************************************/velodiva_revamp?connection_limit=50&pool_timeout=0&sslmode=require&sslcert=/app/certs/node.crt&sslkey=/app/certs/node.key&sslrootcert=/app/certs/ca.crt
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - GMI_BASE_URL=https://api-integration-dsp.gerbangmusik.co.id/v1
      - AT_SECRET=McKeyMouse9989
      - DEFAULT_AVATAR_URL=https://img.freepik.com/premium-photo/graphic-designer-digital-avatar-generative-ai_934475-9292.jpg
      - TZ=Asia/Jakarta
      - DEFAULT_IMAGE_64_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
      - DEFAULT_IMAGE_300_URL=https://bucket.velodiva.com/public/default-thumbnail/300x300.svg
      - DEFAULT_IMAGE_640_URL=https://bucket.velodiva.com/public/default-thumbnail/640x640.svg
      - MINIO_ENDPOINT=bucket.velodiva.com
      - REPOSITORY_FOLDER=repository
      - MINIO_BUCKET_NAME=public
      - STORAGE_PROVIDER=minio
      - REDIS_HOST=*************
      - REDIS_PORT=6379
      - REDIS_PASS=Jaguar123
      - REDIS_TTL=5
      - CDN_URL=https://cdn.velodiva.com/
      - REMOTE_HOST=https://remote-stg.velodiva.com
      - RMS_HOST=https://api-integration-dev.royalti.co.id/v1
      - RMS_CLIENT_ID=gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
      - RMS_CLIENT_SECRET=WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
      - RT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - APP_COMMERCIAL_HOST=https://api-commercial-stg.velodiva.com
      - ELASTIC_NODE=http://************:9201
      - JINGLE_GRPC_HOST=*************:7000
      - GREETING_FOLDER=greeting
      - ATV_API_KEY=UTEAVSPUZIYOASWFXNHXUYRWNUNBLCHU
      - MAINTENANCE_BASE_URL=https://api-maintenance.velodiva.com
      - NODE_ENV=development
    ports:
      - '5050:5050'
    volumes:
      - melodiva:/app/storage
      - /srv/certs:/app/certs
    depends_on:
      - mainredis

  mob:
    container_name: mob
    image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/mob:latest
    restart: on-failure
    environment:
      - APP_MOBILE_SERVER=https://api-mob-stg.velodiva.music
      - APP_MOBILE_PORT=5151
      - DATABASE_URL=*******************************************************/velodiva_revamp?connection_limit=50&pool_timeout=0&sslmode=require&sslcert=/app/certs/node.crt&sslkey=/app/certs/node.key&sslrootcert=/app/certs/ca.crt
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - GMI_BASE_URL=https://api-integration-dsp.gerbangmusik.co.id/v1
      - AT_SECRET=McKeyMouse9989
      - DEFAULT_AVATAR_URL=https://img.freepik.com/premium-photo/graphic-designer-digital-avatar-generative-ai_934475-9292.jpg
      - TZ=Asia/Jakarta
      - DEFAULT_IMAGE_64_URL=https://bucket.velodiva.com/public/default-thumbnail/64x64.svg
      - DEFAULT_IMAGE_300_URL=https://bucket.velodiva.com/public/default-thumbnail/300x300.svg
      - DEFAULT_IMAGE_640_URL=https://bucket.velodiva.com/public/default-thumbnail/640x640.svg
      - MINIO_ENDPOINT=bucket.velodiva.com
      - REPOSITORY_FOLDER=repository
      - MINIO_BUCKET_NAME=public
      - STORAGE_PROVIDER=minio
      - REDIS_HOST=*************
      - REDIS_PORT=6379
      - REDIS_PASS=Jaguar123
      - REDIS_TTL=5
      - CDN_URL=https://cdn.velodiva.com/
      - REMOTE_HOST=https://remote-stg.velodiva.com
      - RMS_HOST=https://api-integration-dev.royalti.co.id/v1
      - RMS_CLIENT_ID=gqw3YQ7LbMEqkzp4PhrVikFpEWtTz3bK
      - RMS_CLIENT_SECRET=WYood1lJMF3nyhyOZ1FfG7dA83FQV4dq
      - RT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - APP_COMMERCIAL_HOST=https://api-commercial-stg.velodiva.com
      - ELASTIC_NODE=http://************:9201
      - JINGLE_GRPC_HOST=*************:7000
      - GREETING_FOLDER=greeting
      - MAINTENANCE_BASE_URL=https://api-maintenance.velodiva.com
      - MOB_API_KEY=SHVLNNLXYRBGLJCBGXQHVSZJECRGJZDW
      - NODE_ENV=development
    ports:
      - '5151:5151'
    volumes:
      - melodiva:/app/storage
      - /srv/certs:/app/certs
    depends_on:
      - mainredis

  database:
    container_name: database
    image: postgres:15.2-alpine
    restart: unless-stopped
    environment:
      - POSTGRES_USER=jaguar
      - POSTGRES_PASSWORD=Jaguar123
      - POSTGRES_DB=melodiva
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'

  mainredis:
    container_name: redis
    image: redis:8.0.1-alpine
    restart: unless-stopped
    command: >
      --requirepass Jaguar123
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - '6379:6379'

volumes:
  db_data:
  melodiva:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '/srv/storage'
  certs:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '/srv/certs'
