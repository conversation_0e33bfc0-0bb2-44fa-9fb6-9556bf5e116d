{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/admin/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/admin/tsconfig.app.json"}, "generateOptions": {"spec": false}, "monorepo": true, "root": "apps/admin", "projects": {"admin": {"type": "application", "root": "apps/admin", "entryFile": "main", "sourceRoot": "apps/admin/src", "compilerOptions": {"tsConfigPath": "apps/admin/tsconfig.app.json", "assets": ["mail/templates/**/*", "mail/images"], "watchAssets": true}}, "commercial": {"type": "application", "root": "apps/commercial", "entryFile": "main", "sourceRoot": "apps/commercial/src", "compilerOptions": {"tsConfigPath": "apps/commercial/tsconfig.app.json", "assets": ["mail/templates/**/*", "mail/images"], "watchAssets": true}}, "proto-schema": {"type": "library", "root": "libs/proto-schema", "entryFile": "index", "sourceRoot": "libs/proto-schema/src", "compilerOptions": {"tsConfigPath": "libs/proto-schema/tsconfig.lib.json"}}, "atv": {"type": "application", "root": "apps/atv", "entryFile": "main", "sourceRoot": "apps/atv/src", "compilerOptions": {"tsConfigPath": "apps/atv/tsconfig.app.json"}}, "mob": {"type": "application", "root": "apps/mob", "entryFile": "main", "sourceRoot": "apps/mob/src", "compilerOptions": {"tsConfigPath": "apps/mob/tsconfig.app.json"}}, "common": {"type": "library", "root": "libs/common", "entryFile": "index", "sourceRoot": "libs/common/src", "compilerOptions": {"tsConfigPath": "libs/common/tsconfig.lib.json"}}, "validator": {"type": "library", "root": "libs/validator", "entryFile": "index", "sourceRoot": "libs/validator/src", "compilerOptions": {"tsConfigPath": "libs/validator/tsconfig.lib.json"}}}}