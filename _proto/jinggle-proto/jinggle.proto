syntax = "proto3";

package jinggle;

import "common-proto/common.proto";

service VelodivaService{
    rpc CreateTrack(stream CreateTrackRequest) returns (common.Empty){}
    rpc ListJinggles(common.Query) returns (ListJingglesResponse) {}
    rpc GetUrlJinggle(GetUrlJinggleRequest) returns (GetUrlJinggleRespose) {}
    rpc DeleteJingle(JingleDefaultRequest) returns (common.Empty){}
    rpc UpdateJingle(UpdateJingleRequest) returns (common.Empty){}
    rpc DetailJingle(JingleDefaultRequest) returns (TrackUserData){}
    rpc UpdateCurrentTrack(UpdateCurrentTrackRequest) returns (common.Empty){}
    rpc ListAllJingles(common.Query) returns (ListJingglesResponse){}
    rpc SuspendJingleTrack(common.Id) returns (common.Empty){}
    rpc UnsuspendJingleTrack(common.Id) returns (common.Empty){}
    rpc GetTemporaryJingleTrack(common.Id) returns (GetTemporaryJingleTrackResponse){}

    rpc CreateJinglePlaylist(CreateJinglePlaylistRequest) returns (common.Empty){}
    rpc ListJinglePlaylist(common.Query) returns (ListJinglePlaylistResponse){}
    rpc DeleteJinglePlaylist (JingleDefaultRequest) returns (common.Empty){}
    rpc UpdateJinglePlaylist (UpdateJinglePlaylistRequest) returns (common.Empty){}
    rpc DetailJinglePlaylist (JingleDefaultRequest) returns (DetailJinglePlaylistResponse) {}
    rpc UpdateSequencePlaylistTrack (UpdateSequencePlaylistTrackRequest) returns (common.Empty){}

    rpc CreateJingleZone(CreateJingleZoneRequest) returns(common.Empty) {}
    rpc ListJingleZone(common.Query) returns (ListJingleZoneResponse){}
    rpc DeleteJingleZone(JingleDefaultRequest) returns (common.Empty){}
    rpc UpdateJingleZone(UpdateJingleZoneRequest) returns (common.Empty){}
    rpc DetailJingleZone(JingleDefaultRequest) returns (DetailJingleZoneResponse){}

    rpc CreateJingleSchedule(CreateJingleScheduleRequest) returns(CreateJingleScheduleResponse){}
    rpc ListJingleSchedule(common.Query) returns(ListJingleScheduleResponse){}
    rpc DeleteJingleSchedule (common.Id) returns (DeleteJingleScheduleResponse){}
    rpc UpdateJingleSchedule (UpdateJingleScheduleRequest) returns (UpdateJingleScheduleResponse){}
    rpc DetailJingleSchedule(JingleDefaultRequest) returns (DetailJingleScheduleResponse){}
    rpc UpdateScheduleTime (UpdateScheduleTimeRequest) returns(common.Empty){}
    rpc UpdateJingleScheduleZoneTime (UpdateJingleScheduleZoneTimeRequest) returns(common.Empty){}

    rpc IsScheduleAvailable (IsScheduleAvailableRequest) returns (IsScheduleAvailableResponse){}
    rpc GetJingleUrlTrackRatio (GetJingleUrlTrackRatioRequest) returns (GetUrlJinggleRespose){}

    rpc GetReportSumamry (common.Id) returns (GetReportSumamryResponse){}
    rpc ListJingleReports (common.Query) returns (ListJingleReportsResponse){}
    rpc ListJingleScheduleReports (common.Query) returns (ListJingleScheduleResponse){}
    rpc ListJingleZoneReports (common.Query) returns (ListJingleZoneResponse) {}
    rpc ListJingleTrackReports (common.Query) returns (ListJingglesResponse){}
    rpc ListJinglePlaylistReports (common.Query) returns (ListJinglePlaylistReportsResponse) {}
    
    rpc GetJingleQueue(GetQueueRequest) returns (GetQueueResponse){}
    rpc GetCurrentTrackJingle (JingleDefaultRequest) returns (GetCurrentTrackJingleResponse){}
}

message GetTemporaryJingleTrackData {
    string id= 1;
    string title =2;
    string artist = 3;
}

message GetTemporaryJingleTrackResponse {
    repeated GetTemporaryJingleTrackData data =1;
}


message GetCurrentTrackJingleResponse {
    string id=1;
    string name =2;
}

message GetUrlJinggleRequest {
    string playlistId =1;
    string jingleId = 2;
    string zoneId = 3;
    string scheduleId = 4;
}

message UpdateSequencePlaylistTrackRequest {
    string playlistId =1;
    string trackId =2;
    string propertyId =3;
    int32 sequence =4;
}

message ZoneTimeData {
    string zoneId = 1;
    string timeZone = 2;
    bool isPlayed=3;
}
  
message ZoneGroupWithZoneTimes {
    string zoneGroupId = 1;
    repeated ZoneTimeData zoneTimes = 2;
}

message UpdateJingleScheduleZoneTimeRequest {
    string scheduleId = 1;
    repeated ZoneTimeData zones = 2;
}
  

message CreateJingleScheduleResponse {
    repeated string zoneIds =1;
    repeated string trackIds =2;
    bool isPlaynow =3;
}

message UpdateCurrentTrackRequest {
    string zoneId =1;
    string trackId =2;
    int32 duration =3;
    string propertyId =4;
}

message zoneUpdateData {
    string zoneId=1;
    bool isPLayed=2;
}

message ZoneGroupWithZones {
    string zoneGroupId = 1;
    repeated zoneUpdateData data = 2;
}
  
message UpdateJingleScheduleResponse {
    repeated ZoneGroupWithZones zoneData = 1;
    string scheduleId = 2;
}
  
message DeleteJingleScheduleResponse {
    repeated string zoneIds=1;
}

message GetQueueRequest {
    string timezone = 1;
    string id = 2;
}

message GetQueueResponse {
    repeated PlaylistData playlistData = 1;
    bool isPlaynow =2;
    int32 ratio =3;
    int64 historyJingle = 4;
    int64 historyJingleUpdate =5;
    bool isLock =6;
    bool isCrossfade =7;
    int32 jingleRemain =8;
    bool isPLayed =9;
}

message UpdateScheduleTimeRequest {
    string timezone =1;
    string id =2;
}

message DetailJingleScheduleResponse {
    ScheduleData scheduleData = 1;
    repeated PlaylistData playlistData = 2;
    repeated ZoneData zoneData =3; 
}

message DetailJingleZoneResponse {
    repeated string zoneIds = 1;
    string propertyId =2;
    string name =3;
    string description =4;
}

message DetailJinglePlaylistResponse {
    PlaylistData playlistData = 1;
    repeated TrackUserData trackData = 2;
}

message UpdateJingleScheduleRequest {
    CreateJingleScheduleRequest data =1;
    JingleDefaultRequest jingleDefaultRequest = 2;
}

message UpdateJingleZoneRequest {
    CreateJingleZoneRequest data =1;
    JingleDefaultRequest jingleDefaultRequest = 2;
}

message UpdateJinglePlaylistRequest {
    CreateJinglePlaylistRequest data =1;
    JingleDefaultRequest jingleDefaultRequest = 2;
}

message UpdateJingleRequest {
    TrackUserData userData =1;
    JingleDefaultRequest jingleDefaultRequest = 2;
}

message JingleDefaultRequest {
    string id =1;
    string propertyId =2;
}

message ListJinglePlaylistReportsResponse {
    HistoryPlaylistData data = 1;
    common.Meta meta =2;
}

message HistoryPlaylistData {
    string name =1;
    string duration =2;
    string type =3;
    string source =4;
    string playTime =5;
    string playCount =6;
    string LastPlayedDate =7;  
    SizeInfo sizeInfo =8;
}

message IsScheduleAvailableRequest {
    int64 createdAtHistorySong = 1;
    string id =2;
    string timezone =3;
    bool skipable = 4;
    int64 createdAtCurrentPlay = 5;
}

message JingleReportData {
    string playlistName = 1;
    int32 assignTo = 2;
    int32 schedule =3;
    int32 playCount =4;
    int32 playTime =5;     
    string historyPlaylistId =6; 
}

message ListJingleReportsResponse {
    repeated JingleReportData data = 1;
    common.Meta meta =2;
}

message GetReportSumamryResponse {
    int32 totalPlayCount = 1;
    int32 totalPlayTime = 2;
    int32 totalTargetDevice =3;

    int32 yesterdayTotalPlayCount = 4;
    int32 yesterdayTotalPlayTime = 5;

}

message GetJingleUrlTrackRatioRequest {
    repeated string playlistId = 1;
    string scheduleId = 2;
    string zoneId = 3;
    bool skipable = 4;
    bool isPlayed =6;
}

message IsScheduleAvailableResponse{
    bool isAvailable =1;   
    repeated string playlistId =2;
    string scheduleId =3;  
    int32 ratio =4; 
    int64 lastPlaylistHistoryCreatedAt = 5;
    int64 lastPlaylistHistoryUpdatedAt = 6;
    bool isPlayed =7;
    int64 scheduleUpdatedAt = 8;
    bool isPlaynow =9;
    int32 remain =10;
}

message ListJingleScheduleResponse {
    repeated ScheduleData data = 1;
    common.Meta meta =2;
}

message ScheduleData {
    string name =1;
    string mode =2;
    repeated string modeValue =3;
    string startTime =4;
    string endTime =5;
    int64 expiredAt = 6;
    int32 ratio =7;
    bool isLockMode =8;
    bool isCrossFade =9;
    string scheduleId =10; 
    repeated string groupDeviceName = 11;
    repeated string playlistName= 12;
    bool isPlaynow = 13;
}

message CreateJingleScheduleRequest {
    repeated string playlistIds =1;
    repeated string groupDeviceIds =2;
    string name =3;
    string mode =4;
    repeated string modeValue =5;
    string startTime =6;
    string endTime =7;
    int64 expiredAt = 8;
    int32 ratio =9;
    bool isPlaynow =10;
    bool isLockMode =11;
    bool isCrossFade =12;
    string propertyId = 13;
    string timeZone = 14;
}

message zoneRequest {
    string id =1;
    string deviceName =2;
    string serialNumber =3;
    string name =4;
}

message CreateJingleZoneRequest {
    repeated zoneRequest zoneData = 1;
    string propertyId =2;
    string name =3;
    string description =4;
}

message ListJingleZoneResponse {
    repeated ZoneData data =1; 
    common.Meta meta =2;
}

message PlaylistData {
    string name =1;
    int32 totalTrack =2;
    string description =3;
    string playlistId = 4;
}

message ZoneData {
    string name =1;
    int32 totalZone =2;
    string description =3;
    string groupZoneId =4;
    string deviceId =5;
    string type =6;
}

message ListJinglePlaylistResponse {
    repeated PlaylistData data =1;
    common.Meta meta =2;
}

message CreateJinglePlaylistRequest {
    repeated string ids =1;
    string name =2;
    string description =3;
}

message GetUrlJinggleRespose {
    string url =1;
    string artistName =2;
    string trackName = 3;
    int32 duration =4; 
    bool isLockMode =5;
    bool isCrossFade =6;
    string trackId = 7;
    bool isOnSchedule =8;
    repeated string zoneIds =9;
    int32 startAt = 10;
    string category =11;
    string status =12;
    string playlistId = 13;
    string playlistDesc =14;
}

message ListJingglesResponse {
    repeated TrackUserData data =1;
    common.Meta meta = 2;
}

message CreateTrackFile {
    bytes chunk = 1;
    string fileName = 2;
}

message TrackUserData {
    string title =1;
    string artist =2;
    string propertyId =3;
    string propertyName =4;
    string type =5;
    string category =6;
    string source =7;
    int64 expiredAt = 8;
    string trackId = 9;
    string cid = 10;
    string originalName =11;
    int32 duration =12;
    string deviceName =13;
    string serialNumber =14;
    string createdAt =15;
    SizeInfo sizeInfo =16;
    int32 sequence =17;
    string status =18;
}

message SizeInfo {
    int32 size =1;
    string type =2;
}

message CreateTrackRequest {
    CreateTrackFile file = 1;
    TrackUserData data =2;
}