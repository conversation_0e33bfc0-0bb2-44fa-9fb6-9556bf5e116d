syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/sku.proto";

service variantService {
  rpc DetailVariant(common.Id) returns (Variant) {}
}

message Variant {
  string id = 1;
  string name = 2;
  double price = 3;
  string description = 4;
  string productId = 5;
  Sku sku = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool publish = 9;
  DetailProduct product = 10;
}

message DetailProduct {
  string id = 1;
  string name = 2;
  string description = 3;
  string propertyTypeId = 4;
  string subfolderId = 5;
  repeated TaxProduct taxes = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool isMultiple = 9;
}

message TaxProduct {
  string id = 1;
  string name = 2;
  double nominal = 3;
  string type = 4;
  string startDate = 5;
  string endDate = 6;
}

message ProductVariant{
  string id = 1;
  string name = 2;
  double price = 3;
  string description = 4;
  string productId = 5;
  Sku sku = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool publish = 9;
}