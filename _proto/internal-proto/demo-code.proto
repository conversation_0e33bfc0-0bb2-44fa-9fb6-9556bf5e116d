syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "membership-proto/property.proto";

service DemoCodeService {
  rpc UseDemoCode(DemoCodeRequest) returns (common.Status) {}
  rpc ListDemoCodeProperties(ListDemoCodePropertiesRequest) returns (ListDemoCodePropertiesResponse){}
  rpc ValidateDemoCode(DemoCodeRequest) returns (common.Status) {}

}

message DemoCodeRequest {
  string code = 1;
  string propertyId = 2;
}

message ListDemoCodePropertiesRequest {
  string demoCodeId = 1;
  common.Query query =2;
}


message DemoCodeProperties {
  string code = 1;
  repeated membership.Property properties = 2; 
}

message ListDemoCodePropertiesResponse {
  repeated DemoCodeProperties data = 1;
  common.Meta meta = 2;
}