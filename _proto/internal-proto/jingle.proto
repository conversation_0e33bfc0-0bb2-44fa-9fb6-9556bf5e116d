syntax = "proto3";

package internal;

import "common-proto/common.proto";

service JingleService {
    rpc GetJingleScheduler(GetJingleSchedulerRequest) returns (common.Empty) {}
    rpc TrackJingleStatus(TrackJingleStatusRequest) returns (common.Empty){}
}


message GetJingleSchedulerRequest {
    repeated string trackIds = 1;
    string zoneId =2;
    string playlistId = 3;
    bool isLockMode =4;
    bool isCrossfade = 5;
    bool isPlaynow =6;
    string scheduleId = 7;
}

message TrackJingleStatusRequest{
    string title =1;
    string trackId=2;
    string status=3;
    string propertyId=4;
}