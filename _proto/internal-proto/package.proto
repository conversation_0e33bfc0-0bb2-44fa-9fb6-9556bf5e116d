syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/plan.proto";
import "membership-proto/property.proto";

service PackageService {
  rpc DetailPackage(common.Query) returns (Package) {}
  rpc ListPackage(common.Query) returns (ListPackageResponse) {}
  rpc GetTotalActivePackage(GetTotalActivePackageRequest) returns (GetTotalActivePackageResponse) {}
  rpc ListExpiredPackage(GetTotalActivePackageRequest) returns (ListExpiredPackageResponse) {}
  rpc GetOnePackage(RequestOnePackage) returns (Package) {}
  rpc CreateTemporaryLicensePackage(CreateTemporaryLicenseRequest) returns (common.Status) {}
  rpc CreateConventionalLicensePackage(CreateTemporaryLicenseRequest) returns (common.Status) {}
  rpc SetPackageLicenseTemporaryExpiration(SetPackageLicenseTemporaryExpirationRequest) returns (common.Status) {}
}

message FeatureSmall {
  string id = 1;
  string name = 2;
  string valueType = 3;
  int64 qty = 4;
  int64 quota = 5;
}

message RequestOnePackage {
  string id = 1;
  string orderId = 2;
}

message PackageFeature {
  string id = 1;
  Feature feature = 2;
  double price = 3;
  int64 qty = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message Package {
  string id = 1;
  string name = 2;
  string status = 3;
  string itemType = 4;
  string itemId = 5;
  bool isActive = 6;
  int64 activeAt = 7;
  int64 expiredAt = 8;
  int64 contractEndAt = 9;
  int64 trialEndAt = 10;
  bool isTrial = 11;
  string createdAt = 12;
  string updatedAt = 13;
  string propertyId = 14;
  string billingCycle = 15;
  repeated FeatureSmall packageFeature = 16;
}

message ListPackageResponse {
  common.Status status = 1;
  common.Meta meta = 2;
  repeated Package data = 3;
}

message GetTotalActivePackageRequest{
  repeated string Ids = 1;
}

message GetTotalActivePackageResponse{
  int32 total =1;
}

message CreateTemporaryLicenseRequest{
  string id = 1;
  string code = 2;
  int64 unit = 3;
  string requestId = 4;
}

message PropertyWithPackages {
  membership.Property property = 1;
  repeated Package packages = 2;
}

message SetPackageLicenseTemporaryExpirationRequest{
  string propertyId = 1;
  int64 expiredAt = 2;
  string licenseType = 3;
}

message ListExpiredPackageResponse {
  repeated PropertyWithPackages data = 1;
}
