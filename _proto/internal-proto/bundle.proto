syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/plan.proto";
import "internal-proto/property-type.proto";
import "internal-proto/license.proto";

service BundleService {
  rpc ListBundle(BundleRequest) returns (ListBundleResponse) {}
  rpc GetBundle(common.Id) returns (Bundle) {}
}

message Bundle {
  string id = 1;
  string name = 2;
  string description = 3;
  Plan plan = 5;
  License license = 6;
  PropertyType propertyType = 7;
  PropertyType subfolder = 8;
  string createdAt = 9;
  string updatedAt = 10;
}


message BundleRequest {
  string industryId = 1;
  string subfolderId = 2;
}

message ListBundleResponse {
  common.Status status = 1;
  repeated Bundle data = 2;
}
