syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/property-type.proto";
import "internal-proto/sku.proto";

service LicenseService {
  rpc GetLicense(common.Id) returns (License) {}
  rpc ListLicense(LicenseRequest) returns (ListLicenseResponse) {}
  rpc LicenseByPropertyType(LicenseRequest) returns (ListLicenseResponse) {}
  rpc SetPerformingLicense(SetLicenseRequest) returns (common.Status) {}
} 

message License {
  string id = 1;
  string name = 2;
  string description = 3;
  double price = 4; 
  repeated TaxLicense taxLicense = 5;
  string paymentStatus = 6;
  string paymentFrequency = 7;
  PropertyType propertyType = 8;
  Sku sku = 9;
}

message TaxLicense {
  string id = 1;
  string taxId = 2;
  string licenseId = 3;
  string name = 4;
  string description = 5;
  string type = 6;
  double nominal = 7;
  string startDate = 8;
  string endDate = 9;
}

message LicenseRequest {
    string propertyTypeId = 1;
} 

message LicenseList {
  repeated License license = 1;
}

message ListLicenseResponse {
  common.Status status = 1;
  map<string, LicenseList> data = 2;
}

message SetLicenseRequest {
  string id = 1;
  string key = 2;
  string type = 3;
}