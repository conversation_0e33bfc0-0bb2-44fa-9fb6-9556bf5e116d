syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/sku.proto";
import "internal-proto/plan.proto";
import "internal-proto/product-variant.proto";

service ProductService {
  rpc ListProduct(ProductRequest) returns (ListProductResponse) {}
  rpc DetailProduct(common.Id) returns (Product) {}
}

message Product {
  string id = 1;
  string name = 2;
  double price = 3;
  string description = 4;
  bool publish = 5;
  string propertyTypeId = 6;
  string subfolderId = 7;
  repeated Tax taxes = 8;
  Sku sku = 9 ;
  string createdAt = 10;
  string updatedAt = 11;
  bool isMultiple = 12;
  string image = 13;
}

message ProductRequest {
  optional string industryId = 1;
  optional string subfolderId = 2;
  optional string businessType = 3;
}

message ListProductResponse {
  common.Status status = 1;
  repeated Products data = 2;
}

message Products {
  string id = 1;
  string name = 2;
  double price = 3;
  string description = 4;
  bool publish = 5;
  string propertyTypeId = 6;
  string subfolderId = 7;
  repeated Tax taxes = 8;
  Sku sku = 9 ;
  repeated ProductVariant variants = 10;
  string createdAt = 11;
  string updatedAt = 12;
  bool isMultiple = 13;
  string image = 14;
}

