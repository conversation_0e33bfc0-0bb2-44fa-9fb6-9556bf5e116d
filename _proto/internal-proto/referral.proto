syntax = "proto3";

package internal;

import "internal-proto/partnership.proto";

service ReferralService {
  rpc UseReferral(ReferralCodeRequest) returns (UseReferralResponse) {}
  rpc UpdateStatusReferral(UpdateReferralRequest) returns (UseReferralResponse) {}
}

message Referral {
  string id = 1;
  string name = 2;
  Partnership partnership = 3;
  string code = 4;
  string discountType = 5;
  string discountValue = 6;
  string startDate = 7;
  string endDate = 8;
}
message ReferralCodeRequest {
  string code = 1;
  string orderId = 2;
}

message UseReferralResponse {
  string referralId = 1;
  string itemId = 2;
  string itemType = 3;
  double totalPriceBefore = 4;
  double discountPrice = 5;
  double totalPriceAfter = 6;
}

message UpdateReferralRequest {
  string orderId = 1;
}