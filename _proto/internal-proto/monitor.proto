syntax = "proto3";

package internal;

import "common-proto/common.proto";

message MusicPlayerOverview {
    string webCount = 1;
    string atvCount = 2;
    string androidCount = 3;
    string iosCount = 4;
}

message PlayHistoryOverview {
    string totalCount = 1;
    string countryCount = 2;
    string songCount = 3;
}

message DeviceOverview {
    string activeCount = 1;
    string inactiveCount = 2;
    string total = 3;
}

message ISRCOverview {
    repeated ISRCOverviewList data = 1;
}

message ISRCOverviewRequest {
    repeated string isrc = 1;
}

message ISRCOverviewList {
    string isrc = 1;
    string count = 2;
}

message SongLibraryOverview {
    string songCount = 1;
    string countryCount = 2;
    string artistCount = 3;
}

message DeviceSummary {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string deviceType = 4;
  string deviceId = 5;
  string songTitle = 6;
  string artist = 7;
  string duration = 8;
  string playedTime = 9;
  string uptime = 10;
  string userName = 11;
  string password = 12;
  string email = 13;
  string statusAccount = 14;
}


message DeviceSummaryListResponse {
  repeated DeviceSummary data = 1;
  common.Meta meta = 2;
}

message PlayHistorySummary {
  string id = 1;
  string playedTime = 2;
  string deviceId = 3;
  string songTitle = 4;
  string artist = 5;
  string duration = 6;
  string album = 7;
  string deviceType = 8;
  string uptime = 9;
  string companyName = 10;
  string brandName = 11;
  string zoneName = 12;
}

message PlayHistorySummaryResponse {
    repeated PlayHistorySummary data = 1;
    common.Meta meta = 2;
}

message DeviceRegionOverview {
  string province = 1;
  string city = 2;
  string urban = 3;
  string district = 4;
  string code = 5;
  string activeCount = 6;
  string inactiveCount = 7;
  string suspendedCount = 8;
  string totalCount = 9;
}

message DeviceRegionOverviewResponse {
    repeated DeviceRegionOverview data = 1;
}

service MonitorService {
    rpc GetListDevice (common.QueryMap) returns (DeviceSummaryListResponse) {}
    rpc GetDetailDevice (common.Id) returns (DeviceSummary) {}
    rpc GetListPlayHistory (common.QueryMap) returns (PlayHistorySummaryResponse) {}
    rpc GetDetailPlayHistory (common.Id) returns (PlayHistorySummary) {}
    rpc GetMusicPlayerOverview(common.Empty) returns (MusicPlayerOverview) {}
    rpc GetPlayHistoryOverview(common.Empty) returns (PlayHistoryOverview) {}
    rpc GetDeviceRegionOverview(common.Empty) returns (DeviceRegionOverviewResponse) {}
    rpc GetDeviceOverview(common.Empty) returns (DeviceOverview) {}
    rpc GetIsrcOverview(ISRCOverviewRequest) returns (ISRCOverview) {}
    rpc GetSongLibraryOverview(common.Empty) returns (SongLibraryOverview) {}
}