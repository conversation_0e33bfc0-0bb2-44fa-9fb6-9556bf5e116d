syntax = "proto3";

package internal;

import "common-proto/common.proto";

service OrderPaymentService {
  rpc OrderCheckout(common.Id) returns (CheckoutResp) {}
  rpc CancelOrder(CancelOrderReq) returns (common.Status) {}
}

message CheckoutResp {
  string checkoutUrl = 1;
  string paymentRequestId = 2;
  bool status = 3;
  string time = 4;
}

message CancelOrderReq {
  string paymentId = 1;
  string reason = 2;
}