syntax = "proto3";

package internal;

import "common-proto/common.proto";

service PostalService {
  rpc GetProvince (ProvinceRequest) returns (ListProvinceResponse) {}
  rpc GetCity (CityRequest) returns (ListCityResponse) {}
  rpc GetDistrict (DistrictRequest) returns (ListDistrictResponse) {}
  rpc GetUrban (UrbanRequest) returns (ListUrbanResponse) {}
  rpc GetZipCode (ZipCodeRequest) returns (ZipCodeResponse) {}
  rpc GetPostal (common.Id) returns (Postal) {}
  rpc GetPostalByIds (GetPostalByIdsRequest) returns (GetPostalByIdsResponse) {}
}

message GetPostalByIdsRequest{
  repeated string postalIds = 1;
}

message GetPostalByIdsResponse {
  repeated Postal data =1;
}

message ProvinceRequest {
  common.Query search = 1;
}

message CityRequest {
  string provinceId = 1;
}

message DistrictRequest {
  string provinceId = 1;
  string city = 2;
}

message UrbanRequest {
  string provinceId = 1;
  string city = 2;
  string district = 3;
}

message ZipCodeRequest {
  string provinceId = 1;
  string city = 2;
  string district = 3;
  string urban = 4;
}

message Postal {
  string id = 1;
  string urban = 2;
  string city = 3;
  string district = 4;
  string code = 5;
  string province = 6;
  string provinceId = 7;
}

message Province {
  string id = 1;
  string name = 2;
}

message PostalByCodeRequest {
  string code = 1;
}

message ListPostalResponse {
  common.Status status = 1;
  repeated Postal data = 2;
}

message ListProvinceResponse {
  common.Status status = 1;
  repeated Province data = 2;
}

message ListCityResponse {
  common.Status status = 1;
  repeated string data = 2; 
}

message ListDistrictResponse {
  common.Status status = 1;
  repeated string data = 2; 
}

message ListUrbanResponse {
  common.Status status = 1;
  repeated string data = 2; 
}

message ZipCodeResponse {
  string id = 1;
  string code = 2;
}