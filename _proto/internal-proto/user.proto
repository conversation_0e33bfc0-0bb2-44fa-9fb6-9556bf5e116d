syntax = "proto3";  
  
package internal;  
  
import "common-proto/common.proto";  
  
service UserService {
    rpc CreateUser(CreateUserRequest) returns (common.Status) {}
    rpc UpdateUser(UpdateUserRequest) returns (common.Status) {}
    rpc UpdateUserPassword(UpdateUserPasswordRequest) returns (common.Status) {}
    rpc DeleteUser(common.Id) returns (common.Status) {}
    rpc GetUsersByPropertyId(GetUsersByPropertyRequest) returns (GetUsersByPropertyResponse) {}
    rpc GetUserById(common.Id) returns (GetUserByIdResponse) {}
}

message CreateUserRequest {
    string username = 1;
    string email = 2;
    string mobileNumber = 3;
    string password = 4;
    string businessType = 5;
    bool isAdmin = 6;
    string firstName = 7;
    string lastName = 8;
    string placeOfBirth = 9;
    string dateOfBirth = 10;
    string gender = 11;
    string address = 12;
    string activationId = 13;
    string propertyId = 14;
    string zoneName = 15;
}

message UpdateUserRequest {
    string userId = 1;
    string firstName = 2;
    string lastName = 3;
    string email = 4;
    string mobileNumber = 5;
    string gender = 6;
    string dateOfBirth = 7;
    string address = 8;
    string activationId = 9;
    string zoneName = 10;
}

message UpdateUserPasswordRequest {
    string userId = 1;
    string password = 2;
}

message GetUsersByPropertyRequest {
    repeated string propertyId = 1;
}

message GetUsersByPropertyResponse {
    repeated UserInfo users = 1;
}

message UserInfo {
    string id = 1;
    string username = 2;
    string email = 3;
    string mobileNumber = 4;
    string businessType = 5;
    bool isAdmin = 6;
    Profile profile = 7;
    bool isActive = 8;
    string status = 9;
    string activationId = 10;
    string zoneName = 11;
    string password = 12;
}

message Profile {
    string firstName = 1;
    string lastName = 2;
    string placeOfBirth = 3;
    string dateOfBirth = 4;
    string gender = 5;
    string address = 6;
}

message GetUserByIdResponse {
    UserInfo user = 1;
    common.Status status = 2;
}