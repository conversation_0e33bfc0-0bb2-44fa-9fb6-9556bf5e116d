syntax = "proto3";  
  
package internal;  
  
import "common-proto/common.proto";  
import "internal-proto/plan.proto";

service PropertyService {  
    rpc DeleteProperty(common.Id) returns (common.Status) {}
    rpc GetPropertyBill(common.Id) returns (GetPropertyBillResponse) {}
    rpc AssignProperty(AssignPropertyRequest) returns (common.Status) {}
    rpc GetPropertyZoneType(GetPropertyZoneTypeRequest) returns (GetPropertyZoneTypeResponse) {}
    rpc GetIsActivateAllUsed(GetIsActivateAllUsedRequest) returns (GetIsActivateAllUsedResponse){}
    rpc GetPropertyIn (GetPropertyInRequest) returns (PropertySummaryResponse) {}
}

message PropertySummaryResponse {
    repeated PropertySummary data = 1;
}

message PropertySummary {
  string id = 1;
  string industry = 2;
  string city = 3;
  string province = 4;
  string packageName = 5;
  string startDate = 6;      
  string endDate = 7;       
  string flag = 8;        
  string totalDevice = 9;
} 

message GetPropertyInRequest {
    repeated string id = 1;
}

message PropertyInfo {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brandName = 4;
  string companyEmail = 5;
  string companyPhoneNumber = 6;
  string address = 7;
  string province = 8;
  string city = 9;
  string district = 10;
  string urban = 11;
  string postalCode = 12;
  string postalId = 13;
  string licenseKey = 14;
  string licenseType = 15;
  string industry = 16;
  string flag = 17;
}

message ActivateAllUsedData {
    string propertyId = 1;
    bool allActivationUsed = 2;
} 

message GetIsActivateAllUsedResponse {
    repeated ActivateAllUsedData data = 1;
}

message GetIsActivateAllUsedRequest {
    repeated string id = 1;
}

message AssignPropertyRequest {
    string userId = 1;
    string propertyId = 2;
}

message GetPropertyZoneTypeData {
    string id = 1;
    string name = 2;
}
  
message GetPropertyZoneTypeResponse {
    repeated GetPropertyZoneTypeData data = 1;
}

message GetPropertyZoneTypeRequest{
    string id = 1;
    string search = 2;
}

message GetPropertyBillResponse {
    repeated GetPropertyBillPackageResponse packages = 1;
}

message GetPropertyBillPackageResponse {
   string packageId = 1;
   string type = 2;
   string name = 3;
   double price = 4;
   string status = 5;
   double total = 6;
   string billCycle = 7;
   string renewDue = 8;
   string orderId = 9;
   repeated Tax taxes = 10;
   string renewType = 11;
   string sku = 12;
   repeated Feature features = 15;
   int32 qty = 16;
   string itemId = 17;
}