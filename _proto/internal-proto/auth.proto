syntax = "proto3";

package internal;

service AuthService {
  rpc GenerateUserToken(GenerateUserTokenReq) returns (GenerateUserTokenResp) {}
  rpc GenerateUserTokenByDeviceId(GenerateUserTokenByDeviceIDReq) returns (GenerateUserTokenResp) {}
}

message GenerateUserTokenResp {
  string accessToken = 1;
  string refreshToken = 2;
}

message GenerateUserTokenReq {
  string userId = 1;
  string propertyId = 2;
  string pass = 3;
  string ip = 4;
  string agent = 5;
}

message GenerateUserTokenByDeviceIDReq {
  string deviceId = 2;
  string pass = 3;
  string ip = 4;
  string agent = 5;
}