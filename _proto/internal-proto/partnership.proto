syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "membership-proto/property.proto";

service PartnershipService {
  rpc ListPartnership(common.Query) returns (ListPartnershipResponse) {}
  rpc GetPartnership(common.Id) returns (Partnership) {}
  rpc ListPartnershipProperties (ListPartnershipPropertiesRequest) returns (ListPartnershipPropertiesResponse){}
}

message Partnership {
  string id = 1;
  string name = 2;
  string popularName = 3;
  string crmUserId = 4;
  string propertyId = 5;
  string mediaId = 6;
  string postalId = 7;
}

message ListPartnershipResponse {
  repeated Partnership data = 1;
  common.Meta meta = 2;
}

message ListPartnershipPropertiesResponse {
  repeated membership.Property data = 1;
  common.Meta meta = 2;
}
message ListPartnershipPropertiesRequest {
  common.Query query =1;
  string partnershipId = 2; 
}