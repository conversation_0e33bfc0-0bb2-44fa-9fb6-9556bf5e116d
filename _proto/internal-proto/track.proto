syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/user.proto";
import "internal-proto/property.proto";

message Track {
    string id = 1;
    string title = 2;
    string country = 3;
    repeated string artists = 4;
    string album = 5;
    string label = 6;
    string release_date = 7;
    string isrc = 8;
    int32 duration = 9;
    string gmi_id = 10;
    string playCount = 11;
    repeated string images = 12;
}

message ListTrackResponse {
    repeated Track data = 1;
    common.Meta meta = 2;
}

message ActivationTrack {
    string id = 1;
    string code = 2;
    string zoneType = 3;
    UserInfo user = 4;
    PropertyInfo property = 5;
}

message TrackPlayHistory {
    string id = 1;
    Track track = 2;
    string playAt = 3;
    string endAt = 4;
    int32 duration = 5;
    string ip = 6;
    string os = 7;
    string country = 8;
    string userAgent = 9;
    ActivationTrack activation = 10;
}

message ListTrackPlayHistoryResponse {
    repeated TrackPlayHistory data = 1;
    common.Meta meta = 2;
}


message ActivationPlayHistory {
    string id = 1;
    string code = 2;
    string zoneType = 3;
    UserInfo user = 4;
    PropertyInfo property = 5;
    Track track = 6;
    string playCount = 7;
}

message ListActivationPlayHistoryResponse {
    repeated ActivationPlayHistory data = 1;
    common.Meta meta = 2;
}

service TrackService {
    rpc ListTrack(common.QueryMap) returns (ListTrackResponse) {}
    rpc DetailTrack(common.Id) returns (Track) {}
    rpc ListTrackPlayHistory(common.QueryMap) returns (ListTrackPlayHistoryResponse) {}
    rpc DetailTrackPlayHistory(common.Id) returns (TrackPlayHistory) {}
    rpc ListActivationPlayHistory(common.QueryMap) returns (ListActivationPlayHistoryResponse) {}
}