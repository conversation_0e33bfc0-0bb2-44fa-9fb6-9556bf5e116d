syntax = "proto3";

package internal;

import "common-proto/common.proto";

service PropertyTypeService {
  rpc ListPropertyTypes(common.Query) returns (ListPropertyTypesResponse) {}
  rpc GetPropertyType(common.Id) returns (PropertyType) {}
  rpc GetPropertyTypeIn(GetPropertyTypeInRequest) returns (GetPropertyTypeInResponse) {}
  rpc GetPropertySub(GetPropertySubRequest) returns (GetPropertySubResponse);
  rpc GetPropertySubLicense(GetPropertySubLicenseRequest) returns (GetPropertySubResponse);
}

message PropertyType {
  string id = 1;
  string name = 2;
  string slug = 3;
  string icon = 4;
  string categoryCode = 5;
  string codeProperty = 6;
  string createdAt = 7;
  string updatedAt = 8;
  string parentId = 9;
  string type = 10;
  string description = 11;
  bool isHidden = 12;
  bool visibility = 13;
  string mediaId = 14;
  bool hasChild = 15;
  string rangeLabel = 16;
  string unitLabel = 17;
  int32 sequence = 18;
  repeated PropertyType children = 19;
}

message ListPropertyTypesResponse {
  repeated PropertyType data = 1;
  common.Meta meta = 2;
}

message GetPropertyTypeInRequest {
  repeated string id = 1;
}

message GetPropertySubRequest {
  string id = 1;
  string businessType = 2;
}

message GetPropertyTypeInResponse {
  repeated PropertyType data = 1;
}

message GetPropertySubResponse {
  repeated PropertyType data = 1;
}

message GetPropertySubLicenseRequest {
  string id = 1;
  string code = 2;
}
