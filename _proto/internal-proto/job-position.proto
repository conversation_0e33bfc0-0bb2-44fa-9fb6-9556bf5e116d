syntax = "proto3";

package internal;

import "common-proto/common.proto";

service JobPositionService {
  rpc ListJobPosition(common.Empty) returns (ListJobPositionResponse) {}
  rpc DetailJobPosition(common.Id) returns (JobPosition) {}
}

message JobPosition {
  string id = 1;
  string name = 2;
  string description = 3;
  string createdAt = 4;
  string updatedAt = 5;
  optional string allowToCopyId = 6;
}

message ListJobPositionResponse {
  common.Status status = 1;
  repeated JobPosition data = 2;
}