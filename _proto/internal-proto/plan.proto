syntax = "proto3";

package internal;

import "common-proto/common.proto";
import "internal-proto/sku.proto";

service PlanService {
  rpc ListPlan(PlanRequest) returns (ListPlanResponse) {}
  rpc DetailPlan(common.Id) returns (Plan) {} 
}

enum PlanType {
  FIX = 0;
  ENTERPRISE = 1;
  CUSTOM = 2;
}

message Plan {
  string id = 1;
  string name = 2;
  double basePrice = 3;
  string description = 4;
  bool publish = 5;
  string duration = 6;
  PlanType type = 7;
  bool isActive = 8;
  string startDate = 9;
  string endDate = 10;
  string createdAt = 11;
  string updatedAt = 12;
  string propertyTypeId = 13;
  string subfolderId = 14;
  repeated Feature features = 15;
  repeated Tax taxes = 17;
  Sku sku = 18;
}

message Feature {
  string featureId = 1;
  int64 qty = 2;
  FeatureDetail feature = 3;
}

message FeatureDetail {
  string id = 1;
  string name = 2;
}

message Tax {
  string id = 1;
  string name = 2;
  double nominal = 3;
  string description = 4;
  string type = 5;
  string startDate = 6;
  string endDate = 7;
}

message Addon { 
  string feature = 1; 
  int64 value = 2;  
  string valueType = 3; 
  int32 order = 4;
  double price = 5;
}

message PlanRequest {
  optional string industryId = 1;
  optional string subfolderId = 2;
  optional string businessType = 3;
}

message PlanList {
  repeated Plan plans = 1;
}

message ListPlanResponse {
  common.Status status = 1;
  repeated Plan data = 2;
}