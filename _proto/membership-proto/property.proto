syntax = "proto3";

package membership;

import "common-proto/common.proto";
import "membership-proto/auth.proto";

service PropertyService {
  rpc GetPropertyUser(PropertyUserRequest) returns (PropertyUserResponse) {}
  rpc ActivateProperty(PropertyUserRequest) returns (common.Status) {}
  rpc SuspendProperty(PropertyUserRequest) returns (common.Status) {}
  rpc GetPropertyByCid(PropertyCidRequest) returns (PropertyUserResponse) {}
  rpc SetPropertyFlag(SetPropertyFlagRequest) returns (common.Status) {}
  rpc GetPropertyById(common.Id) returns (Property) {}
  rpc GetPropertyOrder(common.Id) returns (ListOrderResponse) {}
  rpc GetOverviewProperty(common.Empty) returns (GetOverviewPropertyResponse){}
  rpc ListProperty(common.QueryMap) returns (ListPropertyResponse) {}
  rpc DetailProperty(common.Id) returns (PropertyInfo) {}
  rpc GetListOwnerProperty(common.Query) returns (GetListOwnerPropertyResponse) {}
  rpc GetOwnerProperty(common.Query) returns (GetOwnerPropertyResponse) {}
}

message PropertyInfo {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brandName = 4;
  string companyEmail = 5;
  string companyPhoneNumber = 6;
  string address = 7;
  string province = 8;
  string city = 9;
  string district = 10;
  string urban = 11;
  string postalCode = 12;
  string postalId = 13;
  string licenseKey = 14;
  string licenseType = 15;
  string industry = 16;
  string flag = 17;
  OwnerInfo owner = 18;
  repeated UserInfo users = 19;
}

message OwnerInfo {
    string id = 1;
    string username = 2;
    string email = 3;
    string mobileNumber = 4;
    string businessType = 5;
    Profile profile = 6;
    bool isActive = 7;
}

message UserInfo {
    string id = 1;
    string username = 2;
    string email = 3;
    string mobileNumber = 4;
    string businessType = 5;
    bool isAdmin = 6;
    Profile profile = 7;
    bool isActive = 8;
    string status = 9;
    string activationId = 10;
    string zoneName = 11;
}

message ListPropertyResponse {
  repeated PropertyInfo data = 1;
  common.Meta meta = 2; 
}

message PropertyMonitorOverview {
  int32 totalUserOwner = 1;
  int32 totalProperty = 2;
  int32 percentageUsedPromoCode = 3;
}

message GetOverviewPropertyResponse {
  PropertyMonitorOverview data = 1;
}

message GetAllPropertyRequest {
  common.Query query = 1;
}

message PropertyMonitorUser {
  string id = 1;
  string name = 2;
}

message PropertyMonitorPostal {
  string id = 1;
  string city = 2;
  string address = 3;
}

message PropertyMonitor {
  string crmPropertyId = 1;
  PropertyMonitorUser groupAdmin = 2;
  repeated PropertyMonitorUser userAdmin = 3;
  PropertyMonitorPostal postal = 4;
  string propertyType = 5;
}

message GetAllPropertyResponse {
  repeated PropertyMonitor data = 1;
  common.Meta meta = 2;
}

message PropertyUserProfile {
  string id = 1;
  string firstName = 2;
  string lastName = 3;
  string placeOfBirth = 4;
  string dateOfBirth = 5;
  string address = 6;
  string gender = 7;
  string createdAt = 8;
  string updatedAt = 9;
}

message PropertyUser {
  string id = 1;
  string email = 2;
  string username = 3;
  string mobileNumber = 4;
  string password = 5;
  string businessType = 6;
  PropertyUserProfile profile = 7;
  bool is_admin = 8;
  string zone = 9;
}

message ContactPerson {
  string id = 1;
  string firstName = 2;
  string lastName = 3;
  string email = 4;
  string phone = 5;
  string jobPositionId = 6;
  string createdAt = 7;
  string updatedAt = 8;
}

message Property {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brandName = 4;
  string companyEmail = 5;
  string companyPhoneNumber = 6;
  string npwp = 7;
  string address = 8;
  string createdAt = 9;
  string updatedAt = 10;
  string postalId = 11;
  string propertyTypeId = 12;
  ConfigurationOptions configuration = 13;
  repeated ContactPerson contactPerson = 14;
  string categoryId = 15;
  string industryPlan = 16;
  int64 unit = 17;
  string licenseKey = 18;
  string licenseType = 19;
  string requestId = 20;
  repeated OrderProp order = 21;
}

message OrderProp {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string tag = 5;
  string createdAt = 6;
  string updatedAt = 7;
  string propertyId = 8;
  double totalPrice = 9;
  double discount = 10;
  string voucherId = 11;
  OrderPaymentProp orderPayment = 12;
  repeated OrderDetailProp details = 13;
  string propertyType = 14;
  string userId = 15;
}

message OrderPaymentProp {
  string id = 1;
  string by = 2;
  string url = 3;
  int64 expiredPayment = 4;
  string status = 5;
  string paymentRequestId = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool isPaid = 9;
}

message OrderDetailProp {
  string id = 1;
  string name = 2;
  string duration = 3;
  string price = 4;
  double totalPrice = 5;
  double tax = 6;
  double discount = 7;
  string itemType = 8;
  string itemId = 9;
  int64 qty = 10;
  string createdAt = 11;
  string updatedAt = 12;
  string sku = 13;
}

message PropertyUserRequest {
  string propertyId = 1;
}

message PropertyCidRequest {
  string cid = 1;
}

message UserProperty {
  string id = 1;
  string userId = 2;
  string propertyId = 3;
  bool isDefault = 4;
  string createdAt = 5;
  string updatedAt = 6;
}

message ConfigurationOptions {
  string industryLicense = 1;
  string industryPlan = 2;
}

message PropertyUserResponse {
  Property property = 1;
  PropertyUser user = 2;
  UserProperty userProperty = 3;
}

message PropertyTypeSmall {
  string id = 1;
  string name = 2;
  string slug = 3;
  string icon = 4;
  string categoryCode = 5;
  string codeProperty = 6;
  string type = 7;
}

message PropertyWithType {
  string id = 1;
  string cid = 2;
  string companyName = 3;
  string brandName = 4;
  string companyEmail = 5;
  string companyPhoneNumber = 6;
  string npwp = 7;
  string address = 8;
  string createdAt = 9;
  string updatedAt = 10;
  // PropertyTypeSmall propertyType = 11;
}

message SetPropertyFlagRequest {
  string id = 1;
  string flag = 2;
}

message ListOrderResponse {
  common.Status status = 1;
  common.Meta meta = 2;
  repeated ListAllOrderResponse data = 3;
}

message ListAllOrderResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string tag = 5;
  string createdAt = 6;
  string updatedAt = 7;
  double totalPrice = 9;
  double discount = 10;
  string voucherId = 11;
  ListOrderPayment orderPayment = 12;
  repeated ListOrderDetail orderDetail = 13;
  PropertyWithType property = 14;
}

message ListOrderPayment {
  string id = 1;
  string by = 2;
  string url = 3;
  int64 expiredPayment = 4;
  string status = 5;
  string paymentRequestId = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool isPaid = 9;
}

message ListOrderDetail {
  string id = 1;
  string name = 2;
  string duration = 3;
  string price = 4;
  double totalPrice = 5;
  double tax = 6;
  double discount = 7;
  string itemType = 8;
  string itemId = 9;
  int64 qty = 10;
  string createdAt = 11;
  string updatedAt = 12;
  string sku = 13;
}

message OwnerProperty {
  string id = 1;
  string firstName = 2;
  string lastName = 3;
  string email = 4;
  string mobileNumber = 5;
  string dateOfBirth = 6;
  string gender = 7;
  string businessType = 8;
  repeated common.Id propertyIds = 9;
}

message GetListOwnerPropertyResponse {
  common.Status status = 1;
  common.Meta meta = 2;
  repeated OwnerProperty data = 3;
}

message GetOwnerPropertyResponse {
  common.Status status = 1;
  repeated PropertyWithType data = 2;
}