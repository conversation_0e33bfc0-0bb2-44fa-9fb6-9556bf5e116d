syntax = "proto3";

package membership;

import "common-proto/common.proto";
import "membership-proto/property.proto";

service UserService {
  rpc GetUserById (common.Id) returns (UserResponse) {}
  rpc GetUserByIdPropertyId (UserWithPropertyRequest) returns (UserResponse) {}
}

message UserWithPropertyRequest {
  string userId = 1;
  string propertyId = 2;
}

message UserData {
  string id = 1;
  string username = 2;
  string email = 3;
  string password = 4;
  string provider = 5;
  string mobileNumber = 6;
  string business_type = 7;
  string type = 8;
  string status = 9;
  bool is_active = 10;
  string refferal_code = 11;
  string parent_id = 12;
  ProfileData profile = 13;
  bool is_admin = 14;
  string zone = 15;
}

message ProfileData {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  string placeOfBirth = 4;
  string dateOfBirth = 5;
  string gender = 6;
  string address = 7;
  string mediaId = 8;
}

message UserResponse {
  UserData user = 1;
  Property property = 2;
  UserProperty userProperty = 3;
}