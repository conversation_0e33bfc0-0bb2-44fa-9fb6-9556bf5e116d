syntax = "proto3";

package membership;

import "common-proto/common.proto";
import "membership-proto/property.proto";

service OrderService {
  rpc CheckPayment(CheckPaymentRequest) returns (Order) {}
  rpc GetOrderDetailByProperty(GetOrderDetailByPropertyRequest) returns (Orders){}
  rpc GetOrderDetail(common.Id) returns (Order) {}
  rpc PaidOrder(PaidOrderRequest) returns (common.Status) {}
  rpc UpdateWaitingOrder(UpdateWaitingOrderRequest) returns (common.Status) {}
  rpc RenewOrder(RenewOrderRequest) returns (common.Status) {}
  rpc GetListAllOrder(common.Query) returns (GetListAllOrderResponse) {} // for internal admin
  rpc GetDetailAllOrder(common.Id) returns (GetAllOrderResponse) {} // for internal admin
  rpc TrialOrder(TrialOrderRequest) returns (OrderTrial) {} //for internal admin
  rpc GetActivePropertyByOrderStatusCounter(GetActivePropertyByOrderStatusCounterRequest) returns (GetActivePropertyByOrderStatusCounterResponse){}
}

enum RenewalType {
  SUBSCRIPTION = 0;
  CONTRACT = 1;
}

enum OrderStatus {
  pending = 0;
  completed = 1;
  cancelled = 2;
}

message OrderPayment {
  string id = 1;
  string by = 2;
  string url = 3;
  int64 expiredPayment = 4;
  string status = 5;
  string paymentRequestId = 6;
  string createdAt = 7;
  string updatedAt = 8;
  bool isPaid = 9;
}

message OrderPackageDetail {
  string id = 1;
  string featureId = 2;
  int64 qty = 3;
}

message OrderDetail {
  string id = 1;
  string name = 2;
  string duration = 3;
  string price = 4;
  double totalPrice = 5;
  double tax = 6;
  double discount = 7;
  string itemType = 8;
  string itemId = 9;
  int64 qty = 10;
  string createdAt = 11;
  string updatedAt = 12;
  string sku = 13;
  repeated OrderPackageDetail orderPackageDetail = 14;
}

message Order {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string tag = 5;
  string createdAt = 6;
  string updatedAt = 7;
  string propertyId = 8;
  double totalPrice = 9;
  double discount = 10;
  string voucherId = 11;
  OrderPayment orderPayment = 12;
  repeated OrderDetail orderDetail = 13;
  string propertyType = 14;
  string userId = 15;
}

message Orders{
  repeated Order order = 1;
}


message OrderTrial {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string tag = 5;
  string createdAt = 6;
  string updatedAt = 7;
  string propertyId = 8;
  double totalPrice = 9;
  double discount = 10;
  string voucherId = 11;
  OrderPayment orderPayment = 12;
  repeated OrderDetail details = 13;
  string propertyType = 14;
}

message CheckPaymentRequest {
  string paymentRequestId = 1;
}

message PaidOrderRequest {
  string orderId = 1;
  string paymentRequestId = 2;
  string paymentMethod = 3;
  string paidAt = 4;
  string paymentId = 5;
}

message UpdateWaitingOrderRequest {
  string id = 1;
  bool isPaid = 2;
  string paymentRequestId = 3;
  string url = 4;
  string expired = 5;
}

message RenewOrderRequest {
  string propertyId = 1;
  string itemType = 2;
  string itemId = 3;
  RenewalType renewalType = 4;
}

message TrialOrderRequest {
  string propertyId = 1;
  string itemType = 2;
  string itemId = 3;
}

message GetAllOrderResponse {
  string id = 1;
  string name = 2;
  string description = 3;
  string status = 4;
  string tag = 5;
  string createdAt = 6;
  string updatedAt = 7;
  double totalPrice = 9;
  double discount = 10;
  string voucherId = 11;
  OrderPayment orderPayment = 12;
  repeated OrderDetail orderDetail = 13;
  PropertyWithType property = 14;
  string industry = 15;
  Package package = 16;
}

message GetListAllOrderResponse {
  common.Status status = 1;
  common.Meta meta = 2;
  repeated GetAllOrderResponse data = 3;
}

message GetOrderDetailByPropertyRequest {
  string propertyId = 1;
}

message GetActivePropertyByOrderStatusCounterRequest{
  OrderStatus orderStatus = 1; 
}

message GetActivePropertyByOrderStatusCounterResponse{
  int32 counters = 1;
}


message Package {
  string id = 1;
  string name = 2;
  string status = 3;
  string itemType = 4;
  string itemId = 5;
  bool isActive = 6;
  string activeAt = 7;
  string expiredAt = 8;
  string contractEndAt = 9;
  string trialEndAt = 10;
  bool isTrial = 11;
  string createdAt = 12;
  string updatedAt = 13;
  string propertyId = 14;
}
