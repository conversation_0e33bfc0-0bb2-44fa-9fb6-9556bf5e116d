syntax = "proto3";

package common;

message Id {
    string id = 1;
}

message KeyValuePair {
    string key = 1;
    string value = 2;
}

message QueryMap {
    int32 page = 1;
    int32 limit = 2;
    repeated KeyValuePair filters = 3;
}

message Empty {}

message Query {
    string query = 1;
    string startDate = 2;
    string endDate = 3;
    map<string, string> params = 4;
}

message Status {
    int32   code    = 1;
    string  message = 2;
}

message Meta {
    int32 total       = 1;
    int32 lastPage    = 2;
    int32 currentPage = 3;
    int32 limit       = 4;
    int32 prev        = 5;
    int32 next        = 6;
}

message MetaCursor {
    string lastCursor = 1;
    bool hasNextPage = 2;
}