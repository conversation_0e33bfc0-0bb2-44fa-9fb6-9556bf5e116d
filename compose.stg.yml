version: '3.9'
services:
  admin:
    container_name: api-admin
    image: gitlab.vnt.co.id:5050/melodiva1/api/admin:latest
    restart: unless-stopped
    environment:
      - APP_ADMIN_SERVER=https://api-stg01.melodiva.co.id
      - APP_ADMIN_PORT=8000
      - DATABASE_URL=*************************************************************
      - AT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - RT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg 
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - CDN_URL=https://cdn.velodiva.com/
    ports:
      - '8000:8000'
    depends_on:
      - database
      - mainredis
    volumes:
      - melodiva:/app/storage
  commercial:
    container_name: api-commercial
    image: gitlab.vnt.co.id:5050/melodiva1/api/commercial:latest
    restart: unless-stopped
    environment:
      - APP_COMMERCIAL_SERVER=https://api-stg02.melodiva.co.id
      - APP_COMMERCIAL_PORT=8001
      - DATABASE_URL=*************************************************************
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-stg02.melodiva.co.id/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
      - CDN_URL=https://cdn.velodiva.com/
    ports:
      - '8001:8001'
    depends_on:
      - database
      - mainredis
    volumes:
      - melodiva:/app/storage

  creator:
    container_name: api-creator
    image: gitlab.vnt.co.id:5050/melodiva1/api/creator:latest
    restart: unless-stopped
    environment:
      - APP_CREATOR_SERVER=hhttps://api-stg03.melodiva.co.id
      - APP_CREATOR_PORT=8002
      - DATABASE_URL=*************************************************************
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-stg02.melodiva.co.id/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
    ports:
      - '8002:8002'
    depends_on:
      - database
      - mainredis
    volumes:
      - melodiva:/app/storage

  lmkn:
    container_name: api-lmkn
    image: gitlab.vnt.co.id:5050/melodiva1/api/lmkn:latest
    restart: unless-stopped
    environment:
      - APP_LMKN_SERVER=https://api-stg04.melodiva.co.id
      - APP_LMKN_PORT=8003
      - DATABASE_URL=*************************************************************
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-stg02.melodiva.co.id/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
    ports:
      - '8003:8003'
    depends_on:
      - database
      - mainredis
    volumes:
      - melodiva:/app/storage

  lmk:
    container_name: api-lmk
    image: gitlab.vnt.co.id:5050/melodiva1/api/lmk:latest
    restart: unless-stopped
    environment:
      - APP_LMK_SERVER=https://api-stg05.melodiva.co.id
      - APP_LMK_PORT=8004
      - DATABASE_URL=*************************************************************
      - AT_SECRET=LvZBR9G8br3IiwgExUoDtP96QC4vl8Xr
      - RT_SECRET=NEtqXe9iGhj8tPCUl39Z8Eglubq08jSW
      - GOOGLE_CLIENT_ID=1011196471658-irtrgsaf977o3fialroa83g1lv4vfthg.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - CALLBACK_URL=https://api-stg02.melodiva.co.id/v1/auth/google/callback
      - MAIL_HOST=inmail.vnt.net.id
      - MAIL_USER=<EMAIL>
      - MAIL_PASSWORD=wyxcab-Nehwo7-dygnyg
      - MAIL_FROM=<EMAIL>
      - REDIS=redis://default:Jaguar123@mainredis:6379
    ports:
      - '8004:8004'
    depends_on:
      - database
      - mainredis
    volumes:
      - melodiva:/app/storage

  database:
    container_name: database
    image: postgres:15.2-alpine
    restart: unless-stopped
    # env_file: .env
    environment:
      - POSTGRES_USER=jaguar
      - POSTGRES_PASSWORD=Jaguar123
      - POSTGRES_DB=melodiva
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - '5432:5432'
      
  mainredis:
    container_name: redis
    image: redis:7.2.1-alpine
    restart: unless-stopped
    command: >
          --requirepass Jaguar123
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"

volumes:
  db_data:
  melodiva:
    driver: local
    driver_opts:
      type: 'none'
      o: 'bind'
      device: '/srv/storage'
