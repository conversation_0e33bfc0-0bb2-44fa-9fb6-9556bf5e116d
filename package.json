{"name": "admin", "version": "0.0.1", "prisma": {"seed": "ts-node prisma/seed.ts"}, "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "clean": "rm -rf dist", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "tsv": "pnpm exec ts-node libs/utils/track.util.ts", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/admin/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/admin/test/jest-e2e.json"}, "dependencies": {"@css-inline/css-inline": "^0.14.3", "@devicefarmer/adbkit-apkreader": "^3.2.4", "@elastic/elasticsearch": "^9.0.3", "@grpc/grpc-js": "^1.12.5", "@grpc/reflection": "^1.0.4", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.1", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.5", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.5", "@nestjs/cqrs": "^11.0.0", "@nestjs/elasticsearch": "^11.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.5", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.5", "@nestjs/platform-socket.io": "^11.0.5", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^11.0.3", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.5", "@prisma/client": "^6.2.1", "@socket.io/redis-adapter": "^8.3.0", "@types/geoip-lite": "^1.4.4", "axios": "^1.7.9", "bcrypt": "^6.0.0", "bull": "^4.16.5", "cache-manager": "^6.4.0", "cache-manager-redis-store": "^3.0.1", "cacheable": "^1.8.8", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "csrf-csrf": "^4.0.2", "dayjs": "^1.11.13", "exceljs": "^4.3.0", "express": "^4.21.2", "fluent-ffmpeg": "^2.1.3", "fuse.js": "^7.0.0", "geoip-lite": "^1.4.10", "handlebars": "^4.7.8", "install": "^0.13.0", "ioredis": "^5.4.2", "json2csv": "6.0.0-alpha.2", "keyv": "^5.2.3", "minio": "^8.0.4", "moment": "^2.30.1", "multer": "1.4.5-lts.2", "ncsrf": "^1.1.0", "nestjs-prisma": "^0.24.0", "nodemailer": "^6.10.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "preview-email": "^3.1.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.33.5", "socket.io": "^4.8.1", "ua-parser-js": "^2.0.0"}, "devDependencies": {"@keyv/redis": "^4.2.0", "@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.5", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.14", "@types/minio": "^7.1.1", "@types/multer": "^1.4.12", "@types/node": "^22.10.10", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.21.0", "@typescript-eslint/parser": "^8.21.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.4.2", "prisma": "^6.2.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "ts-proto": "^2.6.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/common(|/.*)$": "<rootDir>/libs/common/src/$1", "^defaultLibraryPrefix/validator(|/.*)$": "<rootDir>/libs/validator/src/$1"}}}