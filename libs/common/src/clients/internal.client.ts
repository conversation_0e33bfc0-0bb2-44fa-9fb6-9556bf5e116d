import { ClientProviderOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { cwd } from 'process';
import { INTERNAL_PACKAGE } from './client.const';
import { ConfigService } from '@nestjs/config';

export function InternalClient(
  configService: ConfigService,
): ClientProviderOptions {
  return {
    name: INTERNAL_PACKAGE,
    transport: Transport.GRPC,
    options: {
      package: ['internal'],
      url: configService.get<string>('INTERNAL_GRPC_HOST', 'localhost:7000'),
      protoPath: [
        join(cwd(), '_proto/internal-proto/plan.proto'),
        join(cwd(), '_proto/internal-proto/postal.proto'),
        join(cwd(), '_proto/internal-proto/property-type.proto'),
        join(cwd(), '_proto/internal-proto/property.proto'),
        join(cwd(), '_proto/internal-proto/bundle.proto'),
        join(cwd(), '_proto/internal-proto/license.proto'),
        join(cwd(), '_proto/internal-proto/addon.proto'),
        join(cwd(), '_proto/internal-proto/voucher.proto'),
        join(cwd(), '_proto/internal-proto/order-payment.proto'),
        join(cwd(), '_proto/internal-proto/device.proto'),
        join(cwd(), '_proto/internal-proto/package.proto'),
        join(cwd(), '_proto/internal-proto/auth.proto'),
        join(cwd(), '_proto/internal-proto/user.proto'),
        join(cwd(), '_proto/internal-proto/configuration.proto'),
        join(cwd(), '_proto/internal-proto/referral.proto'),
        join(cwd(), '_proto/internal-proto/job-position.proto'),
        join(cwd(), '_proto/internal-proto/partnership.proto'),
        join(cwd(), '_proto/internal-proto/demo-code.proto'),
        join(cwd(), '_proto/internal-proto/product.proto'),
        join(cwd(), '_proto/internal-proto/product-variant.proto'),
      ],
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };
}
