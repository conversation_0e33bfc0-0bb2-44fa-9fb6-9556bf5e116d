import { ClientProviderOptions, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { cwd } from 'process';
import { MEMBERSHIP_PACKAGE } from './client.const';
import { ConfigService } from '@nestjs/config';

export function MembershipClient(
  configService: ConfigService,
): ClientProviderOptions {
  return {
    name: MEMBERSHIP_PACKAGE,
    transport: Transport.GRPC,
    options: {
      package: ['membership'],
      url: configService.get<string>('MEBERSHIP_GRPC_HOST', 'localhost:6000'), // Get from env
      protoPath: [
        join(cwd(), '_proto/membership-proto/auth.proto'),
        join(cwd(), '_proto/membership-proto/order.proto'),
        join(cwd(), '_proto/membership-proto/property.proto'),
        join(cwd(), '_proto/membership-proto/user.proto'),
        join(cwd(), '_proto/membership-proto/license.proto'),
      ],
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };
}
