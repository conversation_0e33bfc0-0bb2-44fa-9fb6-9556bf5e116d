import { ConfigService } from '@nestjs/config';
import { ClientProviderOptions, Transport } from '@nestjs/microservices';
import { JINGGLE_PACKAGE } from './client.const';
import { join } from 'path';
import { cwd } from 'process';

export function JinggleClient(
  configService: ConfigService,
): ClientProviderOptions {
  return {
    name: JINGGLE_PACKAGE,
    transport: Transport.GRPC,
    options: {
      package: 'jinggle',
      url: configService.get<string>('JINGLE_GRPC_HOST', '0.0.0.0:7000'), // Get from env
      protoPath: [join(cwd(), '_proto/jinggle-proto/jinggle.proto')],
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };
}
