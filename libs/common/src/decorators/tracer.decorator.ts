/**
 * Tracer method loggers.
 *
 * @param target - The prototype of the class.
 * @param propertyKey - The name of the method being decorated.
 * @param descriptor - The method's property descriptor.
 * @returns The updated property descriptor with tracing functionality.
 */
export function Tracer(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ): PropertyDescriptor {
    const originalMethod = descriptor.value;
  
    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      const timestamp = new Date().toISOString();
  
      logTrace('Input', propertyKey, args, timestamp);
  
      try {
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;
  
        logTrace('Output', propertyKey, result, timestamp, duration, 'Success');
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
  
        logTrace('Error', propertyKey, error, timestamp, duration, 'Failed');
        throw error;
      }
    };
  
    return descriptor;
  }
  
  /**
   * Logs detailed trace information.
   *
   * @param type - Type of trace (Input, Output, or Error).
   * @param methodName - Name of the method.
   * @param data - The data to log.
   * @param timestamp - ISO datetime when the method was called.
   * @param duration - (Optional) Duration in milliseconds.
   * @param status - (Optional) Status of the method call (Success or Failed).
   */
  function logTrace(
    type: 'Input' | 'Output' | 'Error',
    methodName: string,
    data: any,
    timestamp: string,
    duration?: number,
    status?: 'Success' | 'Failed'
  ): void {
    console.log('='.repeat(60));
    console.log(`[Tracer] Method: ${methodName}`);
    console.log(`[Tracer] Type: ${type}`);
    console.log(`[Tracer] Datetime: ${timestamp}`);
    if (duration !== undefined) {
      console.log(`[Tracer] Duration: ${duration}ms`);
    }
    if (status) {
      console.log(`[Tracer] Status: ${status}`);
    }
    console.log(`[Tracer] Data:`, JSON.stringify(data, null, 2));
    console.log('='.repeat(60));
  }
  