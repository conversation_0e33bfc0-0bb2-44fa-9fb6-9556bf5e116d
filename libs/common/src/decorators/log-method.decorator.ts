export function LogMethod(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
        console.log(`[${propertyKey}] Start`, JSON.stringify(args, null, 2));
        try {
            const result = await originalMethod.apply(this, args);
            console.log(`[${propertyKey}] End`, JSON.stringify(result, null, 2));
            return result;
        } catch (error) {
            console.error(`[${propertyKey}] Error:`, error);
            throw error;
        }
    };

    return descriptor;
}