import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, ValidateIf } from 'class-validator';
import { SortType } from './enums/sort-type.enum';

export class BaseFilterDto {
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.search != '')
  @IsString()
  search?: string;
  @IsOptional()
  @ValidateIf((prop) => prop.page != '')
  @IsString()
  @ApiPropertyOptional()
  page?: number;
  @IsOptional()
  @ValidateIf((prop) => prop.limit != '')
  @IsString()
  @ApiPropertyOptional()
  limit?: number;
  @IsOptional()
  @ValidateIf((prop) => prop.sort != '')
  @IsString()
  @ApiPropertyOptional()
  sort?: string;
  @ApiPropertyOptional({ enum: SortType, default: SortType.ASC })
  @IsOptional()
  @IsEnum(SortType)
  sortType?: SortType;
}
