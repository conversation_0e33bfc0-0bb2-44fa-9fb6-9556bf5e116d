import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString, ValidateIf } from 'class-validator';
import { Type } from 'class-transformer';
import { SortType } from './enums/sort-type.enum';

export class BaseCursorFilterDto {
  @ApiPropertyOptional({ description: 'Optional search keyword' })
  @IsOptional()
  @IsString()
  @ValidateIf((prop) => prop.search !== '')
  search?: string;

  @ApiPropertyOptional({
    description: 'Cursor ID of the last item from previous page',
    type: String,
  })
  @IsOptional()
  @IsString()
  cursor?: string;

  @ApiPropertyOptional({
    description: 'Number of items to fetch after the cursor',
    default: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  take?: number;

  @ApiPropertyOptional({
    description: 'Field to sort by',
  })
  @IsOptional()
  @IsString()
  sort?: string;

  @ApiPropertyOptional({
    enum: SortType,
    default: SortType.ASC,
    description: 'Sort order',
  })
  @IsOptional()
  @IsEnum(SortType)
  sortType?: SortType; 
}