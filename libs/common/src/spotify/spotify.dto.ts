import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Allow, IsOptional } from 'class-validator';

export class ExternalUrlsDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  spotify?: string;
}

export class ImageDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  url?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  height?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  width?: number;
}

export class RestrictionsDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  reason?: string;
}

export class FollowersDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  href?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  total?: number;
}

export class ExternalIDSDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  isrc?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  ean?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  upc?: string;
}

export class LinkedFromDto {}

export class SpotifyTrackArtistDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  external_urls?: ExternalUrlsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  followers?: FollowersDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  genres?: string[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  href?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  id?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  images?: ImageDto[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  name?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  popularity?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  type?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  uri?: string;
}

export class AlbumArtistDto {
  @ApiPropertyOptional({ type: ExternalUrlsDto })
  external_urls?: ExternalUrlsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  href?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  id?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  name?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  type?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  uri?: string;
}
export class AlbumDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  album_type?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  total_tracks?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  available_markets?: string[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  external_urls?: ExternalUrlsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  href?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  id?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  images?: ImageDto[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  name?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  release_date?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  release_date_precision?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  restrictions?: RestrictionsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  type?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  uri?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  artists?: AlbumArtistDto[];
}
export class SpotifyTrackDto {
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  album?: AlbumDto;
  @ApiPropertyOptional({ type: [SpotifyTrackArtistDto] })
  @Type(() => SpotifyTrackArtistDto)
  artists?: SpotifyTrackArtistDto[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  available_markets?: string[];
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  disc_number?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  duration_ms?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  explicit?: boolean;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  external_ids?: ExternalIDSDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  external_urls?: ExternalUrlsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  href?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  id?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  is_playable?: boolean;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  linked_from?: LinkedFromDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  restrictions?: RestrictionsDto;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  name?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  popularity?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  preview_url?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  track_number?: number;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  type?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  uri?: string;
  @ApiPropertyOptional()
  @Allow()
  @IsOptional()
  is_local?: boolean;
}
