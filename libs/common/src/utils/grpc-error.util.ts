import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

export class GrpcErrorUtil {
  static handlePrismaError(
    error: any,
    context: string = 'Database operation',
  ): never {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2000':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'The provided value is too long for the field',
          });
        case 'P2001':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Record not found',
          });
        case 'P2002':
          throw new RpcException({
            code: status.ALREADY_EXISTS,
            message: 'Unique constraint violation',
          });
        case 'P2003':
          throw new RpcException({
            code: status.FAILED_PRECONDITION,
            message: 'Foreign key constraint failed',
          });
        case 'P2004':
          throw new RpcException({
            code: status.FAILED_PRECONDITION,
            message: 'Constraint failed on the database',
          });
        case 'P2005':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Invalid value stored in the database',
          });
        case 'P2006':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Invalid value provided',
          });
        case 'P2007':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Data validation error',
          });
        case 'P2008':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Failed to parse the query',
          });
        case 'P2009':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Failed to validate the query',
          });
        case 'P2010':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Raw query failed',
          });
        case 'P2011':
          throw new RpcException({
            code: status.FAILED_PRECONDITION,
            message: 'Null constraint violation',
          });
        case 'P2012':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Missing required value',
          });
        case 'P2013':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Missing required argument',
          });
        case 'P2014':
          throw new RpcException({
            code: status.FAILED_PRECONDITION,
            message: 'Required relation is missing',
          });
        case 'P2015':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Related record not found',
          });
        case 'P2016':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Query interpretation error',
          });
        case 'P2017':
          throw new RpcException({
            code: status.FAILED_PRECONDITION,
            message: 'Records are not connected',
          });
        case 'P2018':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Required connected records not found',
          });
        case 'P2019':
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Input error',
          });
        case 'P2020':
          throw new RpcException({
            code: status.OUT_OF_RANGE,
            message: 'Value out of range',
          });
        case 'P2021':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Table does not exist',
          });
        case 'P2022':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Column does not exist',
          });
        case 'P2023':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Inconsistent column data',
          });
        case 'P2024':
          throw new RpcException({
            code: status.DEADLINE_EXCEEDED,
            message: 'Connection timeout',
          });
        case 'P2025':
          throw new RpcException({
            code: status.NOT_FOUND,
            message: 'Record not found',
          });
        case 'P2026':
          throw new RpcException({
            code: status.UNAVAILABLE,
            message: 'Database server error',
          });
        case 'P2027':
          throw new RpcException({
            code: status.INTERNAL,
            message: 'Multiple database errors occurred',
          });
        default:
          throw new RpcException({
            code: status.INTERNAL,
            message: `Database error occurred: ${error.code}`,
          });
      }
    }

    if (error instanceof Prisma.PrismaClientValidationError) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Invalid data provided',
      });
    }

    if (error instanceof Prisma.PrismaClientInitializationError) {
      throw new RpcException({
        code: status.UNAVAILABLE,
        message: 'Database connection failed',
      });
    }

    if (error instanceof Prisma.PrismaClientRustPanicError) {
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Database engine error',
      });
    }
    throw error;
  }

  /**
   * Handle network/HTTP errors
   */
  static handleNetworkError(error: any): never {
    if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      throw new RpcException({
        code: status.DEADLINE_EXCEEDED,
        message: 'Request timeout',
      });
    }

    if (error.code === 'ECONNREFUSED') {
      throw new RpcException({
        code: status.UNAVAILABLE,
        message: 'Service unavailable',
      });
    }

    if (error.code === 'ENOTFOUND') {
      throw new RpcException({
        code: status.UNAVAILABLE,
        message: 'Service not found',
      });
    }

    if (error.response?.status) {
      const statusCode = error.response.status;
      if (statusCode === 400) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Bad request to external service',
        });
      } else if (statusCode === 401) {
        throw new RpcException({
          code: status.UNAUTHENTICATED,
          message: 'Authentication failed',
        });
      } else if (statusCode === 403) {
        throw new RpcException({
          code: status.PERMISSION_DENIED,
          message: 'Permission denied',
        });
      } else if (statusCode === 404) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Resource not found',
        });
      } else if (statusCode === 409) {
        throw new RpcException({
          code: status.ALREADY_EXISTS,
          message: 'Resource conflict',
        });
      } else if (statusCode === 429) {
        throw new RpcException({
          code: status.RESOURCE_EXHAUSTED,
          message: 'Rate limit exceeded',
        });
      } else if (statusCode >= 500) {
        throw new RpcException({
          code: status.UNAVAILABLE,
          message: 'External service error',
        });
      }
    }
    throw error;
  }

  /**
   * Generic error handler
   */
  static handleError(error: any, context: string = 'Operation'): never {
    console.error(`[${context} Error]`, error);

    if (error instanceof RpcException) {
      throw error;
    }

    // handle as Prisma error
    try {
      this.handlePrismaError(error, context);
    } catch (prismaError) {
      if (prismaError instanceof RpcException) {
        throw prismaError;
      }
    }

    // handle as network error
    try {
      this.handleNetworkError(error);
    } catch (networkError) {
      if (networkError instanceof RpcException) {
        throw networkError;
      }
    }

    // Default fallback
    throw new RpcException({
      code: status.INTERNAL,
      message: `Unexpected error in ${context}`,
    });
  }

  /**
   * Validate required string input
   */
  static validateRequiredString(value: any, fieldName: string): string {
    if (!value || typeof value !== 'string' || value.trim().length === 0) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: `${fieldName} is required`,
      });
    }
    return value.trim();
  }

  /**
   * Validate required number input
   */
  static validateRequiredNumber(value: any, fieldName: string): number {
    if (value === null || value === undefined || isNaN(Number(value))) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: `${fieldName} must be a valid number`,
      });
    }
    return Number(value);
  }
}
