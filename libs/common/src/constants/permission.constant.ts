export const Permission = {
  USER_MANAGE: 'USER:<PERSON><PERSON><PERSON>',
  USER_CREATE: 'USER:CREATE',
  USER_READ: 'USER:READ',
  USER_UPDATE: 'USER:UPDATE',
  USER_DELETE: 'USER:DELETE',

  P<PERSON><PERSON>_MANAGE: 'PLAN:MANAGE',
  PLAN_CREATE: 'PLAN:CREATE',
  PLAN_READ: 'PLAN:READ',
  PLAN_UPDATE: 'PLAN:UPDATE',
  PLAN_DELETE: 'PLAN:DELETE',

  LOG_MANAGE: 'LOG:MANAGE',
  LOG_READ: 'LOG:READ',

  PROPERTY_MANAGE: 'PROPERTY:MANAGE',
  PROPERTY_CREATE: 'PROPERTY:CREATE',
  PROPERTY_READ: 'PROPERTY:READ',
  PROPERTY_UPDATE: 'PROPERTY:UPDATE',
  PROPERTY_DELETE: 'PROPERTY:DELETE',

  PRODUCT_MANAGE: 'PRODUCT:MANAGE',
  PRODUCT_CREATE: 'PRODUCT:CREATE',
  PRODUCT_READ: 'PRODUCT:READ',
  PRODUCT_UPDATE: 'PRODUCT:UPDATE',
  PRODUCT_DELETE: 'PRODUCT:DELETE',

  PRODUCT_VARIANT_MANAGE: 'PRODUCT_VARIANT:MANAGE',
  PRODUCT_VARIANT_CREATE: 'PRODUCT_VARIANT:CREATE',
  PRODUCT_VARIANT_READ: 'PRODUCT_VARIANT:READ',
  PRODUCT_VARIANT_UPDATE: 'PRODUCT_VARIANT:UPDATE',
  PRODUCT_VARIANT_DELETE: 'PRODUCT_VARIANT:DELETE',

  ROLE_MANAGE: 'ROLE:MANAGE',
  ROLE_CREATE: 'ROLE:CREATE',
  ROLE_READ: 'ROLE:READ',
  ROLE_UPDATE: 'ROLE:UPDATE',
  ROLE_DELETE: 'ROLE:DELETE',

  SETTING_MANAGE: 'SETTING:MANAGE',
  SETTING_CREATE: 'SETTING:CREATE',
  SETTING_READ: 'SETTING:READ',
  SETTING_UPDATE: 'SETTING:UPDATE',
  SETTING_DELETE: 'SETTING:DELETE',

  DEVICE_MANAGE: 'DEVICE:MANAGE',
  DEVICE_CREATE: 'DEVICE:CREATE',
  DEVICE_READ: 'DEVICE:READ',
  DEVICE_UPDATE: 'DEVICE:UPDATE',
  DEVICE_DELETE: 'DEVICE:DELETE',

  INTEGRATION_MANAGE: 'INTEGRATION:MANAGE',
  INTEGRATION_CREATE: 'INTEGRATION:CREATE',
  INTEGRATION_READ: 'INTEGRATION:READ',
  INTEGRATION_UPDATE: 'INTEGRATION:UPDATE',
  INTEGRATION_DELETE: 'INTEGRATION:DELETE',

  SCHEDULE_MANAGE: 'SCHEDULE:MANAGE',
  SCHEDULE_CREATE: 'SCHEDULE:CREATE',
  SCHEDULE_READ: 'SCHEDULE:READ',
  SCHEDULE_UPDATE: 'SCHEDULE:UPDATE',
  SCHEDULE_DELETE: 'SCHEDULE:DELETE',

  TRACK_MANAGE: 'TRACK:MANAGE',
  TRACK_CREATE: 'TRACK:CREATE',
  TRACK_READ: 'TRACK:READ',
  TRACK_UPDATE: 'TRACK:UPDATE',
  TRACK_DELETE: 'TRACK:DELETE',

  MEMBERSHIP_MANAGE: 'MEMBERSHIP:MANAGE',
  MEMBERSHIP_CREATE: 'MEMBERSHIP:CREATE',
  MEMBERSHIP_READ: 'MEMBERSHIP:READ',
  MEMBERSHIP_UPDATE: 'MEMBERSHIP:UPDATE',
  MEMBERSHIP_DELETE: 'MEMBERSHIP:DELETE',

  PROPERTY_TYPE_MANAGE: 'PROPERTY_TYPE:MANAGE',
  PROPERTY_TYPE_CREATE: 'PROPERTY_TYPE:CREATE',
  PROPERTY_TYPE_READ: 'PROPERTY_TYPE:READ',
  PROPERTY_TYPE_UPDATE: 'PROPERTY_TYPE:UPDATE',
  PROPERTY_TYPE_DELETE: 'PROPERTY_TYPE:DELETE',

  BILLING_MANAGE: 'BILLING:MANAGE',
  BILLING_CREATE: 'BILLING:CREATE',
  BILLING_READ: 'BILLING:READ',
  BILLING_UPDATE: 'BILLING:UPDATE',
  BILLING_DELETE: 'BILLING:DELETE',

  PLAYLIST_MANAGE: 'PLAYLIST:MANAGE',
  PLAYLIST_CREATE: 'PLAYLIST:CREATE',
  PLAYLIST_READ: 'PLAYLIST:READ',
  PLAYLIST_UPDATE: 'PLAYLIST:UPDATE',
  PLAYLIST_DELETE: 'PLAYLIST:DELETE',

  REPORT_MANAGE: 'REPORT:MANAGE',
  REPORT_CREATE: 'REPORT:CREATE',
  REPORT_READ: 'REPORT:READ',
  REPORT_UPDATE: 'REPORT:UPDATE',
  REPORT_DELETE: 'REPORT:DELETE',

  HISTORY_MANAGE: 'HISTORY:MANAGE',
  HISTORY_CREATE: 'HISTORY:CREATE',
  HISTORY_READ: 'HISTORY:READ',
  HISTORY_UPDATE: 'HISTORY:UPDATE',
  HISTORY_DELETE: 'HISTORY:DELETE',

  VOUCHER_MANAGE: 'VOUCHER:MANAGE',
  VOUCHER_CREATE: 'VOUCHER:CREATE',
  VOUCHER_READ: 'VOUCHER:READ',
  VOUCHER_UPDATE: 'VOUCHER:UPDATE',
  VOUCHER_DELETE: 'VOUCHER:DELETE',

  PAYMENT_MANAGE: 'PAYMENT:MANAGE',
  PAYMENT_CREATE: 'PAYMENT:CREATE',
  PAYMENT_READ: 'PAYMENT:READ',
  PAYMENT_UPDATE: 'PAYMENT:UPDATE',
  PAYMENT_DELETE: 'PAYMENT:DELETE',

  TRIAL_MANAGE: 'TRIAL:MANAGE',
  TRIAL_CREATE: 'TRIAL:CREATE',
  TRIAL_READ: 'TRIAL:READ',
  TRIAL_UPDATE: 'TRIAL:UPDATE',
  TRIAL_DELETE: 'TRIAL:DELETE',

  ATV_MANAGE: 'ATV:MANAGE',
  ATV_CREATE: 'ATV:CREATE',
  ATV_READ: 'ATV:READ',
  ATV_UPDATE: 'ATV:UPDATE',
  ATV_DELETE: 'ATV:DELETE',

  WEB_PLAYER_MANAGE: 'WEB_PLAYER:MANAGE',
  WEB_PLAYER_CREATE: 'WEB_PLAYER:CREATE',
  WEB_PLAYER_READ: 'WEB_PLAYER:READ',
  WEB_PLAYER_UPDATE: 'WEB_PLAYER:UPDATE',
  WEB_PLAYER_DELETE: 'WEB_PLAYER:DELETE',

  PARTNERSHIP_MANAGE: 'PARTNERSHIP_MANAGE',
  PARTNERSHIP_CREATE: 'PARTNERSHIP_CREATE',
  PARTNERSHIP_READ: 'PARTNERSHIP_READ',
  PARTNERSHIP_UPDATE: 'PARTNERSHIP_UPDATE',
  PARTNERSHIP_DELETE: 'PARNTERSHIP_DELETE',

  REPOSITORY_MANAGE: 'REPOSITORY:MANAGE',
  REPOSITORY_CREATE: 'REPOSITORY:CREATE',
  REPOSITORY_READ: 'REPOSITORY:READ',
  REPOSITORY_UPDATE: 'REPOSITORY:UPDATE',
  REPOSITORY_DELETE: 'REPOSITORY:DELETE',

  ZONE_TYPE_MANAGE: 'ZONE_TYPE_MANAGE',
  ZONE_TYPE_CREATE: 'ZONE_TYPE_CREATE',
  ZONE_TYPE_READ: 'ZONE_TYPE_READ',
  ZONE_TYPE_UPDATE: 'ZONE_TYPE_UPDATE',
  ZONE_TYPE_DELETE: 'ZONE_TYPE_DELETE',
};

export const PermissionModule = {
  USER: 'USER',
  PROPERTY: 'PROPERTY',
  PROPERTY_TYPE: 'PROPERTY_TYPE',
  ROLE: 'ROLE',
  PLAN: 'PLAN',
  SETTING: 'SETTING',
  DEVICE: 'DEVICE',
  PLAYLIST: 'PLAYLIST',
  INTEGRATION: 'INTEGRATION',
  SCHEDULE: 'SCHEDULE',
  TRACK: 'TRACK',
  MEMBERSHIP: 'MEMBERSHIP',
  CREATOR: 'CREATOR',
  ORDER: 'ORDER',
  BILLING: 'BILLING',
  REPORT: 'REPORT',
  HISTORY: 'HISTORY',
  PAYMENT: 'PAYMENT',
  TRIAL: 'TRIAL',
  ATV: 'ATV',
  WEB_PLAYER: 'WEB_PLAYER',
  PARTNERSHIP: 'PARTNERSHIP',
  ZONE_TYPE: 'ZONE_TYPE',
};
