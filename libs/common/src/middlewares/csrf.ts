import { doubleCsrf } from 'csrf-csrf';

const isLocal = process.env.NODE_ENV === 'local';

export const { generateCsrfToken, validateRequest } = doubleCsrf({
  getSecret: () => process.env.CSRF_SECRET || 'local-dev-secret',
  getSessionIdentifier: (req) => req?.headers['user-agent'] || 'unknown',

  cookieName: 'xsrf_token',
  cookieOptions: {
    domain: isLocal ? undefined : '.velodiva.com',
    sameSite: isLocal ? 'lax' : 'none',
    path: '/',
    secure: isLocal ? false : true,
    httpOnly: true,
    maxAge: 1000 * 60 * 60 * 24 * 7,
  },

  size: 32,
  ignoredMethods: ['GET', 'HEAD', 'OPTIONS'],
  getCsrfTokenFromRequest: (req) => req.headers['x-csrf-token'],
});
