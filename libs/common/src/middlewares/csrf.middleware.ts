import { ForbiddenException, Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { generateCsrfToken, validateRequest } from './csrf';

@Injectable()
export class CsrfMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const methods = ['GET', 'HEAD', 'OPTIONS'];

    const skipPaths = ['/v1/webhook', '/v1/app', '/v1/health'];
    const shouldSkip = skipPaths.some((path) => req.path.startsWith(path));

    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Content-Security-Policy', "default-src 'self'");

    if (shouldSkip || methods.includes(req.method)) {
      return next();
    }

    if (validateRequest(req)) {
      res.setHeader('X-CSRF-Token', this.generateNewCsrfToken(req, res));
      return next();
    }

    throw new ForbiddenException('invalid csrf token');
  }

  private generateNewCsrfToken(request: any, response: any): string {
    const csrfToken = generateCsrfToken(request, response, { overwrite: true });
    return csrfToken;
  }
}
