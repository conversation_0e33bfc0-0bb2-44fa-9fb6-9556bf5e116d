import {
  Injectable,
  NestMiddleware,
  ServiceUnavailableException,
  Inject,
  Logger,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { AxiosError } from 'axios';

@Injectable()
export class MaintenanceMiddleware implements NestMiddleware {
  static serviceId: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRedis() private readonly redis: Redis,
  ) {}
  private logger = new Logger(MaintenanceMiddleware.name);
  async use(req: Request, res: Response, next: NextFunction) {
    if (this.configService.get<string>('NODE_ENV') !== 'production') {
      return next();
    }

    let clientIp =
      (req.headers['x-forwarded-for'] as string) ||
      req.socket.remoteAddress?.replace('::ffff:', '') ||
      '';

    const apiKey =
      MaintenanceMiddleware.serviceId ||
      this.configService.get<string>('COMMERCIAL_SERVICE_ID');

    const url = `${this.configService.get<string>('MAINTENANCE_BASE_URL')}/v1/coffee-break`;

    try {
      const response = await firstValueFrom(
        this.httpService.get<any>(url, {
          timeout: 10000,
          headers: {
            'x-api-key': apiKey,
            'x-forwarded-for': clientIp,
          },
        }),
      );

      const maintenanceData = response.data?.data;

      if (maintenanceData) {
        throw new ServiceUnavailableException({
          message: 'Service is under maintenance',
          statusCode: 503,
          data: maintenanceData,
        });
      }

      return next();
    } catch (error) {
      const axiosError = error as AxiosError;

      // Timeout
      if (axiosError.code === 'ECONNABORTED') {
        this.logger.warn('Request timeout to maintenance endpoint');
        return next();
      }

      // DNS error, domain tidak ditemukan, dll
      if (
        axiosError.code === 'ENOTFOUND' ||
        axiosError.code === 'ECONNREFUSED'
      ) {
        this.logger.warn(
          `Cannot connect to maintenance endpoint (${axiosError.code})`,
        );
        return next();
      }

      // Kalau responnya 404 atau 503
      if ([404, 503].includes(axiosError.response?.status || 0)) {
        return next();
      }

      // Jika error lainnya, lempar
      throw error;
    }
  }
}
