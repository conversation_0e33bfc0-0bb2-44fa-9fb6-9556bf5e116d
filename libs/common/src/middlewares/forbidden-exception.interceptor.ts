// src/common/filters/forbidden-exception.filter.ts
import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  ForbiddenException,
} from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(ForbiddenException)
export class ForbiddenExceptionFilter implements ExceptionFilter {
  catch(exception: ForbiddenException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    // Optional: log selectively or suppress completely
    if (exception.message !== 'invalid csrf token') {
      console.error(exception);
    }

    response.status(403).json({
      statusCode: 403,
      message: 'Forbidden',
    });
  }
}
