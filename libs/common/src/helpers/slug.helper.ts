import { PrismaService } from "libs/prisma";

const prisma = new PrismaService

export default async function slugify(str: string, parentId: string | null = null): Promise<string> {
  const partialParentId = parentId ? parentId.substring(0, 6) : '';

  let slug = String(str)
    .normalize('NFKD')
    .replace(/[\u0300-\u036f]/g, '')
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9 -]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    + (partialParentId ? `-${partialParentId}` : '');

  let counter = 1;
  while (true) {
    const existingSlug = await prisma.propertyType.findFirst({
      where: { slug },
      select: { id: true },
    });

    if (!existingSlug) {
      return slug;
    }

    slug = `${slug}-${counter}`;
    counter++;
  }
}