import { exclude } from './exclude.helper';

export interface PaginatedResult<T> {
  data: T[];
  meta: {
    total: number;
    lastPage: number;
    currentPage: number;
    limit: number;
    prev: number | null;
    next: number | null;
  };
}

export type PaginateOptions = {
  page?: number | string;
  limit?: number | string;
  exclude?: string[];
};

export const Pagination = async <T, K>(
  model: any,
  args?: K,
  options?: PaginateOptions,
): Promise<PaginatedResult<T>> => {
  const page = Number(options?.page || 1);
  const limit = Number(options?.limit || 100);
  const excludeField = options?.exclude ? options.exclude : [];

  const skip = page > 0 ? limit * (page - 1) : 0;
  const [total, data] = await Promise.all([
    model.count({
      where: args.hasOwnProperty('where') ? args['where'] : undefined,
    }),
    model.findMany({
      ...args,
      take: limit,
      skip,
    }),
  ]);
  const lastPage = Math.ceil(total / limit);
  const reMap = data.map((item) => exclude(item, excludeField));
  return {
    data: reMap,
    meta: {
      total,
      lastPage,
      currentPage: page,
      limit,
      prev: page > 1 ? page - 1 : null,
      next: page < lastPage ? page + 1 : null,
    },
  };
};
