/**
 * Calculates the total tax from a given amount and a list of tax rules.
 *
 * @param amt - The amount to calculate taxes for.
 * @param taxes - The list of taxes to apply.
 * @returns A promise that resolves to an object containing the total tax and detailed tax items.
 */
export function calculateTax(amt: number, taxes: any[]): Promise<{ totalTax: number; taxItems: any[] }> {
    if (!amt || !taxes?.length) {
        return Promise.resolve({
            totalTax: 0,
            taxItems: []
        });
    }

    let totalTax = 0;
    const taxItems: any[] = [];

    for (const tax of taxes) {
        if (!tax?.nominal) {
            continue;
        }

        const nominal = Number(tax.nominal);
        const taxAmount = (amt * nominal) / 100;
        const type = tax.type?.toLowerCase() === 'reduction' ? 'reduction' : 'increase';

        totalTax += type === 'reduction' ? -taxAmount : taxAmount;

        taxItems.push({
            name: tax.name || '',
            type: tax.type || 'increase',
            nominal,
            value: taxAmount
        });
    }

    return Promise.resolve({
        totalTax: Number(totalTax.toFixed(2)),
        taxItems
    });
}
