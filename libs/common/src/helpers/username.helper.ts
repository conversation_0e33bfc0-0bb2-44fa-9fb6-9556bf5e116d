import * as crypto from 'crypto';

type Style = 'lowerCase' | 'upperCase' | 'capital';

export interface Config {
  dictionaries: string[][];
  separator?: string;
  randomDigits?: number;
  length?: number;
  style?: Style;
}

const getRandomInt = (min: number, max: number): number => {
  const randomBuffer = new Uint32Array(1);

  window.crypto.getRandomValues(randomBuffer);

  const randomNumber = randomBuffer[0] / (0xffffffff + 1);

  return randomNumber * (max - min) + min;
};

export const randomNumber = (maxNumber: number | undefined) => {
  let randomNumberString;
  switch (maxNumber) {
    case 1:
      randomNumberString = crypto.randomInt(1, 9).toString();
      break;
    case 2:
      randomNumberString = crypto.randomInt(10, 90).toString();
      break;
    case 3:
      randomNumberString = crypto.randomInt(100, 900).toString();
      break;
    case 4:
      randomNumberString = crypto.randomInt(1000, 9000).toString();
      break;
    case 5:
      randomNumberString = crypto.randomInt(10000, 90000).toString();
      break;
    case 6:
      randomNumberString = crypto.randomInt(100000, 900000).toString();
      break;
    case 7:
      randomNumberString = crypto.randomInt(1000000, 9000000).toString();
      break;
    case 8:
      randomNumberString = crypto.randomInt(10000000, 90000000).toString();
      break;
    case 9:
      randomNumberString = crypto.randomInt(100000000, 900000000).toString();
      break;
    case 10:
      randomNumberString = crypto.randomInt(1000000000, 9000000000).toString();
      break;
    default:
      randomNumberString = '';
      break;
  }
  return randomNumberString;
};

export function generateFromEmail(
  email: string,
  randomDigits?: number,
): string {
  // Retrieve name from email address
  const nameParts = email.replace(/@.+/, '');
  // Replace all special characters like "@ . _ ";
  const name = nameParts.replace(/[&/\\#,+()$~%._@'":*?<>{}]/g, '');
  // Create and return unique username
  return name + randomNumber(randomDigits);
}

export function generateUsername(
  name?: string,
  separator?: string,
  randomDigits?: number,
): string {
  let username;

  if (separator) {
    username = name + separator + randomNumber(randomDigits);
  } else {
    username = name + randomNumber(randomDigits);
  }

  return username;
}
