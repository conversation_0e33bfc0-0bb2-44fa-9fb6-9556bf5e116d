export const PaginationCursor = async < T, Args extends { take?: number; cursor?: any; skip?: number; orderBy?: any } >
(
  model: { findMany(args: Args): Promise<T[]> },
  args: Args
): Promise<{
  data: T[];
  meta: {
    nextCursor: string | null;
    hasNextPage: boolean;
    limit: number;
    sortType?: 'asc' | 'desc';
  };
}> => {
  const data = await model.findMany(args);

  const limit = args.take ?? 10;
  const sortType = args.orderBy?.[Object.keys(args.orderBy)[0]] ?? undefined;
  const nextCursor = data.length === limit ? (data[data.length - 1] as any)?.id ?? null : null;
  const hasNextPage = data.length === limit;

  return {
    data,
    meta: {
      nextCursor,
      hasNextPage,
      limit,
      sortType,
    },
  };
}
