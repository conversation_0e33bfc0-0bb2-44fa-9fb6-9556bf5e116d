import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { dirname, join } from 'path';
import { cwd } from 'process';
import renameFile from './rename-file';

export type ICreateFileOpts = {
  origin: string;
  path: string;
  buffer: Buffer;
};

export type ICreateFileResponse = {
  name: string;
  fullPath: string;
  shortPath: string;
};
export default function (opts: ICreateFileOpts): ICreateFileResponse {
  const nameFile = renameFile(opts.origin);
  const storePath = join(cwd(), 'storage', opts.path, nameFile);
  if (!existsSync(dirname(storePath))) {
    mkdirSync(dirname(storePath), { recursive: true });
  }

  writeFileSync(storePath, opts.buffer);

  return {
    name: nameFile,
    fullPath: storePath,
    shortPath: opts.path,
  };
}
