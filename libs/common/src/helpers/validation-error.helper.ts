import { BadRequestException, ValidationError } from '@nestjs/common';

export function validateErrorFormat(errors: ValidationError[]) {
  return errors.reduce((p, c: ValidationError) => {
    if (!c.children || !c.children.length) {
      p[c.property] = Object.values(c.constraints);
    } else {
      p[c.property] = validateErrorFormat(c.children);
    }
    return p;
  }, {});
}

export const validationExceptionFactory = (errors: ValidationError[]) => {
  const formatError = (errors: ValidationError[]) => {
    const errMsg = {};
    errors.forEach((error: ValidationError) => {
      errMsg[error.property] = error.children.length
        ? [formatError(error.children)]
        : [...Object.values(error.constraints)];
    });
    return errMsg;
  };
  return new BadRequestException(formatError(errors));
};
