// validation color hex format
export const validateHexColor = (color: string) => {
  return /^#[0-9A-F]{6}$/i.test(color);
};

// generator color
export const generateRandomColor = () => {
  const color =
    '#' +
    Math.floor(Math.random() * 16777215)
      .toString(16)
      .padStart(6, '0')
      .toUpperCase();

  if (validateHexColor(color)) {
    return color;
  } else {
    generateRandomColor();
  }
};
