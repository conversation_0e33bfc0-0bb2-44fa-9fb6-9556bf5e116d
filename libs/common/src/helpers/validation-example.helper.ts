import { plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';

export function ValidationExample<T extends object>(dtoClass: new () => T) {
  const instance = plainToInstance(dtoClass, {}) as T;

  const errors: ValidationError[] = validateSync(instance, {
    skipMissingProperties: false,
    whitelist: false,
    forbidUnknownValues: false,
  });

  const formattedErrors = errors.reduce(
    (acc, error) => {
      if (error.constraints) {
        acc[error.property] = Object.values(error.constraints);
      }
      return acc;
    },
    {} as Record<string, string[]>,
  );

  return {
    statusCode: 400,
    message: 'Bad Request',
    data: formattedErrors,
  };
}
