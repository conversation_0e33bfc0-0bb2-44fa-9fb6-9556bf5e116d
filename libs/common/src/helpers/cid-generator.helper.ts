import { $Enums } from '@prisma/client';

export default function cidGenerator(type: $Enums.UserType, current: string) {
  const year = new Date().getFullYear();
  const startNumber = current.padStart(6, '0');
  if (type == 'commercial') {
    return `C${year}${startNumber}`;
  }
  if (type == 'reseller') {
    return `R${year}${startNumber}`;
  }
  if (type == 'creator') {
    return `A${year}${startNumber}`;
  }
}
