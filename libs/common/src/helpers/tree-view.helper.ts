// export function TreeView(items: TreeItem[]): TreeItem[] {
//   const idMapping: { [id: string]: number } = items.reduce((acc, el, i) => {
//     acc[el.id] = i;
//     return acc;
//   }, {});

//   const root: TreeItem[] = [];
//   const processedIds = new Set<string>();

//   items.forEach((el) => {
//     if (processedIds.has(el.id)) {
//       return; 
//     }

//     if (el.parentId === null || el.parentId === undefined) {
//       root.push(el);
//     } else {
//       const parentIndex = idMapping[el.parentId];
//       const parentEl = items[parentIndex];

//       if (parentEl) {
//         parentEl.children = parentEl.children || [];
//         if (!parentEl.children.some(child => child.id === el.id)) {
//           parentEl.children.push(el);
//         }
//       } else {
//         console.error("Parent element not found for item:", el);
//         root.push(el);
//       }
//     }

//     processedIds.add(el.id); // Mark as processed  
//   });

//   return root;
// }  

export function TreeView(items: TreeItem[]): TreeItem[] {
  const idMapping: { [id: string]: TreeItem } = items.reduce((acc, el) => {
    acc[el.id] = { ...el, children: [] }; 
    return acc;
  }, {});

  const root: TreeItem[] = [];

  items.forEach((el) => {
    if (el.parentId === null || el.parentId === undefined) {
      root.push(idMapping[el.id]);
    } else {
      const parentEl = idMapping[el.parentId];
      if (parentEl) {
        parentEl.children.push(idMapping[el.id]);
      } else {
        console.error("Parent element not found for item:", el);
        root.push(idMapping[el.id]);
      }
    }
  });

  return root;
}  



export interface TreeItem {
  id: string;
  name: string;
  slug: string;
  icon?: string;
  parentId?: string;
  children?: TreeItem[];
}