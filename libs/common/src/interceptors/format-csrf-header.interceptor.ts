import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { generateCsrfToken } from '../middlewares';

@Injectable()
export class FormatCsrfHeaderInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    const headers = request.headers;

    if (headers['X-Csrf-Token'] && !headers['x-csrf-token']) {
      headers['x-csrf-token'] = headers['X-Csrf-Token'];
      delete headers['X-Csrf-Token'];
    }

    const csrfToken = headers['x-csrf-token'];

    const isValid = this.validateCsrfToken(csrfToken, request);

    return next.handle().pipe(
      tap(() => {
        if (isValid) {
          const newCsrfToken = this.generateNewCsrfToken(request, response);
          response.setHeader('X-CSRF-Token', newCsrfToken);
        }
      }),
    );
  }

  private validateCsrfToken(token: string, request: any): boolean {
    return !!token && token.startsWith('csrf-');
  }

  private generateNewCsrfToken(request: any, response: any): string {
    const csrfToken = generateCsrfToken(request, response, { overwrite: true });
    return csrfToken;
  }
}
