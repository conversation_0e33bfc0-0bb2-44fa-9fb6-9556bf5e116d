import { Inject, Injectable, OnModuleInit, Optional } from '@nestjs/common';
import { Prisma, PrismaClient } from '@prisma/client';
import { PrismaServiceOptions } from './interfaces';
import { PRISMA_SERVICE_OPTIONS } from './prisma.constants';
import * as runtime from '@prisma/client/runtime/library'

export type PrismaTransactionOptions = {
  retry?: number
  isolationLevel?: Prisma.TransactionIsolationLevel
 }

@Injectable()
export class PrismaService
  extends PrismaClient<
    Prisma.PrismaClientOptions,
    'query' | 'info' | 'warn' | 'error'
  >
  implements OnModuleInit
{
  constructor(
    @Optional()
    @Inject(PRISMA_SERVICE_OPTIONS)
    private readonly prismaServiceOptions: PrismaServiceOptions = {},
  ) {
    super(prismaServiceOptions.prismaOptions);

    if (this.prismaServiceOptions.middlewares) {
      this.prismaServiceOptions.middlewares.forEach((middleware) =>
        this.$use(middleware),
      );
    }
  }

  async onModuleInit() {
    if (this.prismaServiceOptions.explicitConnect) {
      await this.$connect();
    }
  }

  async onModuleDestroy() {
    console.log('PrismaService: Module being destroyed, releasing connection...');
    await this.$disconnect();
  }

  async $transaction<P extends Prisma.PrismaPromise<any>[]>(
    arg: [...P],
    options?: PrismaTransactionOptions
  ): Promise<runtime.Types.Utils.UnwrapTuple<P>>;
 
  async $transaction<R>(
    fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => Promise<R>,
    options?: PrismaTransactionOptions & { maxWait?: number; timeout?: number; }
  ): Promise<R>;
 
  async $transaction<P extends Prisma.PrismaPromise<any>[], R>(
    argOrFn: [...P] | ((prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => Promise<R>),
    options?: PrismaTransactionOptions & { maxWait?: number; timeout?: number; }
  ): Promise<runtime.Types.Utils.UnwrapTuple<P> | R> {
 
    const max_retries = options?.retry || 1
    let retries = 0
    while (retries < max_retries) {
      try {
        if (typeof argOrFn === 'function') {
          return await super.$transaction(argOrFn, options);
        }
 
        if (argOrFn instanceof Array) {
          return await super.$transaction(argOrFn, options);
        }
 
        return
      } catch (error) {
        if (error.code === 'P2034') {
          retries++
          if (retries < max_retries) {
            continue
          } else {
            throw error
          }
        }
        throw error
      }
    }
  }
}