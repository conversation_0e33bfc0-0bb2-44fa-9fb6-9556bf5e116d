// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/postal.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Query, Status } from "../common-proto/common";

export interface GetPostalByIdsRequest {
  postalIds?: string[] | undefined;
}

export interface GetPostalByIdsResponse {
  data?: Postal[] | undefined;
}

export interface ProvinceRequest {
  search?: Query | undefined;
}

export interface CityRequest {
  provinceId?: string | undefined;
}

export interface DistrictRequest {
  provinceId?: string | undefined;
  city?: string | undefined;
}

export interface UrbanRequest {
  provinceId?: string | undefined;
  city?: string | undefined;
  district?: string | undefined;
}

export interface ZipCodeRequest {
  provinceId?: string | undefined;
  city?: string | undefined;
  district?: string | undefined;
  urban?: string | undefined;
}

export interface Postal {
  id?: string | undefined;
  urban?: string | undefined;
  city?: string | undefined;
  district?: string | undefined;
  code?: string | undefined;
  province?: string | undefined;
  provinceId?: string | undefined;
}

export interface Province {
  id?: string | undefined;
  name?: string | undefined;
}

export interface PostalByCodeRequest {
  code?: string | undefined;
}

export interface ListPostalResponse {
  status?: Status | undefined;
  data?: Postal[] | undefined;
}

export interface ListProvinceResponse {
  status?: Status | undefined;
  data?: Province[] | undefined;
}

export interface ListCityResponse {
  status?: Status | undefined;
  data?: string[] | undefined;
}

export interface ListDistrictResponse {
  status?: Status | undefined;
  data?: string[] | undefined;
}

export interface ListUrbanResponse {
  status?: Status | undefined;
  data?: string[] | undefined;
}

export interface ZipCodeResponse {
  id?: string | undefined;
  code?: string | undefined;
}

export interface PostalServiceClient {
  getProvince(request: ProvinceRequest, metadata: Metadata, ...rest: any): Observable<ListProvinceResponse>;

  getCity(request: CityRequest, metadata: Metadata, ...rest: any): Observable<ListCityResponse>;

  getDistrict(request: DistrictRequest, metadata: Metadata, ...rest: any): Observable<ListDistrictResponse>;

  getUrban(request: UrbanRequest, metadata: Metadata, ...rest: any): Observable<ListUrbanResponse>;

  getZipCode(request: ZipCodeRequest, metadata: Metadata, ...rest: any): Observable<ZipCodeResponse>;

  getPostal(request: Id, metadata: Metadata, ...rest: any): Observable<Postal>;

  getPostalByIds(request: GetPostalByIdsRequest, metadata: Metadata, ...rest: any): Observable<GetPostalByIdsResponse>;
}

export interface PostalServiceController {
  getProvince(
    request: ProvinceRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListProvinceResponse> | Observable<ListProvinceResponse> | ListProvinceResponse;

  getCity(
    request: CityRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListCityResponse> | Observable<ListCityResponse> | ListCityResponse;

  getDistrict(
    request: DistrictRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListDistrictResponse> | Observable<ListDistrictResponse> | ListDistrictResponse;

  getUrban(
    request: UrbanRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListUrbanResponse> | Observable<ListUrbanResponse> | ListUrbanResponse;

  getZipCode(
    request: ZipCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ZipCodeResponse> | Observable<ZipCodeResponse> | ZipCodeResponse;

  getPostal(request: Id, metadata: Metadata, ...rest: any): Promise<Postal> | Observable<Postal> | Postal;

  getPostalByIds(
    request: GetPostalByIdsRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPostalByIdsResponse> | Observable<GetPostalByIdsResponse> | GetPostalByIdsResponse;
}

export function PostalServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getProvince",
      "getCity",
      "getDistrict",
      "getUrban",
      "getZipCode",
      "getPostal",
      "getPostalByIds",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PostalService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PostalService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const POSTAL_SERVICE_NAME = "PostalService";
