// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/order-payment.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";

export interface CheckoutResp {
  checkoutUrl?: string | undefined;
  paymentRequestId?: string | undefined;
  status?: boolean | undefined;
  time?: string | undefined;
}

export interface CancelOrderReq {
  paymentId?: string | undefined;
  reason?: string | undefined;
}

export interface OrderPaymentServiceClient {
  orderCheckout(request: Id, metadata: Metadata, ...rest: any): Observable<CheckoutResp>;

  cancelOrder(request: CancelOrderReq, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface OrderPaymentServiceController {
  orderCheckout(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<CheckoutResp> | Observable<CheckoutResp> | CheckoutResp;

  cancelOrder(request: CancelOrderReq, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status;
}

export function OrderPaymentServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["orderCheckout", "cancelOrder"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("OrderPaymentService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("OrderPaymentService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ORDER_PAYMENT_SERVICE_NAME = "OrderPaymentService";
