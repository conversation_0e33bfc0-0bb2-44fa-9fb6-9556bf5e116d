// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/license.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { PropertyType } from "./property-type";
import { Sku } from "./sku";

export interface License {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  price?: number | undefined;
  taxLicense?: TaxLicense[] | undefined;
  paymentStatus?: string | undefined;
  paymentFrequency?: string | undefined;
  propertyType?: PropertyType | undefined;
  sku?: Sku | undefined;
}

export interface TaxLicense {
  id?: string | undefined;
  taxId?: string | undefined;
  licenseId?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  type?: string | undefined;
  nominal?: number | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
}

export interface LicenseRequest {
  propertyTypeId?: string | undefined;
}

export interface LicenseList {
  license?: License[] | undefined;
}

export interface ListLicenseResponse {
  status?: Status | undefined;
  data?: { [key: string]: LicenseList } | undefined;
}

export interface ListLicenseResponse_DataEntry {
  key: string;
  value?: LicenseList | undefined;
}

export interface SetLicenseRequest {
  id?: string | undefined;
  key?: string | undefined;
  type?: string | undefined;
}

export interface LicenseServiceClient {
  getLicense(request: Id, metadata: Metadata, ...rest: any): Observable<License>;

  listLicense(request: LicenseRequest, metadata: Metadata, ...rest: any): Observable<ListLicenseResponse>;

  licenseByPropertyType(request: LicenseRequest, metadata: Metadata, ...rest: any): Observable<ListLicenseResponse>;

  setPerformingLicense(request: SetLicenseRequest, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface LicenseServiceController {
  getLicense(request: Id, metadata: Metadata, ...rest: any): Promise<License> | Observable<License> | License;

  listLicense(
    request: LicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListLicenseResponse> | Observable<ListLicenseResponse> | ListLicenseResponse;

  licenseByPropertyType(
    request: LicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListLicenseResponse> | Observable<ListLicenseResponse> | ListLicenseResponse;

  setPerformingLicense(
    request: SetLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function LicenseServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getLicense", "listLicense", "licenseByPropertyType", "setPerformingLicense"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("LicenseService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("LicenseService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const LICENSE_SERVICE_NAME = "LicenseService";
