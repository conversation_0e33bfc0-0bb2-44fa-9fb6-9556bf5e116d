// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/product-variant.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id } from "../common-proto/common";
import { Sku } from "./sku";

export interface Variant {
  id?: string | undefined;
  name?: string | undefined;
  price?: number | undefined;
  description?: string | undefined;
  productId?: string | undefined;
  sku?: Sku | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  publish?: boolean | undefined;
  product?: DetailProduct | undefined;
}

export interface DetailProduct {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  propertyTypeId?: string | undefined;
  subfolderId?: string | undefined;
  taxes?: TaxProduct[] | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isMultiple?: boolean | undefined;
}

export interface TaxProduct {
  id?: string | undefined;
  name?: string | undefined;
  nominal?: number | undefined;
  type?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
}

export interface ProductVariant {
  id?: string | undefined;
  name?: string | undefined;
  price?: number | undefined;
  description?: string | undefined;
  productId?: string | undefined;
  sku?: Sku | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  publish?: boolean | undefined;
}

export interface variantServiceClient {
  detailVariant(request: Id, metadata: Metadata, ...rest: any): Observable<Variant>;
}

export interface variantServiceController {
  detailVariant(request: Id, metadata: Metadata, ...rest: any): Promise<Variant> | Observable<Variant> | Variant;
}

export function variantServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["detailVariant"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("variantService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("variantService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const VARIANT_SERVICE_NAME = "variantService";
