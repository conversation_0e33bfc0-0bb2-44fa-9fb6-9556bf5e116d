// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/partnership.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Meta, Query } from "../common-proto/common";
import { Property } from "../membership-proto/property";

export interface Partnership {
  id?: string | undefined;
  name?: string | undefined;
  popularName?: string | undefined;
  crmUserId?: string | undefined;
  propertyId?: string | undefined;
  mediaId?: string | undefined;
  postalId?: string | undefined;
}

export interface ListPartnershipResponse {
  data?: Partnership[] | undefined;
  meta?: Meta | undefined;
}

export interface ListPartnershipPropertiesResponse {
  data?: Property[] | undefined;
  meta?: Meta | undefined;
}

export interface ListPartnershipPropertiesRequest {
  query?: Query | undefined;
  partnershipId?: string | undefined;
}

export interface PartnershipServiceClient {
  listPartnership(request: Query, metadata: Metadata, ...rest: any): Observable<ListPartnershipResponse>;

  getPartnership(request: Id, metadata: Metadata, ...rest: any): Observable<Partnership>;

  listPartnershipProperties(
    request: ListPartnershipPropertiesRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<ListPartnershipPropertiesResponse>;
}

export interface PartnershipServiceController {
  listPartnership(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPartnershipResponse> | Observable<ListPartnershipResponse> | ListPartnershipResponse;

  getPartnership(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Partnership> | Observable<Partnership> | Partnership;

  listPartnershipProperties(
    request: ListPartnershipPropertiesRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListPartnershipPropertiesResponse>
    | Observable<ListPartnershipPropertiesResponse>
    | ListPartnershipPropertiesResponse;
}

export function PartnershipServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listPartnership", "getPartnership", "listPartnershipProperties"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PartnershipService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PartnershipService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PARTNERSHIP_SERVICE_NAME = "PartnershipService";
