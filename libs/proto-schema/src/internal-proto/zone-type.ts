// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/zone-type.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Meta, Query } from "../common-proto/common";

export interface ZoneType {
  id?: string | undefined;
  name?: string | undefined;
}

export interface ListZoneTypesResponse {
  data?: ZoneType[] | undefined;
  meta?: Meta | undefined;
}

export interface ZoneTypeServiceClient {
  listZoneTypes(request: Query, metadata: Metadata, ...rest: any): Observable<ListZoneTypesResponse>;
}

export interface ZoneTypeServiceController {
  listZoneTypes(
    request: Query,
    metadata: Metada<PERSON>,
    ...rest: any
  ): Promise<ListZoneTypesResponse> | Observable<ListZoneTypesResponse> | ListZoneTypesResponse;
}

export function ZoneTypeServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listZoneTypes"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ZoneTypeService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ZoneTypeService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const ZONE_TYPE_SERVICE_NAME = "ZoneTypeService";
