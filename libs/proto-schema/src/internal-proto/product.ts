// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/product.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { Tax } from "./plan";
import { ProductVariant } from "./product-variant";
import { Sku } from "./sku";

export interface Product {
  id?: string | undefined;
  name?: string | undefined;
  price?: number | undefined;
  description?: string | undefined;
  publish?: boolean | undefined;
  propertyTypeId?: string | undefined;
  subfolderId?: string | undefined;
  taxes?: Tax[] | undefined;
  sku?: Sku | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isMultiple?: boolean | undefined;
  image?: string | undefined;
}

export interface ProductRequest {
  industryId?: string | undefined;
  subfolderId?: string | undefined;
  businessType?: string | undefined;
}

export interface ListProductResponse {
  status?: Status | undefined;
  data?: Products[] | undefined;
}

export interface Products {
  id?: string | undefined;
  name?: string | undefined;
  price?: number | undefined;
  description?: string | undefined;
  publish?: boolean | undefined;
  propertyTypeId?: string | undefined;
  subfolderId?: string | undefined;
  taxes?: Tax[] | undefined;
  sku?: Sku | undefined;
  variants?: ProductVariant[] | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isMultiple?: boolean | undefined;
  image?: string | undefined;
}

export interface ProductServiceClient {
  listProduct(request: ProductRequest, metadata: Metadata, ...rest: any): Observable<ListProductResponse>;

  detailProduct(request: Id, metadata: Metadata, ...rest: any): Observable<Product>;
}

export interface ProductServiceController {
  listProduct(
    request: ProductRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListProductResponse> | Observable<ListProductResponse> | ListProductResponse;

  detailProduct(request: Id, metadata: Metadata, ...rest: any): Promise<Product> | Observable<Product> | Product;
}

export function ProductServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["listProduct", "detailProduct"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("ProductService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("ProductService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PRODUCT_SERVICE_NAME = "ProductService";
