// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/demo-code.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Meta, Query, Status } from "../common-proto/common";
import { Property } from "../membership-proto/property";

export interface DemoCodeRequest {
  code?: string | undefined;
  propertyId?: string | undefined;
}

export interface ListDemoCodePropertiesRequest {
  demoCodeId?: string | undefined;
  query?: Query | undefined;
}

export interface DemoCodeProperties {
  code?: string | undefined;
  properties?: Property[] | undefined;
}

export interface ListDemoCodePropertiesResponse {
  data?: DemoCodeProperties[] | undefined;
  meta?: Meta | undefined;
}

export interface DemoCodeServiceClient {
  useDemoCode(request: DemoCodeRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  listDemoCodeProperties(
    request: ListDemoCodePropertiesRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<ListDemoCodePropertiesResponse>;

  validateDemoCode(request: DemoCodeRequest, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface DemoCodeServiceController {
  useDemoCode(
    request: DemoCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  listDemoCodeProperties(
    request: ListDemoCodePropertiesRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListDemoCodePropertiesResponse>
    | Observable<ListDemoCodePropertiesResponse>
    | ListDemoCodePropertiesResponse;

  validateDemoCode(
    request: DemoCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function DemoCodeServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["useDemoCode", "listDemoCodeProperties", "validateDemoCode"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("DemoCodeService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("DemoCodeService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const DEMO_CODE_SERVICE_NAME = "DemoCodeService";
