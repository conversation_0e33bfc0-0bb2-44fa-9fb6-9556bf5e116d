// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/property.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Status } from "../common-proto/common";
import { Feature, Tax } from "./plan";

export interface PropertySummaryResponse {
  data?: PropertySummary[] | undefined;
}

export interface PropertySummary {
  id?: string | undefined;
  industry?: string | undefined;
  city?: string | undefined;
  province?: string | undefined;
  packageName?: string | undefined;
  startDate?: string | undefined;
  endDate?: string | undefined;
  flag?: string | undefined;
  totalDevice?: string | undefined;
}

export interface GetPropertyInRequest {
  id?: string[] | undefined;
}

export interface PropertyInfo {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  companyEmail?: string | undefined;
  companyPhoneNumber?: string | undefined;
  address?: string | undefined;
  province?: string | undefined;
  city?: string | undefined;
  district?: string | undefined;
  urban?: string | undefined;
  postalCode?: string | undefined;
  postalId?: string | undefined;
  licenseKey?: string | undefined;
  licenseType?: string | undefined;
  industry?: string | undefined;
  flag?: string | undefined;
}

export interface ActivateAllUsedData {
  propertyId?: string | undefined;
  allActivationUsed?: boolean | undefined;
}

export interface GetIsActivateAllUsedResponse {
  data?: ActivateAllUsedData[] | undefined;
}

export interface GetIsActivateAllUsedRequest {
  id?: string[] | undefined;
}

export interface AssignPropertyRequest {
  userId?: string | undefined;
  propertyId?: string | undefined;
}

export interface GetPropertyZoneTypeData {
  id?: string | undefined;
  name?: string | undefined;
}

export interface GetPropertyZoneTypeResponse {
  data?: GetPropertyZoneTypeData[] | undefined;
}

export interface GetPropertyZoneTypeRequest {
  id?: string | undefined;
  search?: string | undefined;
}

export interface GetPropertyBillResponse {
  packages?: GetPropertyBillPackageResponse[] | undefined;
}

export interface GetPropertyBillPackageResponse {
  packageId?: string | undefined;
  type?: string | undefined;
  name?: string | undefined;
  price?: number | undefined;
  status?: string | undefined;
  total?: number | undefined;
  billCycle?: string | undefined;
  renewDue?: string | undefined;
  orderId?: string | undefined;
  taxes?: Tax[] | undefined;
  renewType?: string | undefined;
  sku?: string | undefined;
  features?: Feature[] | undefined;
  qty?: number | undefined;
  itemId?: string | undefined;
}

export interface PropertyServiceClient {
  deleteProperty(request: Id, metadata: Metadata, ...rest: any): Observable<Status>;

  getPropertyBill(request: Id, metadata: Metadata, ...rest: any): Observable<GetPropertyBillResponse>;

  assignProperty(request: AssignPropertyRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getPropertyZoneType(
    request: GetPropertyZoneTypeRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetPropertyZoneTypeResponse>;

  getIsActivateAllUsed(
    request: GetIsActivateAllUsedRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetIsActivateAllUsedResponse>;

  getPropertyIn(request: GetPropertyInRequest, metadata: Metadata, ...rest: any): Observable<PropertySummaryResponse>;
}

export interface PropertyServiceController {
  deleteProperty(request: Id, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status;

  getPropertyBill(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyBillResponse> | Observable<GetPropertyBillResponse> | GetPropertyBillResponse;

  assignProperty(
    request: AssignPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getPropertyZoneType(
    request: GetPropertyZoneTypeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyZoneTypeResponse> | Observable<GetPropertyZoneTypeResponse> | GetPropertyZoneTypeResponse;

  getIsActivateAllUsed(
    request: GetIsActivateAllUsedRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetIsActivateAllUsedResponse> | Observable<GetIsActivateAllUsedResponse> | GetIsActivateAllUsedResponse;

  getPropertyIn(
    request: GetPropertyInRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertySummaryResponse> | Observable<PropertySummaryResponse> | PropertySummaryResponse;
}

export function PropertyServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "deleteProperty",
      "getPropertyBill",
      "assignProperty",
      "getPropertyZoneType",
      "getIsActivateAllUsed",
      "getPropertyIn",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PropertyService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PropertyService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PROPERTY_SERVICE_NAME = "PropertyService";
