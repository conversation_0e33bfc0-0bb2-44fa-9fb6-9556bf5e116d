// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/monitor.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Meta, QueryMap } from "../common-proto/common";

export interface MusicPlayerOverview {
  webCount?: string | undefined;
  atvCount?: string | undefined;
  androidCount?: string | undefined;
  iosCount?: string | undefined;
}

export interface PlayHistoryOverview {
  totalCount?: string | undefined;
  countryCount?: string | undefined;
  songCount?: string | undefined;
}

export interface DeviceOverview {
  activeCount?: string | undefined;
  inactiveCount?: string | undefined;
  total?: string | undefined;
}

export interface ISRCOverview {
  data?: ISRCOverviewList[] | undefined;
}

export interface ISRCOverviewRequest {
  isrc?: string[] | undefined;
}

export interface ISRCOverviewList {
  isrc?: string | undefined;
  count?: string | undefined;
}

export interface SongLibraryOverview {
  songCount?: string | undefined;
  countryCount?: string | undefined;
  artistCount?: string | undefined;
}

export interface DeviceSummary {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  deviceType?: string | undefined;
  deviceId?: string | undefined;
  songTitle?: string | undefined;
  artist?: string | undefined;
  duration?: string | undefined;
  playedTime?: string | undefined;
  uptime?: string | undefined;
  userName?: string | undefined;
  password?: string | undefined;
  email?: string | undefined;
  statusAccount?: string | undefined;
}

export interface DeviceSummaryListResponse {
  data?: DeviceSummary[] | undefined;
  meta?: Meta | undefined;
}

export interface PlayHistorySummary {
  id?: string | undefined;
  playedTime?: string | undefined;
  deviceId?: string | undefined;
  songTitle?: string | undefined;
  artist?: string | undefined;
  duration?: string | undefined;
  album?: string | undefined;
  deviceType?: string | undefined;
  uptime?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  zoneName?: string | undefined;
}

export interface PlayHistorySummaryResponse {
  data?: PlayHistorySummary[] | undefined;
  meta?: Meta | undefined;
}

export interface DeviceRegionOverview {
  province?: string | undefined;
  city?: string | undefined;
  urban?: string | undefined;
  district?: string | undefined;
  code?: string | undefined;
  activeCount?: string | undefined;
  inactiveCount?: string | undefined;
  suspendedCount?: string | undefined;
  totalCount?: string | undefined;
}

export interface DeviceRegionOverviewResponse {
  data?: DeviceRegionOverview[] | undefined;
}

export interface MonitorServiceClient {
  getListDevice(request: QueryMap, metadata: Metadata, ...rest: any): Observable<DeviceSummaryListResponse>;

  getDetailDevice(request: Id, metadata: Metadata, ...rest: any): Observable<DeviceSummary>;

  getListPlayHistory(request: QueryMap, metadata: Metadata, ...rest: any): Observable<PlayHistorySummaryResponse>;

  getDetailPlayHistory(request: Id, metadata: Metadata, ...rest: any): Observable<PlayHistorySummary>;

  getMusicPlayerOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<MusicPlayerOverview>;

  getPlayHistoryOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<PlayHistoryOverview>;

  getDeviceRegionOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<DeviceRegionOverviewResponse>;

  getDeviceOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<DeviceOverview>;

  getIsrcOverview(request: ISRCOverviewRequest, metadata: Metadata, ...rest: any): Observable<ISRCOverview>;

  getSongLibraryOverview(request: Empty, metadata: Metadata, ...rest: any): Observable<SongLibraryOverview>;
}

export interface MonitorServiceController {
  getListDevice(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<DeviceSummaryListResponse> | Observable<DeviceSummaryListResponse> | DeviceSummaryListResponse;

  getDetailDevice(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<DeviceSummary> | Observable<DeviceSummary> | DeviceSummary;

  getListPlayHistory(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<PlayHistorySummaryResponse> | Observable<PlayHistorySummaryResponse> | PlayHistorySummaryResponse;

  getDetailPlayHistory(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<PlayHistorySummary> | Observable<PlayHistorySummary> | PlayHistorySummary;

  getMusicPlayerOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<MusicPlayerOverview> | Observable<MusicPlayerOverview> | MusicPlayerOverview;

  getPlayHistoryOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<PlayHistoryOverview> | Observable<PlayHistoryOverview> | PlayHistoryOverview;

  getDeviceRegionOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<DeviceRegionOverviewResponse> | Observable<DeviceRegionOverviewResponse> | DeviceRegionOverviewResponse;

  getDeviceOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<DeviceOverview> | Observable<DeviceOverview> | DeviceOverview;

  getIsrcOverview(
    request: ISRCOverviewRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<ISRCOverview> | Observable<ISRCOverview> | ISRCOverview;

  getSongLibraryOverview(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<SongLibraryOverview> | Observable<SongLibraryOverview> | SongLibraryOverview;
}

export function MonitorServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getListDevice",
      "getDetailDevice",
      "getListPlayHistory",
      "getDetailPlayHistory",
      "getMusicPlayerOverview",
      "getPlayHistoryOverview",
      "getDeviceRegionOverview",
      "getDeviceOverview",
      "getIsrcOverview",
      "getSongLibraryOverview",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("MonitorService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("MonitorService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const MONITOR_SERVICE_NAME = "MonitorService";
