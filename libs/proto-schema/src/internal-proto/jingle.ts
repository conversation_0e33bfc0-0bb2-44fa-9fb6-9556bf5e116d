// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.27.3
// source: internal-proto/jingle.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty } from "../common-proto/common";

export interface GetJingleSchedulerRequest {
  trackIds?: string[] | undefined;
  zoneId?: string | undefined;
  playlistId?: string | undefined;
  isLockMode?: boolean | undefined;
  isCrossfade?: boolean | undefined;
  isPlaynow?: boolean | undefined;
  scheduleId?: string | undefined;
}

export interface TrackJingleStatusRequest {
  title?: string | undefined;
  trackId?: string | undefined;
  status?: string | undefined;
  propertyId?: string | undefined;
}

export interface JingleServiceClient {
  getJingleScheduler(request: GetJingleSchedulerRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  trackJingleStatus(request: TrackJingleStatusRequest, metadata: Metadata, ...rest: any): Observable<Empty>;
}

export interface JingleServiceController {
  getJingleScheduler(
    request: GetJingleSchedulerRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  trackJingleStatus(
    request: TrackJingleStatusRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;
}

export function JingleServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getJingleScheduler", "trackJingleStatus"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("JingleService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("JingleService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const JINGLE_SERVICE_NAME = "JingleService";
