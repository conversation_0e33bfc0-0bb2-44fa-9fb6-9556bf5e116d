// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: internal-proto/track.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id, Meta, QueryMap } from "../common-proto/common";
import { PropertyInfo } from "./property";
import { UserInfo } from "./user";

export interface Track {
  id?: string | undefined;
  title?: string | undefined;
  country?: string | undefined;
  artists?: string[] | undefined;
  album?: string | undefined;
  label?: string | undefined;
  release_date?: string | undefined;
  isrc?: string | undefined;
  duration?: number | undefined;
  gmi_id?: string | undefined;
  playCount?: string | undefined;
  images?: string[] | undefined;
}

export interface ListTrackResponse {
  data?: Track[] | undefined;
  meta?: Meta | undefined;
}

export interface ActivationTrack {
  id?: string | undefined;
  code?: string | undefined;
  zoneType?: string | undefined;
  user?: UserInfo | undefined;
  property?: PropertyInfo | undefined;
}

export interface TrackPlayHistory {
  id?: string | undefined;
  track?: Track | undefined;
  playAt?: string | undefined;
  endAt?: string | undefined;
  duration?: number | undefined;
  ip?: string | undefined;
  os?: string | undefined;
  country?: string | undefined;
  userAgent?: string | undefined;
  activation?: ActivationTrack | undefined;
}

export interface ListTrackPlayHistoryResponse {
  data?: TrackPlayHistory[] | undefined;
  meta?: Meta | undefined;
}

export interface ActivationPlayHistory {
  id?: string | undefined;
  code?: string | undefined;
  zoneType?: string | undefined;
  user?: UserInfo | undefined;
  property?: PropertyInfo | undefined;
  track?: Track | undefined;
  playCount?: string | undefined;
}

export interface ListActivationPlayHistoryResponse {
  data?: ActivationPlayHistory[] | undefined;
  meta?: Meta | undefined;
}

export interface TrackServiceClient {
  listTrack(request: QueryMap, metadata: Metadata, ...rest: any): Observable<ListTrackResponse>;

  detailTrack(request: Id, metadata: Metadata, ...rest: any): Observable<Track>;

  listTrackPlayHistory(request: QueryMap, metadata: Metadata, ...rest: any): Observable<ListTrackPlayHistoryResponse>;

  detailTrackPlayHistory(request: Id, metadata: Metadata, ...rest: any): Observable<TrackPlayHistory>;

  listActivationPlayHistory(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Observable<ListActivationPlayHistoryResponse>;
}

export interface TrackServiceController {
  listTrack(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListTrackResponse> | Observable<ListTrackResponse> | ListTrackResponse;

  detailTrack(request: Id, metadata: Metadata, ...rest: any): Promise<Track> | Observable<Track> | Track;

  listTrackPlayHistory(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListTrackPlayHistoryResponse> | Observable<ListTrackPlayHistoryResponse> | ListTrackPlayHistoryResponse;

  detailTrackPlayHistory(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<TrackPlayHistory> | Observable<TrackPlayHistory> | TrackPlayHistory;

  listActivationPlayHistory(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListActivationPlayHistoryResponse>
    | Observable<ListActivationPlayHistoryResponse>
    | ListActivationPlayHistoryResponse;
}

export function TrackServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "listTrack",
      "detailTrack",
      "listTrackPlayHistory",
      "detailTrackPlayHistory",
      "listActivationPlayHistory",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("TrackService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("TrackService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const TRACK_SERVICE_NAME = "TrackService";
