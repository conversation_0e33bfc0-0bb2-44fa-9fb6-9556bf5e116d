// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: membership-proto/license.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Status } from "../common-proto/common";

export interface SetLicenseRequest {
  id?: string | undefined;
  key?: string | undefined;
  type?: string | undefined;
  requestId?: string | undefined;
}

export interface LicenseServiceClient {
  setPerformingLicense(request: SetLicenseRequest, metadata: Metadata, ...rest: any): Observable<Status>;
}

export interface LicenseServiceController {
  setPerformingLicense(
    request: SetLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;
}

export function LicenseServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["setPerformingLicense"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("LicenseService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("LicenseService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const LICENSE_SERVICE_NAME = "LicenseService";
