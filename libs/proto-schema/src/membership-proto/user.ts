// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: membership-proto/user.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Id } from "../common-proto/common";
import { Property, UserProperty } from "./property";

export interface UserWithPropertyRequest {
  userId?: string | undefined;
  propertyId?: string | undefined;
}

export interface UserData {
  id?: string | undefined;
  username?: string | undefined;
  email?: string | undefined;
  password?: string | undefined;
  provider?: string | undefined;
  mobileNumber?: string | undefined;
  business_type?: string | undefined;
  type?: string | undefined;
  status?: string | undefined;
  is_active?: boolean | undefined;
  refferal_code?: string | undefined;
  parent_id?: string | undefined;
  profile?: ProfileData | undefined;
  is_admin?: boolean | undefined;
  zone?: string | undefined;
}

export interface ProfileData {
  id?: string | undefined;
  first_name?: string | undefined;
  last_name?: string | undefined;
  placeOfBirth?: string | undefined;
  dateOfBirth?: string | undefined;
  gender?: string | undefined;
  address?: string | undefined;
  mediaId?: string | undefined;
}

export interface UserResponse {
  user?: UserData | undefined;
  property?: Property | undefined;
  userProperty?: UserProperty | undefined;
}

export interface UserServiceClient {
  getUserById(request: Id, metadata: Metadata, ...rest: any): Observable<UserResponse>;

  getUserByIdPropertyId(request: UserWithPropertyRequest, metadata: Metadata, ...rest: any): Observable<UserResponse>;
}

export interface UserServiceController {
  getUserById(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;

  getUserByIdPropertyId(
    request: UserWithPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<UserResponse> | Observable<UserResponse> | UserResponse;
}

export function UserServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = ["getUserById", "getUserByIdPropertyId"];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("UserService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const USER_SERVICE_NAME = "UserService";
