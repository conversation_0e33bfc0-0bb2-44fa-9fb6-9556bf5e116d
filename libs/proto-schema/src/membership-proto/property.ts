// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: membership-proto/property.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Meta, Query, QueryMap, Status } from "../common-proto/common";
import { Profile } from "./auth";

export interface PropertyInfo {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  companyEmail?: string | undefined;
  companyPhoneNumber?: string | undefined;
  address?: string | undefined;
  province?: string | undefined;
  city?: string | undefined;
  district?: string | undefined;
  urban?: string | undefined;
  postalCode?: string | undefined;
  postalId?: string | undefined;
  licenseKey?: string | undefined;
  licenseType?: string | undefined;
  industry?: string | undefined;
  flag?: string | undefined;
  owner?: OwnerInfo | undefined;
  users?: UserInfo[] | undefined;
}

export interface OwnerInfo {
  id?: string | undefined;
  username?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  businessType?: string | undefined;
  profile?: Profile | undefined;
  isActive?: boolean | undefined;
}

export interface UserInfo {
  id?: string | undefined;
  username?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  businessType?: string | undefined;
  isAdmin?: boolean | undefined;
  profile?: Profile | undefined;
  isActive?: boolean | undefined;
  status?: string | undefined;
  activationId?: string | undefined;
  zoneName?: string | undefined;
}

export interface ListPropertyResponse {
  data?: PropertyInfo[] | undefined;
  meta?: Meta | undefined;
}

export interface PropertyMonitorOverview {
  totalUserOwner?: number | undefined;
  totalProperty?: number | undefined;
  percentageUsedPromoCode?: number | undefined;
}

export interface GetOverviewPropertyResponse {
  data?: PropertyMonitorOverview | undefined;
}

export interface GetAllPropertyRequest {
  query?: Query | undefined;
}

export interface PropertyMonitorUser {
  id?: string | undefined;
  name?: string | undefined;
}

export interface PropertyMonitorPostal {
  id?: string | undefined;
  city?: string | undefined;
  address?: string | undefined;
}

export interface PropertyMonitor {
  crmPropertyId?: string | undefined;
  groupAdmin?: PropertyMonitorUser | undefined;
  userAdmin?: PropertyMonitorUser[] | undefined;
  postal?: PropertyMonitorPostal | undefined;
  propertyType?: string | undefined;
}

export interface GetAllPropertyResponse {
  data?: PropertyMonitor[] | undefined;
  meta?: Meta | undefined;
}

export interface PropertyUserProfile {
  id?: string | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  placeOfBirth?: string | undefined;
  dateOfBirth?: string | undefined;
  address?: string | undefined;
  gender?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface PropertyUser {
  id?: string | undefined;
  email?: string | undefined;
  username?: string | undefined;
  mobileNumber?: string | undefined;
  password?: string | undefined;
  businessType?: string | undefined;
  profile?: PropertyUserProfile | undefined;
  is_admin?: boolean | undefined;
  zone?: string | undefined;
}

export interface ContactPerson {
  id?: string | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  jobPositionId?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface Property {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  companyEmail?: string | undefined;
  companyPhoneNumber?: string | undefined;
  npwp?: string | undefined;
  address?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  postalId?: string | undefined;
  propertyTypeId?: string | undefined;
  configuration?: ConfigurationOptions | undefined;
  contactPerson?: ContactPerson[] | undefined;
  categoryId?: string | undefined;
  industryPlan?: string | undefined;
  unit?: number | undefined;
  licenseKey?: string | undefined;
  licenseType?: string | undefined;
  requestId?: string | undefined;
  order?: OrderProp[] | undefined;
}

export interface OrderProp {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  status?: string | undefined;
  tag?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  propertyId?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  voucherId?: string | undefined;
  orderPayment?: OrderPaymentProp | undefined;
  details?: OrderDetailProp[] | undefined;
  propertyType?: string | undefined;
  userId?: string | undefined;
}

export interface OrderPaymentProp {
  id?: string | undefined;
  by?: string | undefined;
  url?: string | undefined;
  expiredPayment?: number | undefined;
  status?: string | undefined;
  paymentRequestId?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isPaid?: boolean | undefined;
}

export interface OrderDetailProp {
  id?: string | undefined;
  name?: string | undefined;
  duration?: string | undefined;
  price?: string | undefined;
  totalPrice?: number | undefined;
  tax?: number | undefined;
  discount?: number | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
  qty?: number | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  sku?: string | undefined;
}

export interface PropertyUserRequest {
  propertyId?: string | undefined;
}

export interface PropertyCidRequest {
  cid?: string | undefined;
}

export interface UserProperty {
  id?: string | undefined;
  userId?: string | undefined;
  propertyId?: string | undefined;
  isDefault?: boolean | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
}

export interface ConfigurationOptions {
  industryLicense?: string | undefined;
  industryPlan?: string | undefined;
}

export interface PropertyUserResponse {
  property?: Property | undefined;
  user?: PropertyUser | undefined;
  userProperty?: UserProperty | undefined;
}

export interface PropertyTypeSmall {
  id?: string | undefined;
  name?: string | undefined;
  slug?: string | undefined;
  icon?: string | undefined;
  categoryCode?: string | undefined;
  codeProperty?: string | undefined;
  type?: string | undefined;
}

export interface PropertyWithType {
  id?: string | undefined;
  cid?: string | undefined;
  companyName?: string | undefined;
  brandName?: string | undefined;
  companyEmail?: string | undefined;
  companyPhoneNumber?: string | undefined;
  npwp?: string | undefined;
  address?: string | undefined;
  createdAt?:
    | string
    | undefined;
  /** PropertyTypeSmall propertyType = 11; */
  updatedAt?: string | undefined;
}

export interface SetPropertyFlagRequest {
  id?: string | undefined;
  flag?: string | undefined;
}

export interface ListOrderResponse {
  status?: Status | undefined;
  meta?: Meta | undefined;
  data?: ListAllOrderResponse[] | undefined;
}

export interface ListAllOrderResponse {
  id?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
  status?: string | undefined;
  tag?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  totalPrice?: number | undefined;
  discount?: number | undefined;
  voucherId?: string | undefined;
  orderPayment?: ListOrderPayment | undefined;
  orderDetail?: ListOrderDetail[] | undefined;
  property?: PropertyWithType | undefined;
}

export interface ListOrderPayment {
  id?: string | undefined;
  by?: string | undefined;
  url?: string | undefined;
  expiredPayment?: number | undefined;
  status?: string | undefined;
  paymentRequestId?: string | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  isPaid?: boolean | undefined;
}

export interface ListOrderDetail {
  id?: string | undefined;
  name?: string | undefined;
  duration?: string | undefined;
  price?: string | undefined;
  totalPrice?: number | undefined;
  tax?: number | undefined;
  discount?: number | undefined;
  itemType?: string | undefined;
  itemId?: string | undefined;
  qty?: number | undefined;
  createdAt?: string | undefined;
  updatedAt?: string | undefined;
  sku?: string | undefined;
}

export interface OwnerProperty {
  id?: string | undefined;
  firstName?: string | undefined;
  lastName?: string | undefined;
  email?: string | undefined;
  mobileNumber?: string | undefined;
  dateOfBirth?: string | undefined;
  gender?: string | undefined;
  businessType?: string | undefined;
  propertyIds?: Id[] | undefined;
}

export interface GetListOwnerPropertyResponse {
  status?: Status | undefined;
  meta?: Meta | undefined;
  data?: OwnerProperty[] | undefined;
}

export interface GetOwnerPropertyResponse {
  status?: Status | undefined;
  data?: PropertyWithType[] | undefined;
}

export interface PropertyServiceClient {
  getPropertyUser(request: PropertyUserRequest, metadata: Metadata, ...rest: any): Observable<PropertyUserResponse>;

  activateProperty(request: PropertyUserRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  suspendProperty(request: PropertyUserRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getPropertyByCid(request: PropertyCidRequest, metadata: Metadata, ...rest: any): Observable<PropertyUserResponse>;

  setPropertyFlag(request: SetPropertyFlagRequest, metadata: Metadata, ...rest: any): Observable<Status>;

  getPropertyById(request: Id, metadata: Metadata, ...rest: any): Observable<Property>;

  getPropertyOrder(request: Id, metadata: Metadata, ...rest: any): Observable<ListOrderResponse>;

  getOverviewProperty(request: Empty, metadata: Metadata, ...rest: any): Observable<GetOverviewPropertyResponse>;

  listProperty(request: QueryMap, metadata: Metadata, ...rest: any): Observable<ListPropertyResponse>;

  detailProperty(request: Id, metadata: Metadata, ...rest: any): Observable<PropertyInfo>;

  getListOwnerProperty(request: Query, metadata: Metadata, ...rest: any): Observable<GetListOwnerPropertyResponse>;

  getOwnerProperty(request: Query, metadata: Metadata, ...rest: any): Observable<GetOwnerPropertyResponse>;
}

export interface PropertyServiceController {
  getPropertyUser(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyUserResponse> | Observable<PropertyUserResponse> | PropertyUserResponse;

  activateProperty(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  suspendProperty(
    request: PropertyUserRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getPropertyByCid(
    request: PropertyCidRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyUserResponse> | Observable<PropertyUserResponse> | PropertyUserResponse;

  setPropertyFlag(
    request: SetPropertyFlagRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status;

  getPropertyById(request: Id, metadata: Metadata, ...rest: any): Promise<Property> | Observable<Property> | Property;

  getPropertyOrder(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListOrderResponse> | Observable<ListOrderResponse> | ListOrderResponse;

  getOverviewProperty(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetOverviewPropertyResponse> | Observable<GetOverviewPropertyResponse> | GetOverviewPropertyResponse;

  listProperty(
    request: QueryMap,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPropertyResponse> | Observable<ListPropertyResponse> | ListPropertyResponse;

  detailProperty(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyInfo> | Observable<PropertyInfo> | PropertyInfo;

  getListOwnerProperty(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetListOwnerPropertyResponse> | Observable<GetListOwnerPropertyResponse> | GetListOwnerPropertyResponse;

  getOwnerProperty(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetOwnerPropertyResponse> | Observable<GetOwnerPropertyResponse> | GetOwnerPropertyResponse;
}

export function PropertyServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "getPropertyUser",
      "activateProperty",
      "suspendProperty",
      "getPropertyByCid",
      "setPropertyFlag",
      "getPropertyById",
      "getPropertyOrder",
      "getOverviewProperty",
      "listProperty",
      "detailProperty",
      "getListOwnerProperty",
      "getOwnerProperty",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("PropertyService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = [];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("PropertyService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const PROPERTY_SERVICE_NAME = "PropertyService";
