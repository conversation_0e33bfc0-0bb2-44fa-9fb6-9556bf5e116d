// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.6.1
//   protoc               v5.29.3
// source: jinggle-proto/jinggle.proto

/* eslint-disable */
import { Metadata } from "@grpc/grpc-js";
import { GrpcMethod, GrpcStreamMethod } from "@nestjs/microservices";
import { Observable } from "rxjs";
import { Empty, Id, Meta, Query } from "../common-proto/common";

export interface GetTemporaryJingleTrackData {
  id?: string | undefined;
  title?: string | undefined;
  artist?: string | undefined;
}

export interface GetTemporaryJingleTrackResponse {
  data?: GetTemporaryJingleTrackData[] | undefined;
}

export interface GetCurrentTrackJingleResponse {
  id?: string | undefined;
  name?: string | undefined;
}

export interface GetUrlJinggleRequest {
  playlistId?: string | undefined;
  jingleId?: string | undefined;
  zoneId?: string | undefined;
  scheduleId?: string | undefined;
}

export interface UpdateSequencePlaylistTrackRequest {
  playlistId?: string | undefined;
  trackId?: string | undefined;
  propertyId?: string | undefined;
  sequence?: number | undefined;
}

export interface ZoneTimeData {
  zoneId?: string | undefined;
  timeZone?: string | undefined;
  isPlayed?: boolean | undefined;
}

export interface ZoneGroupWithZoneTimes {
  zoneGroupId?: string | undefined;
  zoneTimes?: ZoneTimeData[] | undefined;
}

export interface UpdateJingleScheduleZoneTimeRequest {
  scheduleId?: string | undefined;
  zones?: ZoneTimeData[] | undefined;
}

export interface CreateJingleScheduleResponse {
  zoneIds?: string[] | undefined;
  trackIds?: string[] | undefined;
  isPlaynow?: boolean | undefined;
}

export interface UpdateCurrentTrackRequest {
  zoneId?: string | undefined;
  trackId?: string | undefined;
  duration?: number | undefined;
  propertyId?: string | undefined;
}

export interface zoneUpdateData {
  zoneId?: string | undefined;
  isPLayed?: boolean | undefined;
}

export interface ZoneGroupWithZones {
  zoneGroupId?: string | undefined;
  data?: zoneUpdateData[] | undefined;
}

export interface UpdateJingleScheduleResponse {
  zoneData?: ZoneGroupWithZones[] | undefined;
  scheduleId?: string | undefined;
}

export interface DeleteJingleScheduleResponse {
  zoneIds?: string[] | undefined;
}

export interface GetQueueRequest {
  timezone?: string | undefined;
  id?: string | undefined;
}

export interface GetQueueResponse {
  playlistData?: PlaylistData[] | undefined;
  isPlaynow?: boolean | undefined;
  ratio?: number | undefined;
  historyJingle?: number | undefined;
  historyJingleUpdate?: number | undefined;
  isLock?: boolean | undefined;
  isCrossfade?: boolean | undefined;
  jingleRemain?: number | undefined;
  isPLayed?: boolean | undefined;
}

export interface UpdateScheduleTimeRequest {
  timezone?: string | undefined;
  id?: string | undefined;
}

export interface DetailJingleScheduleResponse {
  scheduleData?: ScheduleData | undefined;
  playlistData?: PlaylistData[] | undefined;
  zoneData?: ZoneData[] | undefined;
}

export interface DetailJingleZoneResponse {
  zoneIds?: string[] | undefined;
  propertyId?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
}

export interface DetailJinglePlaylistResponse {
  playlistData?: PlaylistData | undefined;
  trackData?: TrackUserData[] | undefined;
}

export interface UpdateJingleScheduleRequest {
  data?: CreateJingleScheduleRequest | undefined;
  jingleDefaultRequest?: JingleDefaultRequest | undefined;
}

export interface UpdateJingleZoneRequest {
  data?: CreateJingleZoneRequest | undefined;
  jingleDefaultRequest?: JingleDefaultRequest | undefined;
}

export interface UpdateJinglePlaylistRequest {
  data?: CreateJinglePlaylistRequest | undefined;
  jingleDefaultRequest?: JingleDefaultRequest | undefined;
}

export interface UpdateJingleRequest {
  userData?: TrackUserData | undefined;
  jingleDefaultRequest?: JingleDefaultRequest | undefined;
}

export interface JingleDefaultRequest {
  id?: string | undefined;
  propertyId?: string | undefined;
}

export interface ListJinglePlaylistReportsResponse {
  data?: HistoryPlaylistData | undefined;
  meta?: Meta | undefined;
}

export interface HistoryPlaylistData {
  name?: string | undefined;
  duration?: string | undefined;
  type?: string | undefined;
  source?: string | undefined;
  playTime?: string | undefined;
  playCount?: string | undefined;
  LastPlayedDate?: string | undefined;
  sizeInfo?: SizeInfo | undefined;
}

export interface IsScheduleAvailableRequest {
  createdAtHistorySong?: number | undefined;
  id?: string | undefined;
  timezone?: string | undefined;
  skipable?: boolean | undefined;
  createdAtCurrentPlay?: number | undefined;
}

export interface JingleReportData {
  playlistName?: string | undefined;
  assignTo?: number | undefined;
  schedule?: number | undefined;
  playCount?: number | undefined;
  playTime?: number | undefined;
  historyPlaylistId?: string | undefined;
}

export interface ListJingleReportsResponse {
  data?: JingleReportData[] | undefined;
  meta?: Meta | undefined;
}

export interface GetReportSumamryResponse {
  totalPlayCount?: number | undefined;
  totalPlayTime?: number | undefined;
  totalTargetDevice?: number | undefined;
  yesterdayTotalPlayCount?: number | undefined;
  yesterdayTotalPlayTime?: number | undefined;
}

export interface GetJingleUrlTrackRatioRequest {
  playlistId?: string[] | undefined;
  scheduleId?: string | undefined;
  zoneId?: string | undefined;
  skipable?: boolean | undefined;
  isPlayed?: boolean | undefined;
}

export interface IsScheduleAvailableResponse {
  isAvailable?: boolean | undefined;
  playlistId?: string[] | undefined;
  scheduleId?: string | undefined;
  ratio?: number | undefined;
  lastPlaylistHistoryCreatedAt?: number | undefined;
  lastPlaylistHistoryUpdatedAt?: number | undefined;
  isPlayed?: boolean | undefined;
  scheduleUpdatedAt?: number | undefined;
  isPlaynow?: boolean | undefined;
  remain?: number | undefined;
}

export interface ListJingleScheduleResponse {
  data?: ScheduleData[] | undefined;
  meta?: Meta | undefined;
}

export interface ScheduleData {
  name?: string | undefined;
  mode?: string | undefined;
  modeValue?: string[] | undefined;
  startTime?: string | undefined;
  endTime?: string | undefined;
  expiredAt?: number | undefined;
  ratio?: number | undefined;
  isLockMode?: boolean | undefined;
  isCrossFade?: boolean | undefined;
  scheduleId?: string | undefined;
  groupDeviceName?: string[] | undefined;
  playlistName?: string[] | undefined;
  isPlaynow?: boolean | undefined;
}

export interface CreateJingleScheduleRequest {
  playlistIds?: string[] | undefined;
  groupDeviceIds?: string[] | undefined;
  name?: string | undefined;
  mode?: string | undefined;
  modeValue?: string[] | undefined;
  startTime?: string | undefined;
  endTime?: string | undefined;
  expiredAt?: number | undefined;
  ratio?: number | undefined;
  isPlaynow?: boolean | undefined;
  isLockMode?: boolean | undefined;
  isCrossFade?: boolean | undefined;
  propertyId?: string | undefined;
  timeZone?: string | undefined;
}

export interface zoneRequest {
  id?: string | undefined;
  deviceName?: string | undefined;
  serialNumber?: string | undefined;
  name?: string | undefined;
}

export interface CreateJingleZoneRequest {
  zoneData?: zoneRequest[] | undefined;
  propertyId?: string | undefined;
  name?: string | undefined;
  description?: string | undefined;
}

export interface ListJingleZoneResponse {
  data?: ZoneData[] | undefined;
  meta?: Meta | undefined;
}

export interface PlaylistData {
  name?: string | undefined;
  totalTrack?: number | undefined;
  description?: string | undefined;
  playlistId?: string | undefined;
}

export interface ZoneData {
  name?: string | undefined;
  totalZone?: number | undefined;
  description?: string | undefined;
  groupZoneId?: string | undefined;
  deviceId?: string | undefined;
  type?: string | undefined;
}

export interface ListJinglePlaylistResponse {
  data?: PlaylistData[] | undefined;
  meta?: Meta | undefined;
}

export interface CreateJinglePlaylistRequest {
  ids?: string[] | undefined;
  name?: string | undefined;
  description?: string | undefined;
}

export interface GetUrlJinggleRespose {
  url?: string | undefined;
  artistName?: string | undefined;
  trackName?: string | undefined;
  duration?: number | undefined;
  isLockMode?: boolean | undefined;
  isCrossFade?: boolean | undefined;
  trackId?: string | undefined;
  isOnSchedule?: boolean | undefined;
  zoneIds?: string[] | undefined;
  startAt?: number | undefined;
  category?: string | undefined;
  status?: string | undefined;
  playlistId?: string | undefined;
  playlistDesc?: string | undefined;
}

export interface ListJingglesResponse {
  data?: TrackUserData[] | undefined;
  meta?: Meta | undefined;
}

export interface CreateTrackFile {
  chunk?: Uint8Array | undefined;
  fileName?: string | undefined;
}

export interface TrackUserData {
  title?: string | undefined;
  artist?: string | undefined;
  propertyId?: string | undefined;
  propertyName?: string | undefined;
  type?: string | undefined;
  category?: string | undefined;
  source?: string | undefined;
  expiredAt?: number | undefined;
  trackId?: string | undefined;
  cid?: string | undefined;
  originalName?: string | undefined;
  duration?: number | undefined;
  deviceName?: string | undefined;
  serialNumber?: string | undefined;
  createdAt?: string | undefined;
  sizeInfo?: SizeInfo | undefined;
  sequence?: number | undefined;
  status?: string | undefined;
}

export interface SizeInfo {
  size?: number | undefined;
  type?: string | undefined;
}

export interface CreateTrackRequest {
  file?: CreateTrackFile | undefined;
  data?: TrackUserData | undefined;
}

export interface VelodivaServiceClient {
  createTrack(request: Observable<CreateTrackRequest>, metadata: Metadata, ...rest: any): Observable<Empty>;

  listJinggles(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingglesResponse>;

  getUrlJinggle(request: GetUrlJinggleRequest, metadata: Metadata, ...rest: any): Observable<GetUrlJinggleRespose>;

  deleteJingle(request: JingleDefaultRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  updateJingle(request: UpdateJingleRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  detailJingle(request: JingleDefaultRequest, metadata: Metadata, ...rest: any): Observable<TrackUserData>;

  updateCurrentTrack(request: UpdateCurrentTrackRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  listAllJingles(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingglesResponse>;

  suspendJingleTrack(request: Id, metadata: Metadata, ...rest: any): Observable<Empty>;

  unsuspendJingleTrack(request: Id, metadata: Metadata, ...rest: any): Observable<Empty>;

  getTemporaryJingleTrack(request: Id, metadata: Metadata, ...rest: any): Observable<GetTemporaryJingleTrackResponse>;

  createJinglePlaylist(request: CreateJinglePlaylistRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  listJinglePlaylist(request: Query, metadata: Metadata, ...rest: any): Observable<ListJinglePlaylistResponse>;

  deleteJinglePlaylist(request: JingleDefaultRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  updateJinglePlaylist(request: UpdateJinglePlaylistRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  detailJinglePlaylist(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<DetailJinglePlaylistResponse>;

  updateSequencePlaylistTrack(
    request: UpdateSequencePlaylistTrackRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Empty>;

  createJingleZone(request: CreateJingleZoneRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  listJingleZone(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingleZoneResponse>;

  deleteJingleZone(request: JingleDefaultRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  updateJingleZone(request: UpdateJingleZoneRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  detailJingleZone(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<DetailJingleZoneResponse>;

  createJingleSchedule(
    request: CreateJingleScheduleRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<CreateJingleScheduleResponse>;

  listJingleSchedule(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingleScheduleResponse>;

  deleteJingleSchedule(request: Id, metadata: Metadata, ...rest: any): Observable<DeleteJingleScheduleResponse>;

  updateJingleSchedule(
    request: UpdateJingleScheduleRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<UpdateJingleScheduleResponse>;

  detailJingleSchedule(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<DetailJingleScheduleResponse>;

  updateScheduleTime(request: UpdateScheduleTimeRequest, metadata: Metadata, ...rest: any): Observable<Empty>;

  updateJingleScheduleZoneTime(
    request: UpdateJingleScheduleZoneTimeRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<Empty>;

  isScheduleAvailable(
    request: IsScheduleAvailableRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<IsScheduleAvailableResponse>;

  getJingleUrlTrackRatio(
    request: GetJingleUrlTrackRatioRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetUrlJinggleRespose>;

  getReportSumamry(request: Id, metadata: Metadata, ...rest: any): Observable<GetReportSumamryResponse>;

  listJingleReports(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingleReportsResponse>;

  listJingleScheduleReports(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingleScheduleResponse>;

  listJingleZoneReports(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingleZoneResponse>;

  listJingleTrackReports(request: Query, metadata: Metadata, ...rest: any): Observable<ListJingglesResponse>;

  listJinglePlaylistReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Observable<ListJinglePlaylistReportsResponse>;

  getJingleQueue(request: GetQueueRequest, metadata: Metadata, ...rest: any): Observable<GetQueueResponse>;

  getCurrentTrackJingle(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Observable<GetCurrentTrackJingleResponse>;
}

export interface VelodivaServiceController {
  createTrack(
    request: Observable<CreateTrackRequest>,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  listJinggles(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingglesResponse> | Observable<ListJingglesResponse> | ListJingglesResponse;

  getUrlJinggle(
    request: GetUrlJinggleRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetUrlJinggleRespose> | Observable<GetUrlJinggleRespose> | GetUrlJinggleRespose;

  deleteJingle(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  updateJingle(
    request: UpdateJingleRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  detailJingle(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<TrackUserData> | Observable<TrackUserData> | TrackUserData;

  updateCurrentTrack(
    request: UpdateCurrentTrackRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  listAllJingles(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingglesResponse> | Observable<ListJingglesResponse> | ListJingglesResponse;

  suspendJingleTrack(request: Id, metadata: Metadata, ...rest: any): Promise<Empty> | Observable<Empty> | Empty;

  unsuspendJingleTrack(request: Id, metadata: Metadata, ...rest: any): Promise<Empty> | Observable<Empty> | Empty;

  getTemporaryJingleTrack(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetTemporaryJingleTrackResponse>
    | Observable<GetTemporaryJingleTrackResponse>
    | GetTemporaryJingleTrackResponse;

  createJinglePlaylist(
    request: CreateJinglePlaylistRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  listJinglePlaylist(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJinglePlaylistResponse> | Observable<ListJinglePlaylistResponse> | ListJinglePlaylistResponse;

  deleteJinglePlaylist(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  updateJinglePlaylist(
    request: UpdateJinglePlaylistRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  detailJinglePlaylist(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<DetailJinglePlaylistResponse> | Observable<DetailJinglePlaylistResponse> | DetailJinglePlaylistResponse;

  updateSequencePlaylistTrack(
    request: UpdateSequencePlaylistTrackRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  createJingleZone(
    request: CreateJingleZoneRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  listJingleZone(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingleZoneResponse> | Observable<ListJingleZoneResponse> | ListJingleZoneResponse;

  deleteJingleZone(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  updateJingleZone(
    request: UpdateJingleZoneRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  detailJingleZone(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<DetailJingleZoneResponse> | Observable<DetailJingleZoneResponse> | DetailJingleZoneResponse;

  createJingleSchedule(
    request: CreateJingleScheduleRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<CreateJingleScheduleResponse> | Observable<CreateJingleScheduleResponse> | CreateJingleScheduleResponse;

  listJingleSchedule(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingleScheduleResponse> | Observable<ListJingleScheduleResponse> | ListJingleScheduleResponse;

  deleteJingleSchedule(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<DeleteJingleScheduleResponse> | Observable<DeleteJingleScheduleResponse> | DeleteJingleScheduleResponse;

  updateJingleSchedule(
    request: UpdateJingleScheduleRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<UpdateJingleScheduleResponse> | Observable<UpdateJingleScheduleResponse> | UpdateJingleScheduleResponse;

  detailJingleSchedule(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<DetailJingleScheduleResponse> | Observable<DetailJingleScheduleResponse> | DetailJingleScheduleResponse;

  updateScheduleTime(
    request: UpdateScheduleTimeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  updateJingleScheduleZoneTime(
    request: UpdateJingleScheduleZoneTimeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> | Observable<Empty> | Empty;

  isScheduleAvailable(
    request: IsScheduleAvailableRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<IsScheduleAvailableResponse> | Observable<IsScheduleAvailableResponse> | IsScheduleAvailableResponse;

  getJingleUrlTrackRatio(
    request: GetJingleUrlTrackRatioRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetUrlJinggleRespose> | Observable<GetUrlJinggleRespose> | GetUrlJinggleRespose;

  getReportSumamry(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetReportSumamryResponse> | Observable<GetReportSumamryResponse> | GetReportSumamryResponse;

  listJingleReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingleReportsResponse> | Observable<ListJingleReportsResponse> | ListJingleReportsResponse;

  listJingleScheduleReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingleScheduleResponse> | Observable<ListJingleScheduleResponse> | ListJingleScheduleResponse;

  listJingleZoneReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingleZoneResponse> | Observable<ListJingleZoneResponse> | ListJingleZoneResponse;

  listJingleTrackReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListJingglesResponse> | Observable<ListJingglesResponse> | ListJingglesResponse;

  listJinglePlaylistReports(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListJinglePlaylistReportsResponse>
    | Observable<ListJinglePlaylistReportsResponse>
    | ListJinglePlaylistReportsResponse;

  getJingleQueue(
    request: GetQueueRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetQueueResponse> | Observable<GetQueueResponse> | GetQueueResponse;

  getCurrentTrackJingle(
    request: JingleDefaultRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetCurrentTrackJingleResponse> | Observable<GetCurrentTrackJingleResponse> | GetCurrentTrackJingleResponse;
}

export function VelodivaServiceControllerMethods() {
  return function (constructor: Function) {
    const grpcMethods: string[] = [
      "listJinggles",
      "getUrlJinggle",
      "deleteJingle",
      "updateJingle",
      "detailJingle",
      "updateCurrentTrack",
      "listAllJingles",
      "suspendJingleTrack",
      "unsuspendJingleTrack",
      "getTemporaryJingleTrack",
      "createJinglePlaylist",
      "listJinglePlaylist",
      "deleteJinglePlaylist",
      "updateJinglePlaylist",
      "detailJinglePlaylist",
      "updateSequencePlaylistTrack",
      "createJingleZone",
      "listJingleZone",
      "deleteJingleZone",
      "updateJingleZone",
      "detailJingleZone",
      "createJingleSchedule",
      "listJingleSchedule",
      "deleteJingleSchedule",
      "updateJingleSchedule",
      "detailJingleSchedule",
      "updateScheduleTime",
      "updateJingleScheduleZoneTime",
      "isScheduleAvailable",
      "getJingleUrlTrackRatio",
      "getReportSumamry",
      "listJingleReports",
      "listJingleScheduleReports",
      "listJingleZoneReports",
      "listJingleTrackReports",
      "listJinglePlaylistReports",
      "getJingleQueue",
      "getCurrentTrackJingle",
    ];
    for (const method of grpcMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcMethod("VelodivaService", method)(constructor.prototype[method], method, descriptor);
    }
    const grpcStreamMethods: string[] = ["createTrack"];
    for (const method of grpcStreamMethods) {
      const descriptor: any = Reflect.getOwnPropertyDescriptor(constructor.prototype, method);
      GrpcStreamMethod("VelodivaService", method)(constructor.prototype[method], method, descriptor);
    }
  };
}

export const VELODIVA_SERVICE_NAME = "VelodivaService";
