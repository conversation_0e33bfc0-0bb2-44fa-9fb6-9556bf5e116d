import { Readable } from 'stream';

export function bufferToStreamChunks(
  buffer: Buffer,
  chunkSize: number,
): Readable {
  let offset = 0;
  return new Readable({
    read(size) {
      if (offset < buffer.length) {
        const chunk = buffer.subarray(
          offset,
          Math.min(offset + chunkSize, buffer.length),
        );
        this.push(chunk);
        offset += chunk.length;
      } else {
        this.push(null); // Signal the end of the stream
      }
    },
  });
}
