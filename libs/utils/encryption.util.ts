import * as crypto from 'crypto';

const secretKey = crypto
  .createHash('sha256')
  .update(process.env.PATH_KEY || 'bPLY}4l<>kX-$B?fG|1:w<WyQiE[&]dC')
  .digest();

export function encrypt(algorithm: string, text: string): string {
  const iv = crypto.randomBytes(12);
  const cipher = crypto.createCipheriv(
    algorithm,
    secretKey,
    iv,
  ) as crypto.CipherGCM;

  const encrypted = Buffer.concat([
    cipher.update(text, 'utf8'),
    cipher.final(),
  ]);
  const authTag = cipher.getAuthTag();

  return [
    iv.toString('hex'),
    authTag.toString('hex'),
    encrypted.toString('hex'),
  ].join(':');
}

export function isValidEncryptedFormat(hash: string): boolean {
  const parts = hash.split(':');
  return (
    parts.length === 3 && parts.every((part) => /^[0-9a-fA-F]+$/.test(part))
  );
}

export function decrypt(algorithm: string, hash: string): string {
  const [ivHex, tagHex, encryptedHex] = hash.split(':');

  const iv = Buffer.from(ivHex, 'hex');
  const tag = Buffer.from(tagHex, 'hex');
  const encryptedText = Buffer.from(encryptedHex, 'hex');

  const decipher = crypto.createDecipheriv(
    algorithm,
    secretKey,
    iv,
  ) as crypto.DecipherGCM;
  decipher.setAuthTag(tag);

  const decrypted = Buffer.concat([
    decipher.update(encryptedText),
    decipher.final(),
  ]);

  return decrypted.toString('utf8');
}
