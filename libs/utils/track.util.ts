import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function updateTrackTsv() {
  try {
    const tracks = await prisma.track.findMany({
      where: { tsv: null },
      orderBy: { createdAt: 'desc' },
    });

    if (tracks.length === 0) {
      console.log('No tracks need updating.');
      return;
    }

    for (const track of tracks) {
      const source = track.source as any;
      const tsv = `${source.title} ${source.artists.map((a: any) => a.name).join(' ')} ${source.album.name}`;

      await prisma.track.update({
        where: { id: track.id },
        data: { tsv }
      });
    }

    console.log('Data tsv updated successfully!');
  } catch (error) {
    console.error('Error updating data tsv:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTrackTsv();