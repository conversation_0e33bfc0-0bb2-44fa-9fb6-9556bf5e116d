import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsUniquePlaylistTrackConstraint
  implements ValidatorConstraintInterface
{
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const propertyName = args.constraints[0];
    const relatedValue = (args.object as any)[propertyName];
    const check = await this.prisma.trackOnPlaylist.findFirst({
      where: {
        AND: [{ track: { key: value } }, { playlist: { id: relatedValue } }],
      },
    });

    if (check) {
      return false;
    }
    return true;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `Track already exist on playlist`;
  }
}

export function IsUniquePlaylistTrack(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsUniquePlaylistTrack',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsUniquePlaylistTrackConstraint,
    });
  };
}
