import { Injectable } from '@nestjs/common';
import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
  ValidationOptions,
} from 'class-validator';

interface IsExistOptions {
  model: string; // The name of the model to check
  field?: string; // The field to check against (default is 'id')
}

@ValidatorConstraint({ async: true })
@Injectable()
export class IsExistConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}

  async validate(value: any, args: ValidationArguments): Promise<boolean> {
    const { model, field = 'id' }: IsExistOptions = args.constraints[0];

    // If value is an array, check each id
    if (Array.isArray(value)) {
      const checks = await Promise.all(
        value.map(async (id) => {
          const record = await this.prisma[model].findFirst({
            where: { [field]: id },
          });
          return !!record; // Return true if found
        }),
      );
      return checks.every(Boolean); // Return true if all ids are found
    } else {
      // If it's a single value
      const record = await this.prisma[model].findFirst({
        where: { [field]: value },
      });
      return !!record; // Return true if found
    }
  }

  defaultMessage(args: ValidationArguments): string {
    return `${args.property} does not exist`;
  }
}

// Decorator function to use the IsExistConstraint
export function IsExist(
  options: IsExistOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'isExist',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [options],
      validator: IsExistConstraint,
    });
  };
}
