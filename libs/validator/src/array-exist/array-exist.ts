import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from 'libs/prisma';

@ValidatorConstraint({ async: true })
export class ArrayExistConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    if (!Array.isArray(value)) return false;
    const { model, field = 'id' }: IsArrayExistOptions = args.constraints[0];

    for (let idx = 0; idx < value.length; idx++) {
      const element = value[idx];
      const check = await this.prisma[model as string].findFirst({
        where: { [field]: element },
      });
      if (check) {
        return true;
      }
      return false;
    }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} not exist`;
  }
}

export function IsArrayExist(
  property: IsArrayExistOptions,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsArrayExist',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: ArrayExistConstraint,
    });
  };
}

export type IsArrayExistOptions = {
  model: string;
  field?: string;
};
