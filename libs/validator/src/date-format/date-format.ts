import { dayjs } from '@app/common';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint()
export class DateFormatConstraint implements ValidatorConstraintInterface {
  validate(value: any, args?: ValidationArguments): boolean | Promise<boolean> {
    const frm = args.constraints[0];
    const validate = dayjs(value, frm, true).isValid();
    if (validate) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} invalid format date`;
  }
}

export function DateFormat(
  property: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'DateFormat',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: DateFormatConstraint,
    });
  };
}
