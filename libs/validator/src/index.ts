import { ArrayExistConstraint } from './array-exist/array-exist';
import { ArrayOnceConstraint } from './array-once/array-once';
import { DateFormatConstraint } from './date-format/date-format';
import { IsExistConstraint } from './is-exist/is-exist';
import { IsPhoneConstraint } from './is-phone/is-phone';
import { IsUniqueAdminPropertyConstraint } from './is-unique-admin-property';
import { IsUniqueCommercialPropertyConstraint } from './is-unique-commercial-property/is-unique-commercial-property';
import { IsUniqueMeConstraint } from './is-unique-me/is-unique-me';
import { IsUniquePlaylistTrackConstraint } from './is-unique-track-playlist';
import { IsUniqueConstraint } from './is-unique/is-unique';
import { IsUsedConstraint } from './is-used/is-used';
import { MatchConstraint } from './match/match';
import {
  IsCityConstraint,
  IsDistrictConstraint,
  IsUrbanConstraint,
  IsZipCodeConstraint,
} from './zip-code';
import { IsStartDateBeforeEndDate } from '@validator/validator/start-end-date/start-end-date';
import { IsUniqueParentConstraint } from '@validator/validator/is-unique-parentId/is-unique-parentId';

export * from './array-exist/array-exist';
export * from './array-length';
export * from './array-once/array-once';
export * from './date-format/date-format';
export * from './is-exist/is-exist';
export * from './is-phone/is-phone';
export * from './is-unique-admin-property';
export * from './is-unique-commercial-property/is-unique-commercial-property';
export * from './is-unique-me/is-unique-me';
export * from './is-unique-track-playlist';
export * from './is-unique/is-unique';
export * from './is-used/is-used';
export * from './zip-code';

export const ValidatorProviders = [
  ArrayExistConstraint,
  ArrayOnceConstraint,
  DateFormatConstraint,
  IsExistConstraint,
  IsUniqueMeConstraint,
  IsUniqueConstraint,
  IsUsedConstraint,
  MatchConstraint,
  IsUniqueCommercialPropertyConstraint,
  IsPhoneConstraint,
  IsUniquePlaylistTrackConstraint,
  IsCityConstraint,
  IsDistrictConstraint,
  IsUrbanConstraint,
  IsZipCodeConstraint,
  IsUniqueAdminPropertyConstraint,
  IsStartDateBeforeEndDate,
  IsUniqueParentConstraint,
];
