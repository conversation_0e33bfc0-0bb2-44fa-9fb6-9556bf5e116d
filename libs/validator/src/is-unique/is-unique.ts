import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsUniqueConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const {
      model,
      field = args.property,
      ignore = false,
      keyIgnore = 'id',
    } = args.constraints[0];

    const context = args.object['context'];

    const item = await this.prisma[model as string].findFirst({
      where: { [field]: value },
    });
    if (!item) {
      return true;
    } else {
      if (ignore && item[keyIgnore] == context.params[keyIgnore]) {
        return true;
      } else {
        return false;
      }
    }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} must be unique`;
  }
}

export function IsUnique(
  property: IsUniqueProperty,
  validationOptions?: ValidationOptions,
) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsUnique',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsUniqueConstraint,
    });
  };
}

export type IsUniqueProperty = {
  model: string;
  field?: string;
  ignore?: boolean;
  keyIgnore?: string;
};
