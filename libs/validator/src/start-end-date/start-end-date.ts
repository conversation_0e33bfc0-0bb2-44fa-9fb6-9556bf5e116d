import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ name: 'isStartDateBeforeEndDate', async: false })
export class IsStartDateBeforeEndDate implements ValidatorConstraintInterface {
  validate(startDate: any, args: ValidationArguments) {
    const endDate = args.object['endDate'];
    return startDate < endDate;
  }

  defaultMessage(args: ValidationArguments) {
    return 'Start date cannot exceed the end date';
  }
}
