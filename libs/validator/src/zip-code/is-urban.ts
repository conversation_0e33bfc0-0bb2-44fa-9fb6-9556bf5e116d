import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsUrbanConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const provinceId = (args.object as any)['provinceId'];
    const city = (args.object as any)['city'];
    const district = (args.object as any)['district'];

    const check = await this.prisma.postal.findFirst({
      where: {
        AND: [
          { provinces: { id: provinceId } },
          { city: city },
          { district: district },
          { urban: value },
        ],
      },
    });

    if (check) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `urban not exist`;
  }
}

export function IsUrban(validationOptions?: ValidationOptions) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsUrban',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: IsUrbanConstraint,
    });
  };
}
