import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsDistrictConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const provinceId = (args.object as any)['provinceId'];
    const city = (args.object as any)['city'];

    const check = await this.prisma.postal.findFirst({
      where: {
        AND: [
          { provinces: { id: provinceId } },
          { city: city },
          { district: value },
        ],
      },
    });

    if (check) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `district not exist`;
  }
}

export function IsDistrict(validationOptions?: ValidationOptions) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsDistrict',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: IsDistrictConstraint,
    });
  };
}
