import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from 'libs/prisma';

@ValidatorConstraint({ async: true })
export class IsZipCodeConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const provinceId = (args.object as any)['provinceId'];
    const city = (args.object as any)['city'];
    const district = (args.object as any)['district'];
    const urban = (args.object as any)['urban'];

    const check = await this.prisma.postal.findFirst({
      where: {
        AND: [
          { provinces: { id: provinceId } },
          { city: city },
          { district: district },
          { urban: urban },
          { id: value },
        ],
      },
    });

    if (check) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `zip code not exist`;
  }
}

export function IsZipCode(validationOptions?: ValidationOptions) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsZipCode',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: IsZipCodeConstraint,
    });
  };
}
