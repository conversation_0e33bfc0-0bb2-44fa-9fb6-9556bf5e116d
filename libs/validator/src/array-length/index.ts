import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from 'libs/prisma';

@ValidatorConstraint({ async: true })
export class IsArrayLengthConstraint implements ValidatorConstraintInterface {
  constructor(private readonly prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    if (!Array.isArray(value)) return false;
    const keyRelated = args.constraints[0];
    const key = args.object[keyRelated];
    if (value.length == key) {
      return true;
    }
    return false;
  }
  defaultMessage?(args?: ValidationArguments): string {
    const keyRelated = args.constraints[0];
    const key = args.object[keyRelated];
    return `${args.property} must be ${key} item`;
  }
}

export function IsArrayLength(
  key: string,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'IsArrayLength',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [key],
      options: validationOptions,
      validator: IsArrayLengthConstraint,
    });
  };
}
