import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint()
export class ArrayOnceConstraint implements ValidatorConstraintInterface {
  validate(value: any, args?: ValidationArguments): boolean | Promise<boolean> {
    if (!Array.isArray(value)) return false;
    const { key, val } = args.constraints[0];
    let counter = 0;
    for (let idx = 0; idx < value.length; idx++) {
      const element = value[idx];
      if (typeof element == 'object') {
        if (element[key] == val) {
          counter = counter + 1;
        }
      } else {
        if (element == val) {
          counter = counter + 1;
        }
      }
    }

    if (counter != 1) {
      return false;
    }
    return true;
  }
  defaultMessage?(args?: ValidationArguments): string {
    const { key, val } = args.constraints[0];
    return `${args.property} ${key ? `${key}:` : ''}${val} can only be one`;
  }
}

export function ArrayOnce(
  property: IArrayOnceProperty,
  validationOptions?: ValidationOptions,
) {
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'ArrayOnce',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: ArrayOnceConstraint,
    });
  };
}

type IArrayOnceProperty = {
  key?: string;
  val: any;
};
