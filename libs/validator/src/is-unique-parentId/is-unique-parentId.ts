import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsUniqueParentConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}

  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const {
      model,
      field = args.property,
      parentIdField = 'parentId',
      ignore = false,
      keyIgnore = 'id',
    } = args.constraints[0];

    const context = args.object;

    const parentId = context[parentIdField];

    const item = await this.prisma[model as string].findFirst({
      where: {
        [field]: value,
        [parentIdField]: parentId,
      },
    });

    if (!item) {
      return true;
    } else {
      if (ignore && item[keyIgnore] === context[keyIgnore]) {
        return true;
      } else {
        return false;
      }
    }
  }

  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} must be unique within the same ${args.constraints[0].parentIdField}`;
  }
}

export function IsUniqueWithinParentId(
  property: IsUniqueProperty,
  validationOptions?: ValidationOptions,
) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsUnique',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsUniqueParentConstraint,
    });
  };
}

export type IsUniqueProperty = {
  model: string;
  field?: string;
  parentIdField?: string;
  ignore?: boolean;
  keyIgnore?: string;
};
