import { PrismaService } from 'libs/prisma';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';

@ValidatorConstraint({ async: true })
export class IsUniqueCommercialPropertyConstraint
  implements ValidatorConstraintInterface
{
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const {
      model,
      field = args.property,
      ignore = false,
      keyIgnore = 'id',
    } = args.constraints[0];
    const context = args.object['context'];

    const item = await this.prisma[model as string].findFirst({
      where: { [field]: value },
    });

    if (!item) {
      return true;
    } else {
      if (ignore && item[keyIgnore] == context.user['propertyId']) {
        return true;
      } else {
        return false;
      }
    }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} must be unique`;
  }
}

export function IsUniqueCommercialProperty(
  property: IsUniqueCommercialProperty,
  validationOptions?: ValidationOptions,
) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsUniqueCommercialProperty',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsUniqueCommercialPropertyConstraint,
    });
  };
}

export type IsUniqueCommercialProperty = {
  model: string;
  field?: string;
  ignore?: boolean;
  keyIgnore?: string;
};
