import { Controller, Get, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { GetPropertyQuery, GetPropertiesQuery } from './queries';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@ApiTags('Property')
@Controller('property')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class PropertyController {
  constructor(private queryBus: QueryBus) {}

  @Get()
  @ApiOperation({ summary: 'get detail property' })
  findOne(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetPropertyQuery(user));
  }

  @Get('list')
  @ApiOperation({ summary: 'get all properties for current user' })
  findAll(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetPropertiesQuery(user));
  }
}
