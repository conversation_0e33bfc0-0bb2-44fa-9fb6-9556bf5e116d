import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPropertyInQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';

@QueryHandler(GetPropertyInQuery)
export class GetPropertyInHandler implements IQueryHandler<GetPropertyInQuery> {
    constructor(private prisma: PrismaService) { }

    async execute(query: GetPropertyInQuery) {
        const { args } = query;

        try {
            // console.log('Input IDs:', args.id);

            const properties = await this.prisma.property.findMany({
                where: {
                    crmPropertyId: {
                        in: args.id
                    }
                },
                include: {
                    postal: {
                        include: {
                            provinces: true
                        }
                    },
                    propertyType: true,
                    packages: {
                        take: 1,
                        where: {
                            itemType: 'PLAN',
                        },
                        orderBy: {
                            createdAt: 'desc'
                        }
                    },
                    activation: true
                }
            });

            const data = properties.map(property => {
                const totalActivation = property.activation.length;
                const usedActivation = property.activation.filter(act => act.isUsed).length;

                const result = {
                    id: property.crmPropertyId || '',
                    industry: property.propertyType?.name || '',
                    city: property.postal?.city || '',
                    province: property.postal?.provinces?.name || '',
                    packageName: property.packages[0]?.name || '',
                    startDate: property.packages[0]?.activeAt?.toString() || '',
                    endDate: property.packages[0]?.expiredAt?.toString() || '',
                    flag: property.flag || '',
                    totalDevice: totalActivation > 0 ? `${usedActivation}/${totalActivation}` : ''
                };

                return result;
            });

            return { data };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}