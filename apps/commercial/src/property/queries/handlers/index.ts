import { GetPropertyHandler } from './get-property.handler';
import { GetPropertiesHandler } from './get-properties.handler';
import { GetPropertyBillHandler } from './get-property-bill.handler';
import { GetPropertyZoneTypeHandler } from './get-property-zone-type.handler';
import { GetIsActivateAllUsedHandler } from './get-is-activate-all-used.handler';
import { GetPropertyInHandler } from './get-property-in.handler';

export const PropertyQueryHandlers = [
  Get<PERSON><PERSON>tyHand<PERSON>,
  GetPropertiesHandler,
  GetPropertyBillHandler,
  GetPropertyZoneTypeHandler,
  GetIsActivateAllUsedHandler,
  GetPropertyInHandler
];
