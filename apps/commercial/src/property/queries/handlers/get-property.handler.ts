import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetPropertyQuery } from '../impl';

@QueryHandler(GetPropertyQuery)
export class Get<PERSON><PERSON>tyHandler implements IQueryHandler<GetPropertyQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyQuery) {
    const { user } = query;

    if (!user.hasOwnProperty('propertyId') || !user.propertyId) {
      throw new NotFoundException();
    } else {
      const item = await this.prisma.property.findFirst({
        where: { id: user.propertyId },
        include: {
          postal: {include : {provinces: true}}
        }
      });

      if (!item) {
        throw new NotFoundException();
      }
      return item;
    }
  }
}
