import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetIsActivateAllUsedQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { GetIsActivateAllUsedResponse } from '@app/proto-schema/index.internal';

@QueryHandler(GetIsActivateAllUsedQuery)
export class GetIsActivateAllUsedHandler
  implements IQueryHandler<GetIsActivateAllUsedQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(
    query: GetIsActivateAllUsedQuery,
  ): Promise<GetIsActivateAllUsedResponse> {
    const { args } = query;

    const data = await this.prisma.property.findMany({
      where: {
        crmPropertyId: { in: args.id },
      },
      include: {
        activation: true,
      },
    });

    const result = data.map((property) => {
      const allActivationUsed =
        property.activation.length > 0 &&
        property.activation.every((a) => a.isUsed === true);

      return {
        propertyId: property.crmPropertyId,
        allActivationUsed,
      };
    });

    return { data: result };
  }
}
