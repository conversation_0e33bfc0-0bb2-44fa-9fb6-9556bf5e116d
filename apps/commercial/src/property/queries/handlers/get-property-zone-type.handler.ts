import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GetPropertyZoneTypeResponse } from '@app/proto-schema/index.internal';
import { GetPropertyZoneTypeQuery } from '../impl';

@QueryHandler(GetPropertyZoneTypeQuery)
export class GetPropertyZoneTypeHandler
  implements IQueryHandler<GetPropertyZoneTypeQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(
    query: GetPropertyZoneTypeQuery,
  ): Promise<GetPropertyZoneTypeResponse> {
    const { id, search } = query;

    const searchData = search || '';

    const property = await this.prisma.property.findFirst({
      where: { crmPropertyId: id },
      select: { propertyTypeId: true },
    });

    if (!property) {
      throw new RpcException({
        code: 5,
        message: 'Property not found',
      });
    }

    const zonetypeIds = await this.prisma.zoneTypeProperty.findMany({
      where: { propertyTypeId: property.propertyTypeId },
      select: { zoneTypeId: true },
    });

    const data = await this.prisma.zoneType.findMany({
      where: {
        AND: [
          {
            id: {
              in: zonetypeIds.map((z) => z.zoneTypeId),
            },
          },
          searchData
            ? {
                name: { contains: searchData, mode: 'insensitive' },
              }
            : {},
        ],
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!data) {
      throw new RpcException({
        code: 5,
        message: 'Property not found',
      });
    }

    return {
      data,
    };
  }
}
