import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPropertiesQuery } from '../impl';

@QueryHandler(GetPropertiesQuery)
export class GetPropertiesHandler implements IQueryHandler<GetPropertiesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertiesQuery) {
    const { user } = query;

    const properties = await this.prisma.userProperty.findMany({
      where: { userId: user.id },
      include: {
        property: {
          include: {
            postal: {
              include: {
                provinces: true
              }
            }
          }
        }
      }
    });

    return properties.map(up => up.property);
  }
}