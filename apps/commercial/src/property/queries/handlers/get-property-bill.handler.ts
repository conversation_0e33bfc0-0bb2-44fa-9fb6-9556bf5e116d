import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyBillQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { GetPropertyBillResponse } from '@app/proto-schema/index.internal';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Tracer } from '@app/common';

interface PackageDetails {
    name: string;
    price: number;
    taxes: Array<{
        name: string;
        type: string;
        nominal: number;
    }>;
}

@QueryHandler(GetPropertyBillQuery)
export class GetPropertyBillHandler implements IQueryHandler<GetPropertyBillQuery> {
    constructor(private readonly prisma: PrismaService) { }

    @Tracer
    private async calculateTotal(price: number, taxes: Array<{ nominal: number, type?: string }>, qty: number = 1): Promise<number> {
        if (!price || isNaN(price)) {
            return 0;
        }

        let baseTotal = Number(price) * qty;

        if (!Array.isArray(taxes)) {
            return baseTotal;
        }

        let total = baseTotal;
        for (const tax of taxes) {
            if (!tax.nominal || isNaN(tax.nominal)) {
                continue;
            }

            const taxAmount = (baseTotal * Number(tax.nominal)) / 100;

            if (tax.type?.toLowerCase() === 'reduction') {
                total -= taxAmount;
            } else {
                total += taxAmount;
            }
        }

        return Number(total.toFixed(2));
    }

    @Tracer
    private async getPlanDetails(plan: any): Promise<PackageDetails & { sku?: string, features?: any[] }> {
        if (!plan) {
            return { name: '', price: 0, taxes: [], sku: '', features: [] };
        }

        const features = await this.prisma.planAddon.findMany({
            where: { planId: plan.id },
            include: {
                feature: true
            }
        });

        return {
            name: plan.name || '',
            price: Number(plan.basePrice) || 0,
            taxes: plan.taxPlan.map((taxPlan: any) => ({
                name: taxPlan.tax.name,
                type: taxPlan.tax.type || '',
                nominal: Number(taxPlan.tax.nominal),
            })),
            sku: plan.sku?.code || '',
            features: features.map(addon => ({
                featureId: addon.featureId,
                qty: addon.qty,
                feature: {
                    id: addon.feature?.id,
                    name: addon.feature?.name
                }
            }))
        };
    }

    @Tracer
    private async getLicenseDetails(licenseId: string): Promise<PackageDetails & { sku?: string, features?: any[] }> {
        const license = await this.prisma.license.findUnique({
            where: { id: licenseId },
            include: {
                taxLicense: {
                    include: { tax: true },
                },
                sku: true,
            },
        });

        if (!license) {
            return { name: '', price: 0, taxes: [], sku: '', features: [] };
        }

        return {
            name: license.name,
            price: Number(license.price),
            taxes: license.taxLicense.map((taxLicense) => ({
                name: taxLicense.tax.name,
                type: taxLicense.tax.type || '',
                nominal: Number(taxLicense.tax.nominal),
            })),
            sku: license.sku?.code || '',
            features: []
        };
    }

    @Tracer
    async execute(query: GetPropertyBillQuery): Promise<GetPropertyBillResponse> {
        try {
            const { propertyId } = query;

            const property = await this.prisma.property.findFirst({
                where: {
                    crmPropertyId: propertyId,
                    flag: 'PAID'
                }
            });

            if (!property) {
                return { packages: [] };
            }

            const now = BigInt(Date.now());
            const sevenDaysFromNow = now + BigInt(7 * 24 * 60 * 60 * 1000);

            const packages = await this.prisma.package.findMany({
                where: {
                    property: {
                        id: property.id,
                    },
                    isActive: true,
                    isTrial: null,
                    itemType: {
                        in: ['PLAN', 'LICENSE']
                    },
                },
            });

            if (!packages.length) {
                return { packages: [] };
            }

            const packageResponses = await Promise.all(
                packages.map(async (pkg) => {
                    if (pkg.itemType !== 'PLAN' && pkg.itemType !== 'LICENSE') {
                        return null;
                    }

                    const details = pkg.itemType === 'PLAN'
                        ? await this.getPlanDetails(await this.prisma.plan.findUnique({
                            where: { id: pkg.itemId },
                            include: {
                                taxPlan: {
                                    include: { tax: true },
                                },
                                sku: true,
                            },
                        }))
                        : await this.getLicenseDetails(pkg.itemId || '');

                    let renewType = 'SUBSCRIPTION';
                    let renewDue = pkg.expiredAt?.toString() || '';

                    const contractEndAtBigInt = BigInt(pkg.contractEndAt || 0);
                    const expiredAtBigInt = BigInt(pkg.expiredAt || 0);

                    const isContractEndingSoon = contractEndAtBigInt > 0 &&
                        (contractEndAtBigInt < now || contractEndAtBigInt < sevenDaysFromNow);

                    if (isContractEndingSoon || pkg.duration === 'yearly') {
                        renewType = 'CONTRACT';
                        renewDue = pkg.contractEndAt.toString();
                    }

                    const expirationTime = renewType === 'CONTRACT'
                        ? contractEndAtBigInt
                        : expiredAtBigInt;

                    const total = await this.calculateTotal(Number(details.price), details.taxes, pkg.qty || 1);

                    let status = 'active';
                    if (expirationTime <= now) {
                        status = 'expired';
                    } else if (expirationTime > now && expirationTime <= sevenDaysFromNow) {
                        status = 'nearly expired';
                    }

                    const response = {
                        packageId: pkg.id,
                        type: pkg.itemType || '',
                        name: details.name,
                        price: Number(details.price),
                        status,
                        total: parseFloat(total.toString()),
                        billCycle: pkg.duration || '',
                        renewDue,
                        orderId: pkg.orderId || '',
                        taxes: details.taxes.map(tax => ({
                            ...tax,
                            nominal: Number(tax.nominal) || 0
                        })),
                        renewType,
                        sku: details.sku || '',
                        features: details.features || [],
                        qty: pkg.qty || 1,
                        itemId: pkg.itemId || ''
                    };

                    return response;
                })
            );

            return { packages: packageResponses };
        } catch (error) {
            if (error instanceof RpcException) {
                throw error;
            }
            throw new RpcException({
                code: status.INTERNAL,
                message: 'Failed to fetch property bill',
            });
        }
    }
}