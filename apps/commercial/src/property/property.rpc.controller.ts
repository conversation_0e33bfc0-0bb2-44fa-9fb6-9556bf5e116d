import { Id, Status } from '@app/proto-schema/index.common';
import {
  AssignPropertyRequest,
  PROPERTY_SERVICE_NAME,
  PropertyServiceController,
  GetPropertyBillResponse,
  GetPropertyZoneTypeResponse,
  GetPropertyZoneTypeRequest,
  GetIsActivateAllUsedRequest,
  GetIsActivateAllUsedResponse,
  GetPropertyInRequest,
  GetPropertyTypeInResponse,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { AssignPropertyCommand, DeletePropertyCommand } from './commands';
import {
  GetIsActivateAllUsedQuery,
  GetPropertyBillQuery,
  GetPropertyInQuery,
  GetPropertyZoneTypeQuery,
} from './queries/impl';

@Controller()
export class PropertyRpcController implements PropertyServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'deleteProperty')
  deleteProperty(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new DeletePropertyCommand(request.id));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'assignProperty')
  assignProperty(
    request: AssignPropertyRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new AssignPropertyCommand({
        userId: request.userId,
        propertyId: request.propertyId,
      }),
    );
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyBill')
  getPropertyBill(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyBillResponse> {
    return this.queryBus.execute(new GetPropertyBillQuery(request.id));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyZoneType')
  getPropertyZoneType(
    request: GetPropertyZoneTypeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyZoneTypeResponse> {
    return this.queryBus.execute(
      new GetPropertyZoneTypeQuery(request.id, request.search),
    );
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getIsActivateAllUsed')
  getIsActivateAllUsed(
    request: GetIsActivateAllUsedRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetIsActivateAllUsedResponse> {
    return this.queryBus.execute(new GetIsActivateAllUsedQuery(request));
  }

  @GrpcMethod(PROPERTY_SERVICE_NAME, 'getPropertyIn')
  getPropertyIn(
    request: GetPropertyInRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<GetPropertyTypeInResponse> {
    return this.queryBus.execute(new GetPropertyInQuery(request));
  }
}
