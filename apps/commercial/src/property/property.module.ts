import { Module } from '@nestjs/common';
import { PropertyController } from './property.controller';
import { CqrsModule } from '@nestjs/cqrs';
import { PropertyQueryHandlers } from './queries';
import { PropertyCommandHandlers } from './commands';
import { PropertyRpcController } from './property.rpc.controller';

@Module({
  imports: [CqrsModule],
  controllers: [
    // PropertyController,
    PropertyRpcController
  ],
  providers: [...PropertyQueryHandlers, ...PropertyCommandHandlers],
})
export class PropertyModule {}
