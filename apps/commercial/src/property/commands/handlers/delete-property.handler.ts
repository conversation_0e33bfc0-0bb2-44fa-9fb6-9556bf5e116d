import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeletePropertyCommand } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@CommandHandler(DeletePropertyCommand)
export class DeletePropertyHandler
  implements ICommandHandler<DeletePropertyCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeletePropertyCommand) {
    const { id } = command;
    const property = await this.prisma.property.findFirst({
      where: {
        crmPropertyId: id,
      },
    });

    if (!property) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'property not found',
      });
    }

    try {
      await this.prisma.property.update({
        where: { id: property.id },
        data: { status: 'inActive', deletedAt: new Date() },
      });
      return {
        code: 0,
        status: 200,
        message: 'property successfully marked as deleted',
      };
    } catch (err) {
      console.error(err.message);
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Internal Server Error',
      });
    }
  }
}
