import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { AssignPropertyCommand } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { PropertyService } from 'apps/commercial/src/membership-client/property.service';
import { GenderType } from '@prisma/client';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { randomDeviceID } from 'libs/utils/rand-device-id.util';

@CommandHandler(AssignPropertyCommand)
export class AssignPropertyHandler
  implements ICommandHandler<AssignPropertyCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly propertyService: PropertyService,
  ) {}

  async execute(command: AssignPropertyCommand) {
    const { args } = command;

    // Validate and fetch user and property data
    const { userData, propertyData, pkg, availableActivation } =
      await this.validateAndFetchData(args.propertyId);

    try {
      return await this.prisma.$transaction(async (tr) => {
        // Create user
        const user = await this.createUser(tr, userData);

        // Create user property association
        await this.createUserProperty(tr, user, propertyData);

        // Process package features
        await this.processPackageFeatures(
          tr,
          pkg,
          propertyData,
          availableActivation,
        );

        return {
          status: status.OK,
          message: 'Property Assigned',
        };
      });
    } catch (err) {
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Internal Server Error',
      });
    }
  }

  private async validateAndFetchData(propertyId: string) {
    // Fetch user data from property service
    const userData = await this.propertyService
      .getPropertyUser(propertyId)
      .toPromise();

    if (!userData) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'User Property Not Found',
      });
    }

    // Find property in database
    const propertyData = await this.prisma.property.findFirst({
      where: { crmPropertyId: propertyId },
    });

    if (!propertyData) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Property Not Found',
      });
    }

    // Find active package
    const pkg = await this.prisma.package.findFirst({
      where: {
        propertyId: propertyData.id,
        status: 'active',
        expiredAt: { gt: new Date().getTime() },
      },
      include: {
        features: { include: { feature: true } },
      },
    });

    if (!pkg) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'No Active Package found',
      });
    }

    // Find available activation
    const availableActivation = await this.prisma.activation.findFirst({
      where: {
        propertyId: propertyData.id,
        isUsed: false,
      },
    });

    if (!availableActivation) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'No Available Activation found',
      });
    }

    return { userData, propertyData, pkg, availableActivation };
  }

  private async createUser(tr: any, userData: any) {
    return tr.user.create({
      data: {
        email: userData.user.email,
        phoneNumber: userData.user.mobileNumber,
        username: userData.user.username,
        password: userData.user.password,
        crmUserId: userData.user.id,
        type: 'commercial',
        status: 'active',
        provider: 'local',
        profile: {
          create: {
            firstName: userData.user.profile.firstName,
            lastName: userData.user.profile.lastName,
            placeOfBirth: userData.user.profile.placeOfBirth,
            dateOfBirth: userData.user.profile.dateOfBirth
              ? new Date(userData.user.profile.dateOfBirth)
              : undefined,
            gender: userData.user.profile.gender as GenderType,
            address: userData.user.profile.address,
          },
        },
      },
    });
  }

  private async createUserProperty(tr: any, user: any, propertyData: any) {
    return tr.userProperty.create({
      data: {
        user: { connect: { id: user.id } },
        property: { connect: { id: propertyData.id } },
        isDefault: true, // Consider making this dynamic if needed
      },
    });
  }

  private async processPackageFeatures(
    tr: any,
    pkg: any,
    propertyData: any,
    availableActivation: any,
  ) {
    for (const featurePackage of pkg.features) {
      switch (featurePackage.featureId) {
        case FEATURE.PlayerNode:
          await this.handlePlayerNodeFeature(
            tr,
            propertyData,
            availableActivation,
          );
          break;
        case FEATURE.SongQuota:
          await this.handleSongQuotaFeature(tr, propertyData, featurePackage);
          break;
      }
    }
  }

  private async handlePlayerNodeFeature(
    tr: any,
    propertyData: any,
    availableActivation: any,
  ) {
    // Create new device
    const newDevice = await tr.device.create({
      data: {
        serialNumber: randomDeviceID(),
        type: 'web',
      },
    });

    // Create connection
    await tr.connection.create({
      data: {
        deviceId: newDevice.id,
        connectedCount: 0,
      },
    });

    // Create profile device
    await tr.profileDevice.create({
      data: {
        deviceId: newDevice.id,
        name: 'Web Player',
      },
    });

    // Create activation device
    await tr.activationDevice.create({
      data: {
        deviceId: newDevice.id,
        activationId: availableActivation.id,
        activatedAt: new Date(new Date().toUTCString()),
        qty: 0,
      },
    });

    // Mark activation as used
    await tr.activation.update({
      where: { id: availableActivation.id },
      data: { isUsed: true },
    });
  }

  private async handleSongQuotaFeature(
    tr: any,
    propertyData: any,
    featurePackage: any,
  ) {
    await tr.activationDevice.updateMany({
      where: {
        AND: [
          { activation: { propertyId: propertyData.id } },
          { isActive: true },
        ],
      },
      data: { qty: featurePackage.qty },
    });
  }
}
