import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { SetPerformingLicenseCommand } from '../impl';

@CommandHandler(SetPerformingLicenseCommand)
export class SetPerformingLicenseHandler
  implements ICommandHandler<SetPerformingLicenseCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: SetPerformingLicenseCommand) {
    const { args } = command;
    try {
      await this.prisma.property.update({
        where: {
          id: args.id
        },
        data: {
          licenseKey: args.key,
          licenseType: args.type
        }
      })
      return {
        code: status.OK,
        message: 'success set performing license'
      };
    } catch (err) {
      console.log(err);
      throw new RpcException({
        code : status.INTERNAL,
        message: 'fail set performing license'
      })
    }
  }
}
