import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { LicenseController } from './license.controller';
import { LicenseQueryHandlers } from './queries';
import { IntegrationModule } from '../integration/integration.module';
import { LicenseCommandHandlers } from './commands';
import { JwtModule } from '@nestjs/jwt';
import { LinkLicenseJwtStrategy } from '../auth/strategies/link-license-jwt.strategy';

@Module({
  imports: [CqrsModule, IntegrationModule, JwtModule.register({})],
  controllers: [LicenseController],
  providers: [
    ...LicenseQueryHandlers,
    ...LicenseCommandHandlers,
    LinkLicenseJwtStrategy,
  ],
})
export class LicenseModule {}
