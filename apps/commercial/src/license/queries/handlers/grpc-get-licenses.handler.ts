import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from 'libs/prisma';
import { GetLicensesGrpc } from "../impl";
import { ListLicenseResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetLicensesGrpc)
export class GetLicensesGrpcHandler implements IQueryHandler<GetLicensesGrpc> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetLicensesGrpc): Promise<ListLicenseResponse> {
    const { industryId, subfolderId } = query;

    const whereConditions: {
      AND: Array<{ propertyType?: { id: string } } | { subfolder?: { id: string } }>;
    } = {
      AND: [],
    };

    if (industryId) {
      whereConditions.AND.push({ propertyType: { id: industryId } });
    }

    if (subfolderId) {
      whereConditions.AND.push({ subfolder: { id: subfolderId } });
    }

    const items = await this.prisma.license.findMany({
      where: whereConditions,
      include: {
        taxLicense: {
          include: { tax: true }
        },
        propertyType: {
          select: { id: true, name: true, slug: true }
        },
        subfolder: {
          select: { id: true, name: true, slug: true }
        },
        sku: {
          select: {
            id: true,
            productType: true,
            code: true
          }
        }
      }
    });

    const licenses = items.map(license => ({
      id: license.id,
      name: license.name,
      description: license.description,
      price: Number(license.price),
      taxLicense: license.taxLicense.map(tax => ({
        id: tax.tax.id,
        taxId: tax.tax.id,
        licenseId: tax.licenseId
      })),
      paymentStatus: license.paymentStatus,
      paymentFrequency: license.paymentFrequency,
      propertyType: license.propertyType,
      sku: license.sku ? {
        id: license.sku.id,
        code: license.sku.code,
        productType: license.sku.productType
      } : null
    }));

    const groupedLicenses = {
      [industryId || 'all']: {
        license: licenses
      }
    };

    return {
      status: { code: 200, message: 'Success' },
      data: groupedLicenses
    };
  }
}
