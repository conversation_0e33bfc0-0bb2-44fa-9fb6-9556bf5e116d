import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetLicenseExpiredQuery } from '../impl';

@QueryHandler(GetLicenseExpiredQuery)
export class GetLicenseExpiredHandler implements IQueryHandler<GetLicenseExpiredQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetLicenseExpiredQuery) {
    const { args } = query;
    const pkg = await this.prisma.package.findFirst({
      where: {
        AND: [
          {
            itemType: 'LICENSE'
          },
          {
            isActive: true
          },
          {
            status: 'active'
          }
        ]
      },
    });

    if (!pkg) {
      throw new NotFoundException();
    }

    return {
      expiredAt: pkg.contractEndAt
    };
  }
}
