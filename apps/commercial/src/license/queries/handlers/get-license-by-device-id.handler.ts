import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetLicenseByDeviceIdQuery, GetLicenseQuery } from '../impl';
import { IntegrationService } from 'apps/commercial/src/integration/integration.service';

@QueryHandler(GetLicenseByDeviceIdQuery)
export class GetLicenseByDeviceIdHandler
  implements IQueryHandler<GetLicenseByDeviceIdQuery>
{
  constructor(
    private prisma: PrismaService,
    private integrationService: IntegrationService,
  ) {}

  async execute(query: GetLicenseByDeviceIdQuery) {
    const { user } = query;
    let license = null;
    const activationDevice = await this.prisma.activationDevice.findFirst({
      where: {
        AND: [
          {
            activation: {
              id: user.id
            }
          },
          {
            device: {
              AND: [
                {
                  isActive: true,
                },
              ],
            },
          },
          {
            isActive: true,
          },
        ],
      },
      include: {
        device: true,
        activation: {
          include: {
            config: true,
            property: {
              include: {
                postal: {
                  include: {
                    provinces: true,
                  },
                },
                propertyType: true,
              },
            },
          },
        },
      },
    });

    if (!activationDevice) {
      throw new NotFoundException('device not found');
    }

    if (activationDevice.activation.property?.licenseKey) {
      license = await this.integrationService.DlpGetLicense(activationDevice.activation.property?.licenseKey);
    }

    const currentPlaying = await this.prisma.currentPlayer.findFirst({
      where: {
        AND: [
          {
            activation: {
              id: user.id,
            },
          },
          {
            property: {
              id: activationDevice.activation.propertyId,
            },
          },
        ],
      },
      orderBy: {
        updatedAt: 'desc',
      },
      include: {
        track: true,
      },
    });

    const pkgPlan = await this.prisma.package.findFirst({
      where: {
        AND: [
          { property: { id: activationDevice.activation.propertyId } },
          { status: 'active' },
          { itemType: 'PLAN' },
          { isActive: true }
        ],
      },
    });

    const pkgLicense = await this.prisma.package.findFirst({
      where: {
        AND: [
          { property: { id: activationDevice.activation.propertyId } },
          { status: 'active' },
          { itemType: 'LICENSE' },
          { isActive: true }
        ],
      },
    });

    //const playCount = await this.prisma.playHistory.count({
    //  where: { property: { id: activation.activation.propertyId } },
    //});

    const payload = {
      customer: {
        cid: activationDevice.activation.property?.cid,
        companyName: activationDevice.activation.property?.companyName,
        brandName: activationDevice.activation.property?.brandName,
        industry: activationDevice.activation.property.propertyType.name,
        province: activationDevice.activation.property?.postal?.provinces?.name,
        city: activationDevice.activation.property?.postal?.city,
        district: activationDevice.activation.property?.postal?.district,
        zipCode: activationDevice.activation.property?.postal?.code,
        urban: activationDevice.activation.property?.postal?.urban,
        address: activationDevice.activation.property?.address,
        joinAt: activationDevice.activation.property?.createdAt,
      },
      performLicense: {
        licenseId: license?.code !== 'ERR_BAD_REQUEST' ? license?.code : activationDevice.activation.property?.licenseKey ? activationDevice.activation.property?.licenseKey : null,
        type: license?.type ? license?.type : activationDevice.activation.property?.licenseType ? activationDevice.activation.property?.licenseType : null,
        activeAt: license?.createdAt ? new Date(license?.createdAt).getTime() : pkgLicense?.activeAt ? pkgLicense?.activeAt : null,
        contractEndAt: license?.expiredAt ? new Date(license?.expiredAt).getTime() : pkgLicense?.contractEndAt ? pkgLicense.contractEndAt : null,
      },
      device: {
        musicPlayer: 'Velodiva',
        deviceId: activationDevice.device.serialNumber,
        licenseId: activationDevice.activation.licenseKey,
        zone: activationDevice.activation.zone,
        packageName: pkgPlan.name,
        expiredAt: pkgPlan.expiredAt,
        status: pkgPlan.status,
      },
      //playCount: playCount,
      currentPlaying: currentPlaying?.track?.source || null,
      // status: license?.status || null
      // paused: currentPlaying?.paused,
    };
    return payload;
  }
}
