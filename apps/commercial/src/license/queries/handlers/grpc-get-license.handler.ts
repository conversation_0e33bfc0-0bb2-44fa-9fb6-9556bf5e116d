import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { PrismaService } from 'libs/prisma';
import { License } from "@app/proto-schema/index.internal";
import { RpcException } from "@nestjs/microservices";
import { status } from "@grpc/grpc-js";
import { GetLicenseGrpc } from "../impl";

@QueryHandler(GetLicenseGrpc)
export class GetLicenseGrpcHandler implements IQueryHandler<GetLicenseGrpc> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetLicenseGrpc): Promise<License> {
    const { id } = query;

    const license = await this.prisma.license.findFirst({
      where: { subfolderId: id },
      include: {
        taxLicense: {
          include: { tax: true, },
        },
        propertyType: {
          select: { id: true, name: true, slug: true }
        },
        subfolder: {
          select: { id: true, name: true, slug: true }
        },
        sku: {
          select: {
            id: true,
            productType: true,
            code: true
          }
        }
      }
    });

    if (!license) {
      throw new RpcException({ code: status.NOT_FOUND, message: 'License not found' });
    }

    return {
      id: license.id,
      name: license.name,
      description: license.description,
      price: Number(license.price),
      taxLicense: license.taxLicense.map(tax => ({
        id: tax.id,
        taxId: tax.tax.id,
        name: tax.tax.name,
        type: tax.tax.type,
        startDate: String(tax.tax.startDate),
        endDate: String(tax.tax.endDate),
        nominal: Number(tax.tax.nominal),
        description: tax.tax.description,
        licenseId: license.id,
      })),
      paymentStatus: license.paymentStatus,
      paymentFrequency: license.paymentFrequency,
      propertyType: license.propertyType,
      sku: license.sku ? {
        id: license.sku.id,
        code: license.sku.code,
        productType: license.sku.productType
      } : null
    };
  }
}
