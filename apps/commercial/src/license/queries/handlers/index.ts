import { GetLicenseByDeviceIdHandler } from './get-license-by-device-id.handler';
import { GetLicenseHandler } from './get-license.handler';
import { GetLicenseExpiredHandler } from './get-lincense-expired.handler';
import { GetLinkLicenseHandler } from './get-link-license.handler';
import { GetLicenseGrpcHandler } from './grpc-get-license.handler';
import { GetLicensesPropertyGrpcHandler } from './grpc-get-licenses-propertyId.handler';
import { GetLicensesGrpcHandler } from './grpc-get-licenses.handler';

export const LicenseQueryHandlers = [
  GetLicenseHandler,
  GetLicenseByDeviceIdHandler,
  GetLicenseGrpcHandler,
  GetLicensesGrpcHandler,
  GetLicensesPropertyGrpcHandler,
  GetLinkLicenseHandler,
  GetLicenseExpiredHandler
];
