import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetLicensesPropertyIdGrpc } from "../impl"; // Ensure this imports the correct query class
import { PrismaService } from "libs/prisma";
import { ListLicenseResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetLicensesPropertyIdGrpc)
export class GetLicensesPropertyGrpcHandler implements IQueryHandler<GetLicensesPropertyIdGrpc> {
  constructor(private prisma: PrismaService) { }

  async execute(query: GetLicensesPropertyIdGrpc): Promise<ListLicenseResponse> {
    const { propertyTypeId } = query; 
    const whereConditions = {
      propertyTypeId: propertyTypeId, 
      isActive: true 
    };

    const items = await this.prisma.license.findMany({
      where: whereConditions,
      include: {
        taxLicense: {
          include: { tax: true } 
        },
        propertyType: {
          select: { id: true, name: true, slug: true }
        },
        sku: {
          select: {
            id: true,
            productType: true,
            code: true
          }
        }
      }
    });

    const licenses = items.map(license => ({
      id: license.id,
      name: license.name,
      description: license.description,
      price: Number(license.price),
      taxLicense: license.taxLicense.map(tax => ({
        id: tax.tax.id,
        taxId: tax.tax.id,
        licenseId: tax.licenseId,
        type: tax.tax.type,
        startDate: String(tax.tax.startDate),
        endDate: String(tax.tax.endDate),
      })),
      paymentStatus: license.paymentStatus,
      paymentFrequency: license.paymentFrequency,
      propertyType: license.propertyType,
      sku: license.sku ? {
        id: license.sku.id,
        code: license.sku.code,
        productType: license.sku.productType
      } : null
    }));

    const groupedLicenses = {
      [propertyTypeId]: {
        license: licenses
      }
    };

    return {
      status: { code: 200, message: 'Success' },
      data: groupedLicenses
    };
  }
}
