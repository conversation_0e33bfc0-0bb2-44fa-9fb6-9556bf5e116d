import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetLinkLicenseQuery } from '../impl/get-link-license.query';
import { JwtService } from '@nestjs/jwt';
import { NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'libs/prisma';

@QueryHandler(GetLinkLicenseQuery)
export class GetLinkLicenseHandler
  implements IQueryHandler<GetLinkLicenseQuery>
{
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private config: ConfigService,
  ) {}

  async execute(query: GetLinkLicenseQuery): Promise<any> {
    const { id } = query;

    const deviceId = await this.prisma.activation.findFirst({
      where: {
        id,
      },
      select: {
        id: true,
      },
    });

    if (!deviceId) {
      throw new NotFoundException('Device Not Found');
    }

    const item = await this.prisma.setting.findFirst({
      where: { type: 'expired-license-url-token' },
    });

    const option = (item?.option as { time?: string }) ?? {};
    const expiresIn = option.time ?? '15m';

    const [atToken] = await Promise.all([
      this.jwtService.signAsync(
        { id: deviceId.id },
        {
          secret: this.config.get<string>('AT_SECRET'),
          expiresIn,
        },
      ),
    ]);

    return {
      token: atToken,
      link: `${this.config.get<string>('APP_COMMERCIAL_SERVER')}/v1/license/license-device`,
    };
  }
}
