import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetLicenseQuery } from '../impl';

@QueryHandler(GetLicenseQuery)
export class GetLicenseHandler implements IQueryHandler<GetLicenseQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetLicenseQuery) {
    const { args } = query;
    const property = await this.prisma.property.findFirst({
      where: { id: args.id },
      include: {
        postal: { include: { provinces: { select: { name: true } } } },
      },
    });

    if (!property) {
      throw new NotFoundException();
    }

    // TODO : get orders

    //const plans = await this.prisma.order.findMany({
    //  where: {
    //    AND: [{ property: { id: args.id } }, { status: 'paid' }],
    //  },
    //  include: {
    //    details: { include: { feature: true } },
    //    payment: true,
    //    plan: true,
    //  },
    //});

    //const quota = plans.map((plan) => {
    //  const limit = plan.details.filter(
    //    (detail) => detail.feature.id == 'f-004',
    //  );
    //  return {
    //    planId: plan.id,
    //    // plan: plan?.plan?.name,
    //    orderAt: plan.createdAt,
    //    duration: plan.duration,
    //    // limit: convertByType(limit[0].feature.valueType, limit[0].value),
    //    status: 'ACTIVE',
    //  };
    //});

    const playCount = await this.prisma.playHistory.count({
      where: { property: { id: args.id } },
    });

    const currentPlaying = await this.prisma.playHistory.findFirst({
      where: { AND: [{ property: { id: args.id } }, { endAt: null }] },
      include: { track: { select: { source: true } } },
      orderBy: { playAt: 'desc' },
    });

    const payload = {
      cid: property?.cid,
      companyName: property?.companyName,
      brandName: property?.brandName,
      province: property?.postal?.provinces?.name,
      city: property?.postal?.city,
      district: property?.postal?.district,
      zipCode: property?.postal?.code,
      urban: property?.postal?.urban,
      address: property?.address,
      joinAt: property?.createdAt,
      //package: quota,
      playCount: playCount,
      currentPlaying: currentPlaying?.track?.source,
      // paused: currentPlaying?.paused,
    };
    return payload;
  }
}
