import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { FilterLicenseDto } from './dto/filter-license.dto';
import {
  GetLicenseByDeviceIdQuery,
  GetLicenseQuery,
  GetLicenseGrpc,
  GetLicensesGrpc,
  GetLicensesPropertyIdGrpc,
  GetLinkLicenseQuery,
  GetLicenseExpiredQuery,
} from './queries';
import { User } from '../auth/decorator/user.decorator';
import { GrpcMethod } from '@nestjs/microservices';
import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { Id, Status } from '@app/proto-schema/index.common';
import {
  License,
  LICENSE_SERVICE_NAME,
  ListLicenseResponse,
  LicenseRequest,
  SetLicenseRequest,
} from '@app/proto-schema/index.internal';
import { SetPerformingLicenseCommand } from './commands';
import { LinkLicenseJwtGuard } from '../auth/guards/link-license-jwt.guard';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';

@Controller('license')
@ApiTags('License')

// @UseGuards(JwtGuard, StatusSuspendedGuard)
export class LicenseController {
  constructor(private readonly queryBus: QueryBus) {}

  @GrpcMethod(LICENSE_SERVICE_NAME, 'GetLicense')
  getLicense(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<License> | Observable<License> | License {
    return this.queryBus.execute(new GetLicenseGrpc(request.id));
  }

  @GrpcMethod(LICENSE_SERVICE_NAME, 'ListLicense')
  listLicense(
    request: LicenseRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListLicenseResponse>
    | Observable<ListLicenseResponse>
    | ListLicenseResponse {
    return this.queryBus.execute(new GetLicensesGrpc(request.propertyTypeId));
  }

  @GrpcMethod(LICENSE_SERVICE_NAME, 'licenseByPropertyType')
  licenseBypropertyId(
    request: LicenseRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListLicenseResponse>
    | Observable<ListLicenseResponse>
    | ListLicenseResponse {
    return this.queryBus.execute(
      new GetLicensesPropertyIdGrpc(request.propertyTypeId),
    );
  }

  @GrpcMethod(LICENSE_SERVICE_NAME, 'setPerformingLicense')
  setPerformingLicense(
    request: SetLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.queryBus.execute(new SetPerformingLicenseCommand(request));
  }

  //@Get()
  //findAll(@Query() filter: FilterLicenseDto) {
  //  return this.queryBus.execute(new GetLicenseQuery(filter));
  //}

  @Get(':deviceId/get-link')
  getLink(@Param('deviceId') deviceId: string) {
    return this.queryBus.execute(new GetLinkLicenseQuery(deviceId));
  }

  @ApiBearerAuth()
  @UseGuards(LinkLicenseJwtGuard)
  @Get('license-device')
  findByDeviceId(@User() user: { id: string }) {
    return this.queryBus.execute(new GetLicenseByDeviceIdQuery(user));
  }

  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  @Get('expired')
  find(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetLicenseExpiredQuery(user));
  }
}
