import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetJobPositionsQuery } from '../impl';
import { ListJobPositionResponse } from '@app/proto-schema/index.internal';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetJobPositionsQuery)
export class GetJobPositionsHandler implements IQueryHandler<GetJobPositionsQuery> {
  constructor(
    private prisma: PrismaService,
  ) { }

  async execute(query: GetJobPositionsQuery): Promise<ListJobPositionResponse> {
    try {
      const jobPositions = await this.prisma.jobPosition.findMany();

      return {
        status: { code: status.OK, message: 'Success' },
        data: jobPositions.map(position => ({
          id: position.id,
          name: position.name,
          description: position.description || '',
          createdAt: position.createdAt.toISOString(),
          updatedAt: position.updatedAt.toISOString(),
          allowToCopyId: position.allowToCopyId || undefined,
        })),
      };
    } catch (error) {
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Failed to fetch job positions',
      });
    }
  }
}
