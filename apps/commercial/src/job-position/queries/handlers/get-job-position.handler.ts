import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetJobPositionQuery } from '../impl';
import { JobPosition } from '@app/proto-schema/index.internal';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetJobPositionQuery)
export class GetJobPositionHandler implements IQueryHandler<GetJobPositionQuery> {
  constructor(
    private prisma: PrismaService,
  ) { }

  async execute(query: GetJobPositionQuery): Promise<JobPosition> {
    const { id } = query;

    try {
      const position = await this.prisma.jobPosition.findUnique({
        where: { id },
      });

      if (!position) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Job position not found',
        });
      }

      return {
        id: position.id,
        name: position.name,
        description: position.description || '',
        createdAt: position.createdAt.toISOString(),
        updatedAt: position.updatedAt.toISOString(),
        allowToCopyId: position.allowToCopyId || undefined,
      };
    } catch (error) {
      if (error instanceof RpcException) {
        throw error;
      }
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Failed to fetch job position',
      });
    }
  }
}
