import { Empty, Id, } from '@app/proto-schema/index.common';
import {
    JOB_POSITION_SERVICE_NAME,
    JobPosition,
    ListJobPositionResponse,
    JobPositionServiceController
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { GetJobPositionQuery, GetJobPositionsQuery } from './queries';

@Controller()
export class JobPositionRpcController implements JobPositionServiceController {
    constructor(private readonly queryBus: QueryBus) { }

    @GrpcMethod(JOB_POSITION_SERVICE_NAME, 'listJobPosition')
    listJobPosition(request: Empty, metadata: Metadata, ...rest: any): Promise<ListJobPositionResponse> | Observable<ListJobPositionResponse> | ListJobPositionResponse {
        return this.queryBus.execute(new GetJobPositionsQuery(request));
    }

    @GrpcMethod(JOB_POSITION_SERVICE_NAME, 'detailJobPosition')
    detailJobPosition(request: Id, metadata: Metadata, ...rest: any): Promise<JobPosition> | Observable<JobPosition> | JobPosition {
        return this.queryBus.execute(new GetJobPositionQuery(request.id));
    }
}
