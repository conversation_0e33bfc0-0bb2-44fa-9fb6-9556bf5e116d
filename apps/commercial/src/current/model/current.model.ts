import { AggregateRoot } from '@nestjs/cqrs';
import { CreateCurrentTrackEvent, EndCurrentTrackEvent, UpdateCurrentTrackEvent } from '../events';

export class CurrentModel extends AggregateRoot {
  constructor() {
    super();
    this.autoCommit = true;
  }

  createCurrentTrack(data: any) {
    this.apply(new CreateCurrentTrackEvent(data));
  }

  updateCurrentTrack(data: any) {
    this.apply(new UpdateCurrentTrackEvent(data));
  }

  endCurrentTrack(data: any) {
    this.apply(new EndCurrentTrackEvent(data))
  }
}
