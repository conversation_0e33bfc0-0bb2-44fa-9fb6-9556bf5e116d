import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetAllCurrentQuery } from '../impl';
import { Pagination } from '@app/common';
import { Prisma } from '@prisma/client';

@QueryHandler(GetAllCurrentQuery)
export class GetAllCurrentHandler implements IQueryHandler<GetAllCurrentQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetAllCurrentQuery) {
//    const { user, args } = query;
//    const search = args.search || '';
//    const items = await Pagination<any,Prisma.CurrentPlayerFindManyArgs>(
//      this.prisma.currentPlayer,
//      {
//        where: {
//          AND: [
//            {
//              property: {
//                id: user.propertyId
//              }
//            },
//            {
//              activation: {
//                type: { contains: search, mode: 'insensitive' },
//                serialNumber: { contains: search, mode: 'insensitive' }
//              }
//            }
//          ]
//        },
//        orderBy: { updatedAt: 'desc' },
//        include: {
//          device: {
//            include: {
//              profile: true
//            }
//          },
//          track: true
//        }
//      },
//      {
//        limit: args.limit,
//        page: args.page,
//      },
//    );
//
//    items.data = items.data.map((item) => {
//      return {
//        device: item.device,
//        current: {
//          duration: item.duration,
//          durationType: item.durationType,
//          paused: item.paused,
//          track: item.track.source
//        }
//      }
//    });
//
//    return items;
  }
}
