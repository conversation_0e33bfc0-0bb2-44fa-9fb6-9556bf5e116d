import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetCurrentQuery } from '../impl';

@QueryHandler(GetCurrentQuery)
export class GetCurrentHandler implements IQueryHandler<GetCurrentQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetCurrentQuery) {
    const { user } = query;

    if (!user.deviceId) {
      return {}
    }
    
    const item = await this.prisma.currentPlayer.findFirst({
      where: {
        AND: [
          {
            activation: {
              id: user.deviceId
            }
          },
          {
            property: {
              id: user.propertyId
            }
          }
        ]
      },
      orderBy: {
        updatedAt: 'desc'
      },
      include: {
        track: true,
        activation: {
          include: {
            config: true,
            activationDevice: {
              include: {
                device: true
              }
            }
          }
        }
      }
    });
    if (!item) {
      return {};
    }

    let trackUri;
    if (typeof item.track.source === 'object' && item.track.source !== null) { 
      trackUri = { 
        ...item.track.source, 
        uri: `queue:${(item.track.source as { id: string }).id}`
      }
    }

    return {
      device: item.activation.activationDevice[0].device,
      current: {
        duration: item.duration,
        durationType: item.durationType,
        paused: item.paused,
        track: trackUri
      }
    }
  }
}
