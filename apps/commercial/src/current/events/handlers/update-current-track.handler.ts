import { <PERSON><PERSON><PERSON>lisher, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateCurrentTrackEvent } from '../impl';
import { HistoryModel } from 'apps/commercial/src/history/models/history.model';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
//import { IntegModel } from 'apps/commercial/src/integration/model/integration.model';

@EventsHandler(UpdateCurrentTrackEvent)
export class UpdateCurrentTrackHandler
  implements IEventHandler<UpdateCurrentTrackEvent>
{
  constructor(
    private prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async handle(event: UpdateCurrentTrackEvent) {
    const { args } = event;
    try {
      const current = await this.prisma.currentPlayer.findFirst({
        where: {
          AND: [
            { activationId: args.deviceId },
            {
              track: {
                key: args.trackId,
              },
            },
            {
              propertyId: args.propertyId,
            },
          ],
        },
        include: {
          activation: {
            include: {
              activationDevice: {
                include: {
                  device: {
                    select: {
                      id: true,
                      type: true,
                    },
                  },
                },
                orderBy: {
                  updatedAt: 'desc',
                },
                take: 1,
              },
            },
          },
        },
      });
      if (current) {
        await this.prisma.currentPlayer.update({
          where: {
            id: current.id,
          },
          data: {
            counter: current.counter + 1,
            duration: args.duration,
            paused: false,
          },
        });

        const item = await this.prisma.setting.findFirst({
          where: { type: 'history' },
        });

        if (!item) {
          return;
        }
        const option = item.option as any;
        const updatedAt = new Date(current.updatedAt);
        const createdAt = new Date(current.createdAt);
        const difMil = Number(updatedAt) - Number(createdAt);
        const difSec = Math.floor(difMil / 1000);
        if (difSec >= Number(option.interval)) {
          const history = await this.prisma.playHistory.findFirst({
            where: {
              AND: [
                {
                  track: {
                    id: current.trackId,
                  },
                },
                {
                  activation: {
                    id: current.activationId,
                  },
                },
                {
                  property: {
                    id: current.propertyId,
                  },
                },
                {
                  endAt: null,
                },
              ],
            },
          });
          if (!history) {
            console.log('======execute history=======');
            console.log('counter', current.counter + 1);
            const eventModel = this.publisher.mergeClassContext(HistoryModel);
            const event = new eventModel();
            event.createTrackHistory({
              key: args.trackId,
              trackId: current.trackId,
              propertyId: current.propertyId,
              deviceId: current.activation.id,
              playerType: current.activation.activationDevice[0].device.type,
              userAgent: current.userAgent,
              ip: current.ip,
              os: current?.os,
              macAddress: current?.macAddress,
              isp: current?.isp,
              geo: current?.geo,
              country: current?.country,
              duration: args.duration,
              durationType: current.durationType,
              city: current?.city,
              region: current?.region, 
              timeZone: current?.timeZone,
              zip: current?.zip,
              flag: args.flag,
            });
          }
        }
      }
    } catch (error) {
      return 'failed update current track';
    }
  }
}
