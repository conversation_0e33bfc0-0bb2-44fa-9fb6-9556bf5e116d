import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from "@nestjs/cqrs";
import { EndCurrentTrackEvent } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { GmiRESTService } from "apps/commercial/src/gmi/gmi.service";
import { InternalServerErrorException, NotFoundException } from "@nestjs/common";

@EventsHandler(EndCurrentTrackEvent)
export class EndCurrentTrackHandler implements IEventHandler<EndCurrentTrackEvent>{
    constructor(
        private prisma: PrismaService,
        private gmiRestService: GmiRESTService
    ) {
        
    }

    async handle(event: EndCurrentTrackEvent) {
        const { args } = event
        try {
            await this.prisma.$transaction(async (tr) => {
              const current = await tr.currentPlayer.findFirst({
                where: {
                  AND: [
                    { activationId: args.deviceId },
                    {
                      track: {
                        key: args.itemId
                      }
                    },
                    {
                      paused: false
                    }
                  ]
                },
                orderBy: {
                  updatedAt: 'desc'
                }
              });
      
              if (!current) {
                throw new NotFoundException('current not found')
              }
      
              const history = await tr.playHistory.findFirst({
                where: {
                  AND: [
                    {
                      track: {
                        key: args.itemId
                      }
                    },
                    {
                      activation: {
                        id: args.deviceId
                      }
                    },
                    {
                      property: {
                        id: args.propertyId
                      }
                    }
                  ]
                },
              });
      
              if (!history) {
                throw new NotFoundException('history not found')
              }
      
              const updatedAt = new Date(current.updatedAt);
              const createdAt = new Date(current.createdAt);
              const difMil = Number(updatedAt) - Number(createdAt);
              const difSec = Math.floor(difMil / 1000);
      
              await tr.playHistory.update({
                where: {
                  id: history.id
                },
                data: {
                  duration: difSec,
                  endAt: new Date().toISOString()
                }
              });
      
              await tr.currentPlayer.delete({
                where: {
                  id: current.id
                },
              });
            });
            return 'success end current'
          } catch (e) {
            throw new InternalServerErrorException()
          }
        }
      }