import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { Prisma } from '@prisma/client';
import { CreateCurrentTrackEvent } from '../impl';
import geoIp from 'geoip-lite';
import { catchError, firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { AxiosError } from 'axios';

@EventsHandler(CreateCurrentTrackEvent)
export class CreateCurrentTrackHandler implements IEventHandler<CreateCurrentTrackEvent> {
  constructor(
    private prisma: PrismaService,
    private httpService: HttpService
  ) {}

  async handle(event: CreateCurrentTrackEvent) {
    const { args } = event;

    try {
      const lookup = geoIp.lookup(args?.info?.ip);
      const { data } = await firstValueFrom(
        this.httpService.get<any>(
          `http://ip-api.com/json/${args?.info?.ip}`)
          .pipe(
            catchError((error: AxiosError) => {
              throw Error('Error client meta : ' + error)
            }),
          ),
      ); 

      const current = await this.prisma.currentPlayer.findFirst({
        where: {
          AND: [
            { activationId: args.deviceId }
          ]
        },
        orderBy: {
          updatedAt: 'desc'
        },
        include: {
          track: true
        }
      });

      if (current?.id) {
        await this.prisma.currentPlayer.delete({
          where: {
            id: current.id
          },
        });
      }
      
        if (current) {
          const history = await this.prisma.playHistory.findFirst({
            where: {
              AND: [
                {
                  track: {
                    key: current.track.key
                  }
                },
                {
                  activation: {
                    id: args.deviceId
                  }
                },
                {
                  property: {
                    id: args.propertyId
                  }
                },
                {
                  endAt: null
                }
              ]
            },
          });
          if (history) {
            const updatedAt = new Date(current.updatedAt);
            const createdAt = new Date(current.createdAt);
            const difMil = Number(updatedAt) - Number(createdAt);
            const difSec = Math.floor(difMil / 1000);
    
            await this.prisma.playHistory.update({
              where: {
                id: history.id
              },
              data: {
                duration: difSec,
                endAt: new Date().toISOString()
              }
            });

          } else {
            //const current = await tr.currentPlayer.findFirst({
            //  where: {
            //    deviceId: args.deviceId
            //  }
            //});
            //if (current) {
            //  await tr.currentPlayer.deleteMany({
            //    where: {
            //      deviceId: args.deviceId
            //    },
            //  });
            //}
          }
        }

        const track = await this.prisma.track.findFirst({
          where: {
            key: args.itemId
          },
          select: {
            id: true,
            key: true
          }
        });

        await this.prisma.currentPlayer.create({
          data: {
            durationType: 'second',
            trackId: track.id,
            activationId: args.deviceId,
            ip: args.info.ip,
            os: args.info.os,
            userAgent: args.info.userAgent,
            geo: lookup?.ll.join(','),
            region: data?.regionName,
            city: data?.city,
            timeZone: data?.timezone,
            country: data?.country,
            isp: data?.isp,
            zip: data?.zip,
            propertyId: args.propertyId
          }
        });

    } catch (error) {
      console.log(error);
      return 'failed create current track';
    }
  }
}
