import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { EndCurrentCommand, PauseCurrentCommand } from '../impl';
import { InternalServerErrorException, NotFoundException } from '@nestjs/common';

@CommandHandler(EndCurrentCommand)
export class EndCurrentHandler
  implements ICommandHandler<EndCurrentCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: EndCurrentCommand) {
    const { user, trackId } = command;
    //try {
    //  await this.prisma.$transaction(async (tr) => {
    //    const current = await tr.currentPlayer.findFirst({
    //      where: {
    //        AND: [
    //          { deviceId: user.deviceId },
    //          {
    //            track: {
    //              key: trackId
    //            }
    //          },
    //          {
    //            paused: false
    //          }
    //        ]
    //      },
    //      orderBy: {
    //        updatedAt: 'desc'
    //      }
    //    });
    //
    //    if (!current) {
    //      throw new NotFoundException('current not found')
    //    }
    //
    //    const history = await tr.playHistory.findFirst({
    //      where: {
    //        AND: [
    //          {
    //            track: {
    //              key: trackId
    //            }
    //          },
    //          {
    //            device: {
    //              id: user.deviceId
    //            }
    //          },
    //          {
    //            property: {
    //              id: user.propertyId
    //            }
    //          }
    //        ]
    //      },
    //    });
//
    //    if (!history) {
    //      throw new NotFoundException('history not found')
    //    }
//
    //    const updatedAt = new Date(current.updatedAt);
    //    const createdAt = new Date(current.createdAt);
    //    const difMil = Number(updatedAt) - Number(createdAt);
    //    const difSec = Math.floor(difMil / 1000);
//
    //    await tr.playHistory.update({
    //      where: {
    //        id: history.id
    //      },
    //      data: {
    //        duration: difSec,
    //        endAt: new Date().toISOString()
    //      }
    //    });
    //
    //    await tr.currentPlayer.delete({
    //      where: {
    //        id: current.id
    //      },
    //    });
    //  });
    //  return 'success end current'
    //} catch (e) {
    //  throw new InternalServerErrorException()
    //}
  }
}
