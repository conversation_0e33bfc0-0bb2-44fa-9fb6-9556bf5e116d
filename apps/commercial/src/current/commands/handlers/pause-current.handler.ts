import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { PauseCurrentCommand } from '../impl';
import { InternalServerErrorException, NotFoundException } from '@nestjs/common';

@CommandHandler(PauseCurrentCommand)
export class PauseCurrentHandler
  implements ICommandHandler<PauseCurrentCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: PauseCurrentCommand) {
    const { user, trackId } = command;

 //   try {
 //     const current = await this.prisma.currentPlayer.findFirst({
 //       where: {
 //         AND: [
 //           { deviceId: user.deviceId },
 //           {
 //             track: {
 //               key: trackId
 //             }
 //           },
 //           {
 //             paused: false
 //           }
 //         ]
 //       },
 //       orderBy: {
 //         updatedAt: 'desc'
 //       }
 //     });
//
 //     if (!current) {
 //       throw new NotFoundException()
 //     }
//
 //     await this.prisma.currentPlayer.update({
 //       where: {
 //         id: await current.id
 //       },
 //       data: {
 //         paused: true
 //       },
 //     });
//
 //     return 'success pause current'
 //   } catch (e) {
 //     throw new InternalServerErrorException()
 //   }
  }
}
