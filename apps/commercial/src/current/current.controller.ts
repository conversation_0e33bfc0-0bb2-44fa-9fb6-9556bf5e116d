import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { FilterCurrentAllDto } from './dto/current-all.dto';
import { GetAllCurrentQuery, GetCurrentQuery } from './queries/impl';
import { EndCurrentCommand, PauseCurrentCommand } from './commands/impl';
import { CurrentDto } from './dto/current.dto';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('current')
@ApiTags('Current')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class CurrentController {
  constructor(
    private commandBus: CommandBus,
    private queryBus: QueryBus,
  ) {}

  @Get('all')
  @ApiOperation({ summary: 'get all current play' })
  getAll(@User() user: ICurrentUser, @Query() filter: FilterCurrentAllDto) {
    return this.queryBus.execute(new GetAllCurrentQuery(user, filter));
  }

  @Get()
  @ApiOperation({ summary: 'get current play' })
  getCurrentPlay(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetCurrentQuery(user));
  }

  @Patch('pause')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @ApiOperation({ summary: 'paused current play track' })
  pausedCurrent(
    @User() user: ICurrentUser,
    @Body() pauseCurrentDto: CurrentDto,
  ) {
    return this.commandBus.execute(
      new PauseCurrentCommand(user, pauseCurrentDto.trackId),
    );
  }

  @Patch('end')
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  @ApiOperation({ summary: 'end current play track' })
  endCurrent(@User() user: ICurrentUser, @Body() endCurrentDto: CurrentDto) {
    return this.commandBus.execute(
      new EndCurrentCommand(user, endCurrentDto.trackId),
    );
  }
}
