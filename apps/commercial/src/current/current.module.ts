import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { GmiModule } from '../gmi/gmi.module';
import { CurrentEventHandlers } from './events';
import { HistoryModule } from '../history/history.module';
import { CurrentCommandHandlers } from './commands/handlers';
import { CurrentQueryHandlers } from './queries/handlers';
import { CurrentController } from './current.controller';
import { LimiterModule } from '../limiter/limiter.module';
import { IntegrationModule } from '../integration/integration.module';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    HistoryModule,
    LimiterModule,
    IntegrationModule,
    HttpModule
  ],
  providers: [
    ...CurrentCommandHandlers,
    ...CurrentQueryHandlers,
    ...CurrentEventHandlers
  ],
})
export class CurrentModule {}
