import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { EventsModule } from '../events/events.module';
import { SessionController } from './session.controller';
import { SessionCommandHandlers } from './commands';

@Module({
  imports: [
    CqrsModule,
    EventsModule,
  ],
  controllers: [SessionController],
  providers: [
    ...SessionCommandHandlers
  ],
})
export class SessionModule {}
