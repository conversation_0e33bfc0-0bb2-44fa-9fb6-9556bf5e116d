import { Controller, <PERSON>, UseGuards } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { AcceptNewSessionCommand, RejectNewSessionCommand } from './commands';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';
@ApiTags('Session')
@Controller('session')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SessionController {
  constructor(private commandBus: CommandBus) {}

  @Patch('accept')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  acceptSession(@User() user: ICurrentUser) {
    return this.commandBus.execute(new AcceptNewSessionCommand(user));
  }

  @Patch('reject')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  rejectSession(@User() user: ICurrentUser) {
    return this.commandBus.execute(new RejectNewSessionCommand(user));
  }
}
