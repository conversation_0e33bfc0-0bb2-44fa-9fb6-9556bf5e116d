import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { AcceptNewSessionCommand } from '../impl';
import { WebGateway } from 'apps/commercial/src/events/web.gateway';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';

@CommandHandler(AcceptNewSessionCommand)
export class AcceptNewSessionHandler
  implements ICommandHandler<AcceptNewSessionCommand>
{
  constructor(
    private prisma: PrismaService,
    private webGateway: WebGateway,
    private playerGateway: PlayerGateway,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async execute(command: AcceptNewSessionCommand) {
    const { user } = command;
      const guestSessionStr = await this.redis.get(
        `guest-session-${user.id}`,
      );

      if (!guestSessionStr) {
        throw new NotFoundException()
      }

      const guestSession = JSON.parse(guestSessionStr);
      const session = await this.prisma.userSession.findFirst({
        where: {
          isActive: true,
          userId: user.id,
        },
      });

      if (!session) {
        throw new NotFoundException()
      }

      await this.prisma.userSession.update({
        where: {
          id: session.id
        },
        data: {
          isActive: false,
          hashAt: null
        }
      });

      await this.prisma.userSession.create({
        data: {
          isActive: true,
          hashAt: guestSession.hashAt,
          media: guestSession.media,
          ip: guestSession.ip,
          userId: user.id,
        }
      });

      this.playerGateway.server.to(`${user.deviceId}`).disconnectSockets();
      this.webGateway.server.to(user.id).disconnectSockets();
      this.webGateway.server.to(`guest-session-${user.id}`).emit('session', {
        ip: null,
        userAgent: null,
        status: 'connected'
      });

      await this.redis.del(
        `guest-session-${user.id}`,
      );

      return 'successfully accept new session';
  }
}