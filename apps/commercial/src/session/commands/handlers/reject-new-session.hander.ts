import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { AcceptNewSessionCommand, RejectNewSessionCommand } from '../impl';
import { WebGateway } from 'apps/commercial/src/events/web.gateway';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';

@CommandHandler(RejectNewSessionCommand)
export class RejectNewSessionHandler
  implements ICommandHandler<RejectNewSessionCommand>
{
  constructor(
    private prisma: PrismaService,
    private webGateway: WebGateway,
    private playerGateway: PlayerGateway,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async execute(command: RejectNewSessionCommand) {
    const { user } = command;
      const guestSessionStr = await this.redis.get(
        `guest-session-${user.id}`,
      );

      if (!guestSessionStr) {
        throw new NotFoundException()
      }

      const guestSession = JSON.parse(guestSessionStr);
      const session = await this.prisma.userSession.findFirst({
        where: {
          isActive: true,
          userId: user.id,
        },
      });

      if (!session) {
        throw new NotFoundException()
      }

      this.webGateway.server.to(`guest-session-${user.id}`).emit('session', {
        ip: null,
        userAgent: null,
        status: 'rejected'
      });

      await this.redis.del(
        `guest-session-${user.id}`,
      );

      return 'successfully reject new session';
  }
}