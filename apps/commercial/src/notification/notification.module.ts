import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { NotificationController } from './notification.controller';
import { NotificationQueryHandlers } from './queries/handlers';
import { NotificationCommandHandlers } from './commands/handlers';
import { EventsModule } from '../events/events.module';
import { NotificationEventHandlers } from './events';

@Module({
  imports: [CqrsModule, EventsModule],
  controllers: [NotificationController],
  providers: [
    ...NotificationQueryHandlers,
    ...NotificationCommandHandlers,
    ...NotificationEventHandlers
  ],
})
export class NotificationModule {}
