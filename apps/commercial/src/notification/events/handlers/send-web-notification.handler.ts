import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { SendWebNotificationEvent } from '../impl';
import { WebGateway } from 'apps/commercial/src/events/web.gateway';

@EventsHandler(SendWebNotificationEvent)
export class SendWebNotificationHandler implements IEventHandler<SendWebNotificationEvent> {
  constructor(
    private readonly webGateway: WebGateway,
    private readonly prismaService: PrismaService
  ) {}

  async handle(event: SendWebNotificationEvent) {
    const { args } = event;
    try {
      const connectedSocket = await this.webGateway.server
      .in(args.to as string)
      .fetchSockets();

      if (connectedSocket.length > 0) {
        this.webGateway.server.to(args.to).emit(args.event, args.data);
      }

      await this.prismaService.notification.create({
        data: {
          toId: args.to,
          title: args.data.type.replace(/_/g, ' '),
          message: args.data.message,
          type: args.data.type,
          tags: args.data.type.split('_')
        }
      });
    } catch (error) {
      return 'failed add notification';
    }
  }
}
