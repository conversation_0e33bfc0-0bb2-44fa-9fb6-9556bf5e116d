import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ReadAllNotificationCommand } from '../impl';
import { InternalServerErrorException } from '@nestjs/common';

@CommandHandler(ReadAllNotificationCommand)
export class ReadAllNotificationHandler
  implements ICommandHandler<ReadAllNotificationCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: ReadAllNotificationCommand) {
    const { user } = command;
    try {
      await this.prisma.notification.updateMany({
        where: {
          toId: user.id
        },
        data: {
          isRead: true,
        },
      });

      return 'successfully read all notification';
    } catch (e) {
      throw new InternalServerErrorException();
    }
  }
}
