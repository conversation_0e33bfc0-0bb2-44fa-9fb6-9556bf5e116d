// import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
// import { AtvGateway } from 'apps/mobile/src/events/atv.gateway';
// import { MobGateway } from 'apps/mobile/src/events/mob.gateway';
// import { REMOTE_PLAYER } from 'apps/mobile/src/common/constans/socket';
// import { RemotePlayerCommand } from '../impl';

// @CommandHandler(RemotePlayerCommand)
// export class RemotePlayerCommandHandler
//   implements ICommandHandler<RemotePlayerCommand>
// {
//   constructor(
//     private readonly atvGateway: AtvGateway,
//     private readonly mobGateway: MobGateway,
//   ) {}

//   async execute(command: RemotePlayerCommand) {
//     const { args } = command;
//     switch(args.event) {
//       case 'status':
//         if (args.deviceType === 'atv') {
//           this.atvGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         if (args.deviceType === 'mob') {
//           this.mobGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         break;
//       case 'currentState':
//         if (args.deviceType === 'atv') {
//           this.atvGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         if (args.deviceType === 'mob') {
//           this.mobGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         break;
//       case 'volume':
//         if (args.deviceType === 'atv') {
//           this.atvGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value as Number
//           });
//         }
//         if (args.deviceType === 'mob') {
//           this.mobGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value as Number
//           });
//         }
//         break;
//       case 'command':
//         if (args.deviceType === 'atv') {
//           this.atvGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         if (args.deviceType === 'mob') {
//           this.mobGateway.server
//           .to(args.to as string)
//           .emit(REMOTE_PLAYER, {
//             event: args.event,
//             data: args.value
//           });
//         }
//         break;
//       default:
//         break;
//     }
//     return
//   }
// }
