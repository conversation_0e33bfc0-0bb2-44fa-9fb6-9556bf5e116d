import { Controller } from '@nestjs/common';
import { EventPublisher } from '@nestjs/cqrs';
import { EventPattern } from '@nestjs/microservices';
import { NotificationModel } from './models/notification.model';
import { PrismaService } from '../../../../libs/prisma';

@Controller()
export class NotificationEventController {
  constructor(
    private readonly publisher: EventPublisher,
    private readonly prismaService: PrismaService
  ) {}

  @EventPattern('WEB-NOTIFICATION')
  async handleRemotePlayerEvent(data: Record<string, unknown>) {
    const property = await this.prismaService.property.findFirst({
      where: {
        id: data.to as string
      },
      include: {
        users: {
          where: {
            isDefault: true
          },
          include: {
            user: true
          }
        }
      }
    });

    if (property && property.users[0]?.user) {
      const notificationModel = this.publisher.mergeClassContext(NotificationModel);
      const hookModel = new notificationModel();
      hookModel.sendWebNotification({
        to: property.users[0].user.id,
        event: 'notification',
        data: {
          message: `device was activated`,
          type: 'device_active',
          data: data.value
        }
      });
    }
    return;
  }
}