import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { Pagination } from '@app/common';
import { Notification, Prisma } from '@prisma/client';
import { GetCountUnreadNotificationsQuery, GetNotificationsQuery } from '../impl';

@QueryHandler(GetCountUnreadNotificationsQuery)
export class GetCountUnreadNotificationsHandler implements IQueryHandler<GetCountUnreadNotificationsQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetCountUnreadNotificationsQuery) {
    const { user } = query;
    const count = await this.prisma.notification.count({
      where: {
        AND: [
          {
            isRead: false
          },
          {
            toId: user.id
          }
        ]
      }
    })

    return {
      count
    };
  }
}
