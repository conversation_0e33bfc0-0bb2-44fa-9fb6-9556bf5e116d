import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { Pagination } from '@app/common';
import { Notification, Prisma } from '@prisma/client';
import { GetNotificationsQuery } from '../impl';

@QueryHandler(GetNotificationsQuery)
export class GetNotificationsHandler implements IQueryHandler<GetNotificationsQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetNotificationsQuery) {
    const { user, args } = query;
    const items = await Pagination<Notification,Prisma.NotificationFindFirstArgs>(
      this.prisma.notification,
      {
        where: {
          toId: user.id 
        },
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          url: true,
          message: true,
          type: true,
          tags: true,
          isRead: true,
          createdAt: true,
          updatedAt: true
        }
      },
      {
        limit: args.limit,
        page: args.page,
      },
    );

    return items;
  }
}
