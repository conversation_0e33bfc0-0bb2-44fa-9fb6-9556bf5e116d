import { Modu<PERSON> } from '@nestjs/common';
import { WebhookController } from './webhook.controller';
import { HookIsellerHandlers } from './commands';
import { CqrsModule } from '@nestjs/cqrs';
import { WebhookEventHandlers } from './events';
import { NotificationModule } from '../notification/notification.module';
import { MembershipClientModule } from '../membership-client/membership-client.module';
// import { HookProcessedSagas } from './sagas/hook-processed.saga';

@Module({
  imports: [
    CqrsModule,
    NotificationModule,
    MembershipClientModule
  ],
  controllers: [WebhookController],
  providers: [
    ...WebhookEventHandlers,
    ...HookIsellerHandlers,
    // HookProcessedSagas
  ],
})
export class WebhookModule {}
