import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { HookProcessEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import {
  CreateLicensePackageCommand,
  CreatePackageCommand,
  UpdateExtendPackageCommand,
  UpdateLicensePackageCommand,
  UpdateNextPackageCommand,
} from 'apps/commercial/src/package/commands';
import { CreateAccountCommand } from 'apps/commercial/src/auth/commands';
import { PropertyService } from 'apps/commercial/src/membership-client/property.service';
import { UserService } from 'apps/commercial/src/membership-client/user.service';
import { OrderService } from 'apps/commercial/src/membership-client/order.service';

@EventsHandler(HookProcessEvent)
export class HookProcessHandler implements IEventHandler<HookProcessEvent> {
  constructor(
    private commandBus: CommandBus,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
    private readonly orderService: OrderService,
  ) {}

  async handle(event: HookPro<PERSON>Event) {
    const { args } = event;
    try {
      const userProperty = await this.userService
        .getUserAndPropertyById(args.order.userId, args.order.propertyId)
        .toPromise();

      switch (args.order.tag) {
        case 'new-subscription':
          // create account
          let account = null;

          userProperty.property['flag'] = 'PAID';
          if (userProperty.user.business_type === 'Single') {
            const existingUser = await this.prisma.userProperty.findFirst({
              where: {
                user: {
                  AND: [
                    {
                      isActive: true,
                    },
                    {
                      isAdmin: true,
                    },
                    {
                      crmUserId: userProperty.user.id,
                    },
                  ],
                },
              },
            });
            if (!existingUser) {
              account = await this.commandBus.execute(
                new CreateAccountCommand(
                  {
                    id: userProperty.user.id,
                    email: userProperty.user.email,
                    password: userProperty.user.password,
                    username: userProperty.user.username,
                    dateOfBirth: userProperty.user.profile?.dateOfBirth,
                    firstName: userProperty.user.profile.first_name,
                    address: userProperty.user.profile?.address,
                    lastName: userProperty.user.profile.last_name,
                    placeOfBirth: userProperty.user.profile?.placeOfBirth,
                    phoneNumber: userProperty.user.mobileNumber,
                    gender: userProperty.user.profile.gender,
                    property: userProperty.property,
                    isAdmin: true,
                    zone: userProperty.user.zone,
                  },
                  true,
                ),
              );
            } else {
              account.user = existingUser;
            }

            Object.assign(account.property, {
              categoryCode: userProperty.property.categoryId,
              unit: Number(userProperty.property.unit),
              licenseKey: userProperty.property?.licenseKey,
              licenseType: userProperty.property?.licenseType,
              requestId: userProperty.property?.requestId,
            });
          } else {
            account = await this.commandBus.execute(
              new CreateAccountCommand(
                {
                  id: userProperty.user.id,
                  email: userProperty.user.email,
                  password: userProperty.user.password,
                  username: userProperty.user.username,
                  dateOfBirth: userProperty.user.profile?.dateOfBirth,
                  firstName: userProperty.user.profile.first_name,
                  address: userProperty.user.profile?.address,
                  lastName: userProperty.user.profile.last_name,
                  placeOfBirth: userProperty.user.profile?.placeOfBirth,
                  phoneNumber: userProperty.user.mobileNumber,
                  gender: userProperty.user.profile.gender,
                  property: userProperty.property,
                  isAdmin: userProperty.user.is_admin,
                  zone: userProperty.user.zone,
                },
                false,
              ),
            );

            Object.assign(account.property, {
              categoryCode: userProperty.property.categoryId,
              unit: Number(userProperty.property.unit),
              licenseKey: userProperty.property?.licenseKey,
              requestId: userProperty.property?.requestId,
              licenseType: userProperty.property?.licenseType,
            });
          }

          // Check for existing trial packages and deactivate them
          const trialPackages = await this.prisma.package.findMany({
            where: {
              propertyId: account.property.id,
              isTrial: true,
              isActive: true,
              status: {
                in: ['active', 'suspend'],
              },
            },
          });

          if (trialPackages.length > 0) {
            await this.prisma.package.updateMany({
              where: {
                propertyId: account.property.id,
                isTrial: true,
                isActive: true,
                status: {
                  in: ['active', 'suspend'],
                },
              },
              data: {
                isActive: false,
                status: 'inActive',
              },
            });
          }

          if (account.property.status === 'suspend') {
            await this.prisma.property.update({
              where: {
                id: account.property.id,
              },
              data: {
                status: 'active',
              },
            });
          }

          // create package plan
          await this.commandBus.execute(
            new CreatePackageCommand(account, args.order),
          );
          // create package license
          await this.commandBus.execute(
            new CreateLicensePackageCommand(account, args.order),
          );

          const voucher = await this.prisma.voucherDetail.findFirst({
            where: {
              orderId: args.order.id,
            },
          });

          if (voucher) {
            await this.prisma.voucherDetail.update({
              where: { orderId: args.order.id },
              data: {
                status: 'used',
              },
            });
          }
          break;
        case 'next-subscription':
          // create package plan
          await this.commandBus.execute(
            new UpdateNextPackageCommand(
              { property: { id: userProperty.property.id } },
              args.order,
            ),
          );

          const vcr = await this.prisma.voucherDetail.findFirst({
            where: {
              orderId: args.order.id,
            },
          });

          if (vcr) {
            await this.prisma.voucherDetail.update({
              where: { orderId: args.order.id },
              data: {
                status: 'used',
              },
            });
          }
          break;
        case 'extend-subscription':
          // extend package plan
          await this.commandBus.execute(
            new UpdateExtendPackageCommand(
              { property: { id: userProperty.property.id } },
              args.order,
            ),
          );

          // extend package license
          await this.commandBus.execute(
            new UpdateLicensePackageCommand(
              {
                property: {
                  id: userProperty.property.id,
                  categoryCode: userProperty.property.categoryId,
                  unit: Number(userProperty.property.unit),
                  licenseKey: userProperty.property?.licenseKey,
                  requestId: userProperty.property?.requestId,
                },
              },
              args.order,
            ),
          );

          const vcrExt = await this.prisma.voucherDetail.findFirst({
            where: {
              orderId: args.order.id,
            },
          });

          if (vcrExt) {
            await this.prisma.voucherDetail.update({
              where: { orderId: args.order.id },
              data: {
                status: 'used',
              },
            });
          }
          break;
        case 'one-purchase':
          break;
        default:
          break;
      }

      await this.orderService
        .paidOrder(
          event.args.order.id,
          event.args.paymentMethod,
          event.args.paymentRequestId,
          event.args.paidAt,
          event.args.paymentId,
        )
        .toPromise();

      return;
    } catch (error) {
      console.log(error);
      return 'failed create package';
    }
  }
}
