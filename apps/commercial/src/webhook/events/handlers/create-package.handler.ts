import { <PERSON><PERSON><PERSON>lisher, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { CreatePackageEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { randStr } from 'libs/utils/rand-str.util';
import { randomDeviceID } from 'libs/utils/rand-device-id.util';
import { NotificationModel } from 'apps/commercial/src/notification/models/notification.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { IntegrationModel } from 'apps/commercial/src/integration/model/integration.model';

@EventsHandler(CreatePackageEvent)
export class CreatePackageHandler implements IEventHandler<CreatePackageEvent> {
  constructor(
    private prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async handle(event: CreatePackageEvent) {
    const { args } = event;
    try {
      const dateNow = new Date();
      const now = Date.now();
      let expiredDateStr: string = '';
      let device;

      const property = await this.prisma.property.findFirst({
        where: {
          id: args.propertyId,
        },
        include: {
          users: {
            include: {
              user: true,
            },
            take: 1,
          },
          propertyType: true,
        },
      });

      if (!property) {
        return;
      }

      const user = property.users[0]?.user;
      if (!user) {
        return;
      }

      switch (args.duration) {
        case 'daily':
          expiredDateStr = dateNow.setDate(dateNow.getDay() + 1).toString();
          break;
        case 'hourly':
          expiredDateStr = dateNow.setHours(dateNow.getHours() + 1).toString();
          break;
        case 'weekly':
          expiredDateStr = dateNow.setDate(dateNow.getDay() + 7).toString();
          break;
        case 'monthly':
          const firstDayNextMonth = new Date(
            dateNow.getFullYear(),
            dateNow.getMonth() + 1,
            1,
          );
          expiredDateStr = new Date(firstDayNextMonth.getTime() - 1)
            .getTime()
            .toString();
          break;
        case 'yearly':
          const oneYearFromNow = new Date();
          dateNow.setFullYear(oneYearFromNow.getFullYear() + 1);
          expiredDateStr = dateNow.getTime().toString();
          break;
        default:
          return;
      }

      const contractEndAt = dateNow
        .setFullYear(dateNow.getFullYear() + 1)
        .toString();
      await this.prisma.$transaction(async (tr) => {
        const pkg = await tr.package.create({
          data: {
            name: args.plan.name,
            isActive: true,
            planId: args.plan.id,
            propertyId: args.propertyId,
            activeAt: now,
            expiredAt: BigInt(expiredDateStr),
            contractEndAt: BigInt(contractEndAt),
            isTrial: args.isTrial,
            trialEndAt: args.trialEndAt,
            orderId: args.orderId
          },
        });

        const date = new Date();
        date.setDate(date.getDate() + 30);
        date.toUTCString();

        for (let i = 0; i < args.details.length; i++) {
          if (args.details[i].featureId == FEATURE.PlayerNode) {
            for (let j = 0; j < args.details[i].qty; j++) {
              await tr.activation.create({
                data: {
                  propertyId: args.propertyId,
                  // expiredAt: date,
                  code: randStr(12),
                  sequence: j + 1,
                },
              });
            }
            await tr.packageFeature.create({
              data: {
                packageId: pkg.id,
                featureId: args.details[i].featureId,
                price: args.details[i].price,
                qty: 0,
              },
            });

            const activation = await tr.activation.findFirst({
              where: {
                propertyId: args.propertyId,
              },
            });

            const newDevice = await tr.device.create({
              data: {
                serialNumber: randomDeviceID(),
                type: 'web',
              },
            });

            device = newDevice;

            //await tr.connection.create({
            //  data: {
            //    deviceId: newDevice.id,
            //    connectedCount: 0,
            //  },
            //});
            //
            //await tr.profileDevice.create({
            //  data: {
            //    deviceId: newDevice.id,
            //    name: 'web-player',
            //  },
            //});

            await tr.activationDevice.create({
              data: {
                deviceId: newDevice.id,
                activationId: activation.id,
                activatedAt: new Date(new Date().toUTCString()),
                qty: 0,
              },
            });

            await tr.activation.update({
              where: {
                id: activation.id,
              },
              data: {
                isUsed: true,
              },
            });

            //await tr.activityDevice.create({
            //  data: {
            //    deviceId: newDevice.id,
            //    actionType: '0xRE',
            //    information: `Device ${newDevice.serialNumber} has been added`,
            //  },
            //});
          } else {
            await tr.packageFeature.create({
              data: {
                packageId: pkg.id,
                featureId: args.details[i].featureId,
                price: args.details[i].price,
                qty: args.details[i].qty,
              },
            });

            if (args.details[i].featureId == FEATURE.SongQuota) {
              await tr.activationDevice.updateMany({
                where: {
                  AND: [
                    {
                      activation: {
                        propertyId: args.propertyId,
                      },
                    },
                    {
                      isActive: true,
                    },
                  ],
                },
                data: {
                  qty: args.details[i].qty,
                },
              });
            }
          }
        }

        await tr.user.update({
          where: {
            id: user.id,
          },
          data: {
            status: 'active',
          },
        });
      });

      // send notification order success
      const notificationModel =
        this.publisher.mergeClassContext(NotificationModel);
      const hookModel = new notificationModel();
      hookModel.sendWebNotification({
        to: user.id,
        event: 'notification',
        data: {
          message: `order successfully paid`,
          type: 'order_success',
          data: {
            orderId: args.id,
          },
        },
      });

      hookModel.sendWebNotification({
        to: user.id,
        event: 'notification',
        data: {
          message: `membership successfully activated`,
          type: 'membership_active',
        },
      });
    } catch (error) {
      console.log(error);
      return 'failed create package';
    }
  }
}
