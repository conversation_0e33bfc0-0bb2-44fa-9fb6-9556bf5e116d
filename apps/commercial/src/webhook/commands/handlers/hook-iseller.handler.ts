import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  EventPublisher,
  ICommandHandler,
} from '@nestjs/cqrs';
import { HookIsellerCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { generateMd5 } from '@app/common'; // Assuming you have this utility function
import {
  BadRequestException,
  HttpStatus,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { WebhookModel } from '../../models/webhook.model';
import { IsellerWebhookPayload } from '../../type';
import { Metadata, status } from '@grpc/grpc-js';
import { OrderService } from 'apps/commercial/src/membership-client/order.service';

@CommandHandler(HookIsellerCommand)
export class HookIsellerHandler implements ICommandHandler<HookIsellerCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
    private readonly orderService: OrderService,
  ) {}

  async execute(command: HookIsellerCommand) {
    try {
      const payload: IsellerWebhookPayload = command.args;
      // console.log('HOOOKK MASSSUUKKKK');

      const { signature, ...dataToSign } = payload;

      const config = await this.prisma.setting.findFirst({
        where: {
          type: 'iseller',
        },
      });
      if (!config) {
        throw new InternalServerErrorException('Internal Server Error');
      }

      const itemIds = payload.items.map((item) => item.id);
      const signatureString = `${config.option['clientId']}.${payload.payment_request_id}.${itemIds.join('.')}.${payload.amount}.${config.option['clientSecret']}`;
      const calculatedSignature = generateMd5(signatureString).toLowerCase();

      if (signature !== calculatedSignature) {
        throw new BadRequestException('Invalid webhook signature');
      }

      const order = await this.orderService
        .checkPayment(payload.payment_request_id)
        .toPromise();
      // console.log(order);

      if (!order.orderPayment) {
        throw new NotFoundException('Order payment not found');
      }

      if (order.orderPayment.isPaid) {
        return HttpStatus.OK;
      }

      // TODO : handler if paid amount not same as order amount
      // const newStatus = payload.paid_amount === payload.amount ? 'paid' : 'pending';

      const webhookModel = this.publisher.mergeClassContext(WebhookModel);
      const hookModel = new webhookModel();
      hookModel.processHook({
        order: order,
        paymentRequestId: payload.payment_request_id,
        paymentMethod: payload.payment_method,
        paidAt: payload.paid_date,
        paymentId: payload.payment_id,
      });

      // await this.prisma.$transaction(async (tr) => {
      //   const updateOrder = await tr.order.update({
      //     where: { id: orderPayment.orderId},
      //     data: {
      //       status: 'paid',
      //     },
      //   })

      //   await tr.orderPayment.update({
      //     where: { id: orderPayment.id },
      //     data: {
      //       by: payload.payment_method,
      //       status: newStatus,
      //     },
      //   });

      //   const property = await tr.property.findFirst({
      //     where: {
      //       id: updateOrder.propertyId
      //     },
      //     include: {
      //       user: true
      //     }
      //   })

      //   await tr.user.update({
      //     where: {
      //       id: property.user.id
      //     },
      //     data: {
      //       status: 'active'
      //     }
      //   });

      //   await tr.property.update({
      //     where: {
      //       id: property.id
      //     },
      //     data: {
      //       status: 'active'
      //     }
      //   });
      // });
    } catch (err) {
      // console.log(err);
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException({
          code: HttpStatus.NOT_FOUND,
          message: err.message,
        });
      } else {
        throw new InternalServerErrorException({
          code: HttpStatus.INTERNAL_SERVER_ERROR,
          message: 'Internal server error',
        });
      }
    }

    // const order = await this.prisma.order.findFirst({
    //   where: {
    //     id: orderPayment.orderId
    //   },
    //   include: {
    //     plan: true,
    //     details: true
    //   }
    // })

    // const webhookModel = this.publisher.mergeClassContext(WebhookModel);
    // const hookModel = new webhookModel();
    // hookModel.createPackage({
    //   id: order.id,
    //  //userId: user
    //   duration: order.duration,
    //   plan: order.plan,
    //   propertyId: order.propertyId,
    //   details: order.details,
    // });

    return HttpStatus.OK;
  }
}
