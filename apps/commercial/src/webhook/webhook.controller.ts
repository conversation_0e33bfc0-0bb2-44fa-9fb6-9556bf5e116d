import { Controller, Post, Body, HttpCode } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { HookIsellerCommand } from './commands';
import { ApiTags } from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';

@ApiTags('webhook')
@Controller('webhook')
export class WebhookController {
  constructor(private commandBus: CommandBus) {}

  @Post()
  @HttpCode(200)
  @Throttle({ default: { limit: 50, ttl: 60000 } })
  create(@Body() args: any) {
    console.log(args)
    return this.commandBus.execute(new HookIsellerCommand(args));
  }
}
