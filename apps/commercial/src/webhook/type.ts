export interface IsellerWebhookPayload {
    amount: number;
    items: { quantity: number; name: string; id: string; sku: string; price: number }[];
    customer: { firstname: string; lastname: string | null; email: string; phone: string | null; id: string };
    paid_amount: number;
    payment_method: string;
    payment_id: string;
    payment_request_id: string;
    additional_info: string;
    paid_date: string;
    signature: string;
  }