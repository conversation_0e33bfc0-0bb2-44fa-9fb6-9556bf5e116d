// import { Injectable } from "@nestjs/common";
// import { CommandBus, ICommand, ofType, Saga } from "@nestjs/cqrs";
// import { mergeMap, Observable, from } from "rxjs";
// import { OrderService } from "../../membership-client/order.service";
// import { HookProcessEvent } from "../events";

// @Injectable()
// export class HookProcessedSagas {
//   public constructor(private readonly orderService: OrderService) {}

//   @Saga()
//   paidOrder = (events$: Observable<any>): Observable<ICommand> => {
//     return events$.pipe(
//       ofType(HookProcessEvent),
//       mergeMap((event) => 
//         from(this.handlePaidOrder(event)) // Convert Promise to Observable
//       )
//     );
//   };

//   private async handlePaidOrder(event: HookProcessEvent): Promise<ICommand | null> {
//     try {
//       console.log('======FROM SAGA========')
//       await this.orderService.paidOrder(
//         event.args.order.id, 
//         event.args.paymentMethod, 
//         event.args.paymentRequestId,
//         event.args.paidAt,
//         event.args.paymentId
//       ).toPromise();

//       // TODO: Add notification logic here if needed

//     } catch (err) {
//       console.error("Error processing paid order:", err);
//     }
//     return null; // Or return an ICommand if needed
//   }
// }
