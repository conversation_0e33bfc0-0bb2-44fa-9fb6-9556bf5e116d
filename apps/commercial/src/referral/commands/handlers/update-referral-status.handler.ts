import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateReferralStatusCommand } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { firstValueFrom } from 'rxjs';
import { OrderService } from '../../../membership-client/order.service';

@CommandHandler(UpdateReferralStatusCommand)
export class UpdateReferralStatusHandler
  implements ICommandHandler<UpdateReferralStatusCommand>
{
  constructor(
    private prisma: PrismaService,
    private orderService: OrderService,
  ) {}

  async execute(request: UpdateReferralStatusCommand) {
    const { args } = request;

    const refCode = await this.prisma.referralCodeDetail.findFirst({
      where: { orderId: args.orderId },
    });

    const partnership = await this.prisma.referralCode.findFirst({
      where: {
        id: refCode.referralCodeId,
      },
    });

    const order = await firstValueFrom(
      this.orderService.getOrderDetail(args.orderId),
    );

    const idPartner = await this.prisma.propertyPartnership.findFirst({
      where: {
        crmPropertyId: order.propertyId,
        partnershipId: partnership.partnershipId,
      },
    });

    await this.prisma.propertyPartnership.delete({
      where: { id: idPartner.id },
    });

    return this.prisma.referralCodeDetail.update({
      where: { orderId: args.orderId },
      data: {
        status: 'available',
      },
    });
  }
}
