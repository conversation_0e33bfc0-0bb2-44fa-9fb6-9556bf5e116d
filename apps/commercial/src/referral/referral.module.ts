import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { MembershipClientModule } from '../membership-client/membership-client.module';
import { ReferralRpcController } from './referral.rpc.controller';
import { ReferralCommandHandlers } from './commands';

@Module({
  imports: [CqrsModule, MembershipClientModule],
  controllers: [ReferralRpcController],
  providers: [...ReferralCommandHandlers],
})
export class ReferralModule {}
