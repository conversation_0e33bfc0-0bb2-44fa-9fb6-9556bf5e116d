import { Grpc<PERSON>ethod } from '@nestjs/microservices';
import {
  REFERRAL_SERVICE_NAME,
  ReferralCodeRequest,
  ReferralServiceController,
  UpdateReferralRequest,
  UseReferralResponse,
} from '@app/proto-schema/internal-proto/referral';
import { Controller } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Observable } from 'rxjs';
import { UpdateReferralStatusCommand, UseReferralCommand } from './commands';
import { Metadata } from '@grpc/grpc-js';

@Controller()
export class ReferralRpcController implements ReferralServiceController {
  constructor(private readonly commandBus: CommandBus) {}

  @GrpcMethod(REFERRAL_SERVICE_NAME, 'useReferral')
  useReferral(
    request: ReferralCodeRequest,
  ):
    | Promise<UseReferralResponse>
    | Observable<UseReferralResponse>
    | UseReferralResponse {
    return this.commandBus.execute(new UseReferralCommand(request));
  }

  @GrpcMethod(REFERRAL_SERVICE_NAME, 'updateStatusReferral')
  updateStatusReferral(
    request: UpdateReferralRequest,
    metadata: Metadata,
    ...rest
  ):
    | Promise<UseReferralResponse>
    | Observable<UseReferralResponse>
    | UseReferralResponse {
    return this.commandBus.execute(new UpdateReferralStatusCommand(request));
  }
}
