import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { PlaylistTrackCommandHandlers } from './commands';
import { PlaylistTrackController } from './playlist-track.controller';
import { TrackPlaylistQueryHandlers } from './queries';
import { GmiModule } from '../gmi/gmi.module';
import { PositionQueueHandlers } from './events';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [PlaylistTrackController],
  providers: [
    ...PlaylistTrackCommandHandlers,
    ...TrackPlaylistQueryHandlers,
    ...PositionQueueHandlers,
  ],
})
export class PlaylistTrackModule {}
