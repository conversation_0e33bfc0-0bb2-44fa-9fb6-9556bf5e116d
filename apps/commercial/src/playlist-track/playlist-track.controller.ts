import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  AddTrackPlaylistCommand,
  RemoveTrackPlaylistCommand,
  RepositionPlaylistTrackCommand,
} from './commands';
import { AddTrackPlaylistDto } from './dto/add-track-playlist.dto';
import { FilterPlaylistTrackDto } from './dto/filter-playlist-track.dto';
import { RemoveTrackPlaylistDto } from './dto/remove-track-playlist.dto';
import { RepositionPlaylistTrackDto } from './dto/reposition-playlist-track.dto';
import { GetPlaylistTracksQuery } from './queries';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('playlist-track')
@ApiTags('Playlist Track')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class PlaylistTrackController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Get(':playlistId')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'get track/song by playlistId' })
  findAll(
    @Query() filter: FilterPlaylistTrackDto,
    @User() user: ICurrentUser,
    @Param('playlistId') playlistId: string,
  ) {
    return this.queryBus.execute(
      new GetPlaylistTracksQuery(user, playlistId, filter),
    );
  }

  @Post()
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'add track/song to playlist' })
  create(
    @Body() addTrackPlaylistDto: AddTrackPlaylistDto,
    @User() user: ICurrentUser
  ) {
    return this.commandBus.execute(
      new AddTrackPlaylistCommand(addTrackPlaylistDto, user),
    );
  }

  @Patch()
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'remove track/song in playlist' })
  update(
    @Body() removeTrackPlaylistDto: RemoveTrackPlaylistDto,
    @User() user: ICurrentUser
  ) {
    return this.commandBus.execute(
      new RemoveTrackPlaylistCommand(removeTrackPlaylistDto, user),
    );
  }

  @Patch('reposition/:playlistId')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'reposition track in playlist' })
  reposition(
    @Param('playlistId') playlistId: string,
    @Body() repositionPlaylistTrackDto: RepositionPlaylistTrackDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new RepositionPlaylistTrackCommand(
        playlistId,
        repositionPlaylistTrackDto,
        user,
      ),
    );
  }
}
