import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { IPlaylistTrack } from '../../types';
import { GetPlaylistTracksQuery } from '../impl';
import { partitionArray } from 'libs/utils/partition-array.util';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';

@QueryHandler(GetPlaylistTracksQuery)
export class GetPlaylistTracksHandler
  implements IQueryHandler<GetPlaylistTracksQuery>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestsService: GmiRESTService,
  ) {}

  async execute(query: GetPlaylistTracksQuery) {
    const { user, playlistId, filter } = query;
    const search = filter.search || '';
    const page = Number(filter?.page || 1);
    const limit = Number(filter?.limit || 100);

    const skip = page > 0 ? limit * (page - 1) : 0;
    let items = await this.prisma.$queryRawUnsafe(`
      SELECT 
        "tp"."trackId" AS tableId,
        "t"."source"->>'id' AS id,
        "t"."source"->>'title' AS title, 
        "t"."source"->'artists' AS artists, 
        "t"."source"->'album' AS album,
        "t"."source"->>'isrc' AS isrc,
        "t"."source"->>'duration' AS duration,
        "t"."source"->'images' AS images,
        "tp"."order" AS ordering 
      FROM "TrackOnPlaylist" AS "tp" 
      INNER JOIN "Track" AS "t" ON "tp"."trackId" = "t"."id" 
      INNER JOIN "Playlist" AS "p" ON "tp"."playlistId" = "p"."id" 
      WHERE 
        "p"."id" = '${playlistId}'
        AND (
          (
            "p"."activationId" = '${user.deviceId}' AND
            "p"."propertyId" = '${user.propertyId}'
          )
          OR EXISTS (
            SELECT 1 FROM "PlaylistFollower" pf
            INNER JOIN "Activation" a ON pf."activationId" = a."id"
            WHERE pf."playlistId" = p."id"
              AND a."id" = '${user.deviceId}'
          )
          OR "p"."linkCode" = '${filter.code}'
          OR EXISTS (
            SELECT 1 FROM "PlaylistAccess" pa
            WHERE pa."playlistId" = p."id"
              AND (
                pa."propertyId" = '${user.propertyId}' OR
                pa."activationId" = '${user.deviceId}'
              )
          )
          OR "p"."permission" = 'PUBLIC'
        )
        AND (
          ("t"."source"#>>ARRAY['name']) ILIKE '%${search}%'
          OR ("t"."source"#>>ARRAY['album','name']) ILIKE '%${search}%'
          OR ("t"."source"->>'title') ILIKE '%${search}%'
          OR ("t"."source"#>>ARRAY['artists', '0', 'name']) ILIKE '%${search}%'
        )
      ORDER BY "ordering" ASC 
      LIMIT ${limit} OFFSET ${skip}
    `) as any[];

    if (items.length > 0) {
      const originId = items.map((r) => {
        return r.tableid;
      });

      const trackGmiIds = items.map((tr) => {
        return tr.id;
      });
      const filteredTrack = [];
      const gmiIdsPartition = partitionArray(trackGmiIds, 50);
      for (let idx = 0; idx <= gmiIdsPartition.length - 1; idx++) {
        const trackIn = await this.gmiRestsService.getTrackIn(
          gmiIdsPartition[idx],
          'myplaylist',
        );
        if (trackIn.length > 0) {
          for (let j = 0; j <= trackIn.length - 1; j++) {
            filteredTrack.push(trackIn[j]);
          }
        }
      }

      const trackMap = new Map(filteredTrack.map((trck) => [trck.id, trck]));
      items = items.map((itm) =>
        this.transformImageData(trackMap.get(itm.id), itm.tableid),
      );

      const trackLike = await this.prisma.trackLike.findMany({
        where: {
          AND: [
            {
              trackId: {
                in: originId,
              },
            },
            {
              activationId: user.deviceId,
            },
          ],
        },
      });

      items = items.map((trck) => {
        Object.assign(trck, { isLike: false });
        if (trackLike.length > 0) {
          trackLike.map((tl) => {
            if (trck.tableId == tl.trackId) {
              return (trck.isLike = true);
            }
          });
        }
        return trck;
      });
    }

    const itemsCount = await this.prisma.$queryRawUnsafe(
      `SELECT count(*) FROM "TrackOnPlaylist" AS "tp" 
      INNER JOIN "Track" AS "t" ON "tp"."trackId"="t"."id" 
      INNER JOIN "Playlist" AS "p" ON "tp"."playlistId"="p"."id" 
      WHERE (("p"."propertyId"='${user.propertyId}') 
      AND ("tp"."playlistId"='${playlistId}') 
      AND ((("t"."source"#>>ARRAY['name']::text[]) ILIKE '%${search}%') OR (("t"."source"#>>ARRAY['album','name']::text[]) ILIKE '%${search}%')))`,
    );
    const total = Number(itemsCount[0].count);

    const lastPage = Math.ceil(total / limit);

    return {
      data: items.map((item) => ({
        ...item,
        uri: `myplaylist:${playlistId}:${item.id}`,
      })),
      meta: {
        total,
        lastPage,
        currentPage: page,
        limit,
        prev: page > 1 ? page - 1 : null,
        next: page < lastPage ? page + 1 : null,
      },
    };
  }

  private transformImageUrl(url: string): string {
    const baseUrlRegex = /(https?:\/\/[^\/]+)\/+/;
    return url.replace(baseUrlRegex, process.env.CDN_URL);
  }

  private transformImageData(data: any, tableId: string) {
    return {
      ...data,
      tableId: tableId,
      images:
        data.images?.map((image: { url: string }) => ({
          ...image,
          url: this.transformImageUrl(image.url),
        })) || [],
      album: {
        ...data.album,
        images:
          data.album?.images?.map((image: { url: string }) => ({
            ...image,
            url: this.transformImageUrl(image.url),
          })) || [],
      },
    };
  }
}
