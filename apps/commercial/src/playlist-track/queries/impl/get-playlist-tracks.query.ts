import { IQuery } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { FilterPlaylistTrackDto } from '../../dto/filter-playlist-track.dto';

export class GetPlaylistTracksQuery implements IQuery {
  constructor(
    public readonly user: ICurrentUser,
    public readonly playlistId: string,
    public readonly filter: FilterPlaylistTrackDto,
  ) {}
}
