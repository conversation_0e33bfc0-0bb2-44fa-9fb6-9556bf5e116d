import { ICommand } from '@nestjs/cqrs';
import { RepositionPlaylistTrackDto } from '../../dto/reposition-playlist-track.dto';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';

export class RepositionPlaylistTrackCommand implements ICommand {
  constructor(
    public readonly playlistId: string,
    public readonly args: RepositionPlaylistTrackDto,
    public readonly user: ICurrentUser
  ) {}
}
