import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RemoveTrackPlaylistCommand } from '../impl';
import { BadRequestException, Inject, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { PlatformType } from '@prisma/client';

@CommandHandler(RemoveTrackPlaylistCommand)
export class RemoveTrackPlaylistHandler
  implements ICommandHandler<RemoveTrackPlaylistCommand>
{
  constructor(
    private prisma: PrismaService,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: RemoveTrackPlaylistCommand) {
    const { args, user } = command;
    try {
      const playlist = await this.prisma.playlist.findFirst({
        where: {
          AND: [
            {
              id: args.playlistId
            },
            {
              activation: {
                id: user.deviceId
              }
            }
          ]
        }
      });
      if (!playlist) {
        throw new BadRequestException('playlist not found')
      }

      const track = await this.prisma.track.findFirst({
        where: { key: args.trackId },
      });

      await this.prisma.trackOnPlaylist.delete({
        where: {
          playlistId_trackId: {
            playlistId: args.playlistId,
            trackId: track.id,
          },
        },
      });

      const scheduleData = await this.prisma.scheduleZone.findMany({
        where: {
          schedule: {
            contents: {
              playlistId: args.playlistId,
            },
          },
        },
        select: {
          activationId: true,
        },
      });

      const playlistPlatform = await this.prisma.playlistPlatform.findFirst({
        where: {
          AND: [
            { playlistId: args.playlistId },
            { platform: PlatformType.ATV },
          ],
        },
        include: {
          playlist: {
            select: { activationId: true },
          },
        },
      });

      const activationIds = scheduleData.map((item) => item.activationId);

      const playlistPlatformId = playlistPlatform?.playlist?.activationId
        ? [playlistPlatform?.playlist?.activationId]
        : [];

      const combinedIds = [...activationIds, ...playlistPlatformId];

      const uniqueActivationIds = [...new Set(combinedIds)];
      // console.log(uniqueActivationIds);
      this.client.emit('playlist-update', {
        activationId: uniqueActivationIds,
        data: { playlistId: args.playlistId },
      });

      return 'successfully removed track in playlist';
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new NotFoundException(error.message)
      }
      throw new InternalServerErrorException(
        'failed removed track in playlist',
      );
    }
  }
}
