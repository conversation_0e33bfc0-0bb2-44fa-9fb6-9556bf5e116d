import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>Command<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PlatformType, Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import { AddTrackPlaylistCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { BadRequestException, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@CommandHandler(AddTrackPlaylistCommand)
export class AddTrackPlaylistHandler
  implements ICommandHandler<AddTrackPlaylistCommand>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestService: GmiRESTService,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: AddTrackPlaylistCommand) {
    const { args, user } = command;
    let ordering: number = 0;
    const playlist = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          {
            id: args.playlistId
          },
          {
            activation: {
              id: user.deviceId
            }
          }
        ]
      }
    });
    if (!playlist) {
      throw new BadRequestException('playlist not found')
    }

    const lastRecord = await this.prisma.trackOnPlaylist.findFirst({
      where: { playlist: { id: args.playlistId } },
      orderBy: { order: 'desc' },
    });
    if (lastRecord) {
      ordering = Number(lastRecord.order);
    }

    try {
      await this.prisma.$transaction(async (tr) => {
        const tracks = args.tracks;
        const trackOnPlaylistData = [];

        let trackId: string;
        for (let idx = 0; idx < tracks.length; idx++) {
          const elm = tracks[idx];
          let track = await this.prisma.track.findFirst({
            where: { key: elm.id },
          });

          if (!track) {
            const gmiTrack = await this.gmiRestService.melodivaUse(elm.id);

            track = await tr.track.create({
              data: {
                key: elm.id,
                source: gmiTrack,
              },
            });
          }

          const isTrackInPlaylist = await tr.trackOnPlaylist.findFirst({
            where: { trackId: track.id, playlistId: args.playlistId },
          });

          if (!isTrackInPlaylist) {
            trackOnPlaylistData.push({
              playlistId: args.playlistId,
              trackId: track.id,
              order: ordering + idx + 1,
            });
          } else {
            console.warn(
              `Track with ID ${track.id} is already in the playlist ${args.playlistId}`,
            );
            throw new BadRequestException('Track already in the playlist');
          }
        }

        await tr.trackOnPlaylist.createMany({
          data: trackOnPlaylistData,
        });
      });

      const scheduleData = await this.prisma.scheduleZone.findMany({
        where: {
          schedule: {
            contents: {
              playlistId: args.playlistId,
            },
          },
        },
        select: {
          activationId: true,
        },
      });

      const playlistPlatform = await this.prisma.playlistPlatform.findFirst({
        where: {
          AND: [
            { playlistId: args.playlistId },
            { platform: PlatformType.ATV },
          ],
        },
        include: {
          playlist: {
            select: { activationId: true },
          },
        },
      });

      const activationIds = scheduleData.map((item) => item.activationId);

      const playlistPlatformId = playlistPlatform?.playlist?.activationId
        ? [playlistPlatform?.playlist?.activationId]
        : [];

      const combinedIds = [...activationIds, ...playlistPlatformId];

      const uniqueActivationIds = [...new Set(combinedIds)];
      // console.log(uniqueActivationIds);
      this.client.emit('playlist-update', {
        activationId: uniqueActivationIds,
        data: { playlistId: args.playlistId },
      });

      return 'successfully added track to playlist';
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      } else {
        console.error('failed added track to playlist', error.message);
        return 'failed added track to playlist';
      }
    }
  }
}
