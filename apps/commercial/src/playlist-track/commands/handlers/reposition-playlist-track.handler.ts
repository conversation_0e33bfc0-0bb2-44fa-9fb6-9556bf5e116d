import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RepositionPlaylistTrackCommand } from '../impl';
import { QueueModel } from '../../model/queue.model';
import { BadRequestException, Inject, InternalServerErrorException } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@CommandHandler(RepositionPlaylistTrackCommand)
export class RepositionPlaylistTrackHandler
  implements ICommandHandler<RepositionPlaylistTrackCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: RepositionPlaylistTrackCommand) {
    const { playlistId, args, user } = command;
    try {
      const playlist = await this.prisma.playlist.findFirst({
        where: {
          AND: [
            {
              id: playlistId
            },
            {
              activation: {
                id: user.deviceId
              }
            }
          ]
        }
      });
      if (!playlist) {
        throw new BadRequestException('playlist not found')
      }

      let tracks;

      await this.prisma.$transaction(async (tr) => {
        await tr.trackOnPlaylist.deleteMany({
          where: { playlist: { id: playlistId } },
        });

        const trackIds = [];
        for (let idx = 0; idx < args.trackIds.length; idx++) {
          const elm = args.trackIds[idx];

          let trck = await tr.track.findFirst({
            where: { key: elm },
          });

          await tr.trackOnPlaylist.create({
            data: {
              playlistId: playlistId,
              trackId: trck.id,
              order: idx + 1,
            },
          });

          trackIds.push(trck.id);
        }

        tracks = await tr.trackOnPlaylist
          .findMany({
            where: { playlistId: playlistId },
            orderBy: { order: 'asc' },
            include: { track: true },
          })
          .then((items) => items.map((item) => item.track));
      });

      const playlistPlatform = await this.prisma.playlistPlatform.findMany({
        where: {
          playlistId: playlistId,
        },
      });
      const hasATV = playlistPlatform.some((p) => p.platform === 'ATV');

      if (hasATV) {
        this.client.emit('playlist-update', {
          data: { playlistId: playlistId },
        });
      }

      // this.emitQueueUpdated(tracks, user, 'playlist', playlistId);

      return 'successfully reposition playlist track';
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }

  // private emitQueueUpdated(tracks: any[], user: any, queueType: string, itemId: string) {
  //   const queueModel = this.publisher.mergeClassContext(QueueModel);
  //   const event = new queueModel();
  //   event.QueueUpdate(tracks, user, queueType, itemId)
  // }
}
