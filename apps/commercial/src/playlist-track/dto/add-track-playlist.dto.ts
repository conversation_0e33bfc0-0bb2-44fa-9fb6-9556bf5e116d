import { ApiProperty } from '@nestjs/swagger';
import { IsExist } from '@validator/validator';
import { Type } from 'class-transformer';
import {
  ArrayMaxSize,
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsString,
  ValidateNested,
} from 'class-validator';
import { TrackPlaylistDto } from './track-playlist.dto';

export class AddTrackPlaylistDto {
  @ApiProperty()
  @IsString()
  @IsExist({ model: 'playlist', field: 'id' })
  playlistId: string;
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @ApiProperty({ type: [TrackPlaylistDto] })
  @ArrayUnique((prop) => prop.key)
  @ArrayMaxSize(100)
  @Type(() => TrackPlaylistDto)
  tracks: TrackPlaylistDto[];
}
