import { SpotifyTrackDto } from '@app/common';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';

export class TrackPlaylistDto {
  @IsString()
  @ApiProperty()
  id: string;
  // @ValidateNested()
  // @ApiProperty()
  // @Type(() => SpotifyTrackDto)
  // @IsObject()
  // source: SpotifyTrackDto;
  
  // @ValidateNested()
  // @ApiProperty()
  // @IsOptional()
  // @IsObject()
  // //dibuat any karena sementara, ambil source dari GMIrest
  // source: any; 
}
