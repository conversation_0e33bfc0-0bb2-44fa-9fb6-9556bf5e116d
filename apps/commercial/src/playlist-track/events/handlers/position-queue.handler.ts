import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PositionQueueEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';

@EventsHandler(PositionQueueEvent)
export class PositionQueueHandler implements IEventHandler<PositionQueueEvent> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly player: PlayerGateway,
  ) {}

  async handle(event: PositionQueueEvent) {
    const { tracks, user, type, itemId } = event;

    const limit = Math.max(1, Number(10));

    try {
      const queue = await this.prisma.queue.findFirst({
        where: { activationId: user.deviceId },
        select: { id: true },
      });

      if (!queue) {
        await this.prisma.queue.create({
          data: {
            activation: { connect: { id: user.deviceId } },
            Property: { connect: { id: user.propertyId } },
          },
        });
      }

      await this.prisma.queueTrack.deleteMany({
        where: { queueId: queue.id },
      });

      for (let i = 0; i < tracks.length; i++) {
        const track = tracks[i];
        await this.prisma.queueTrack.create({
          data: {
            queueId: queue.id,
            trackId: track.id,
            addedAt: new Date(),
            order: i,
            current: itemId == track.key,
            uri: `queue:${track.key}`,
          },
        });
      }

      const currentTrack = await this.prisma.queueTrack.findFirst({
        where: {
          queue: { activationId: user.deviceId },
          current: true,
        },
        include: { track: true },
      });

      let currentTrackIndex = currentTrack ? currentTrack.order : -1;

      let skip = Math.max(0, currentTrackIndex - 1);

      if (skip + limit > tracks.length) {
        skip = Math.max(0, tracks.length - limit);
      }

      const queueTracks = await this.prisma.queueTrack.findMany({
        where: {
          queue: { activationId: user.deviceId },
        },
        include: { track: true },
        orderBy: { order: 'asc' },
        skip,
        take: limit,
      });

      if (
        currentTrack &&
        !queueTracks.some((track) => track.id === currentTrack.id)
      ) {
        queueTracks[limit - 1] = currentTrack;
      }

      const responseData = {
        tracks: queueTracks.map((queueTrack) => {
          const source = queueTrack.track.source as { uri: string };
          source.uri = queueTrack.uri;
          return {
            ...source,
            current: queueTrack.current,
          };
        }),
      };

      this.player.server.to(user.deviceId).emit('queue', responseData);
    } catch (error) {
      console.error('Error handling CreatedQueueEvent:', error);
      this.player.server
        .to(user.deviceId)
        .emit('queue', { message: error.message, code: error.code });
    }
  }
}
