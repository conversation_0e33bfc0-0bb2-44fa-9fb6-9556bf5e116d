import { AggregateRoot } from "@nestjs/cqrs";
import { ICurrentUser } from "../../auth/strategies/types/user.type";
import { PositionQueueEvent } from "../events";

export class QueueModel extends AggregateRoot{
    constructor() {
        super()
        this.autoCommit = true
    }


    QueueUpdate(tracks: any[], user: ICurrentUser, type: string, itemId: string) {
        this.apply(new PositionQueueEvent( tracks, user, type, itemId));
      }

}