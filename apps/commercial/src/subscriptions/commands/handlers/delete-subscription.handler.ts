import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeleteSubscriptionCommand } from '../impl';

@CommandHandler(DeleteSubscriptionCommand)
export class DeleteSubscriptionHandler
  implements ICommandHandler<DeleteSubscriptionCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteSubscriptionCommand) {
    const { id } = command;
    // return this.prisma.subscriptions.delete({ where: { id: id } });
  }
}
