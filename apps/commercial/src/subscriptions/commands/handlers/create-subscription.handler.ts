import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CreateSubscriptionCommand } from '../impl';

@CommandHandler(CreateSubscriptionCommand)
export class CreateSubscriptionHandler
  implements ICommandHandler<CreateSubscriptionCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateSubscriptionCommand) {
    const { sub, args } = command;
    // const dataProperty = await this.prisma.userOnProperty.findFirst({
    //   where: { userId: sub },
    // });

    // return await this.prisma.subscriptions.create({
    //   data: {
    //     price: args.price,
    //     expirationDate: args.expiration_date,
    //     planId: args.plan_id,
    //     propertyId: dataProperty.propertyId,
    //   },
    // });
  }
}
