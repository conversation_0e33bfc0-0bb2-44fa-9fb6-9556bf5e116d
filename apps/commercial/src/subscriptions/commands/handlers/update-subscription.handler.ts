import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateSubscriptionCommand } from '../impl';

@CommandHandler(UpdateSubscriptionCommand)
export class UpdateSubscriptionHandler
  implements ICommandHandler<UpdateSubscriptionCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateSubscriptionCommand) {
    const { id, args } = command;
    // return await this.prisma.subscriptions.update({
    //   where: { id: id },
    //   data: {
    //     price: args.price,
    //   },
    // });
  }
}
