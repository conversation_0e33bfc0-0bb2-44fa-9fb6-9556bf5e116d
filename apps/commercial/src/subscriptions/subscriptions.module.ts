import { Module } from '@nestjs/common';
import { SubscriptionsController } from './subscriptions.controller';
import { CqrsModule } from '@nestjs/cqrs';
import { SubscriptionQueryHandlers } from './queries';
import { SubscriptionCommandHandlers } from './commands';

@Module({
  imports: [CqrsModule],
  controllers: [SubscriptionsController],
  providers: [...SubscriptionQueryHandlers,...SubscriptionCommandHandlers],
})
export class SubscriptionsModule {}
