import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiExcludeController, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import {
  CreateSubscriptionCommand,
  DeleteSubscriptionCommand,
  UpdateSubscriptionCommand,
} from './commands';
import { CreateSubscriptionDto } from './dto/create-subscription.dto';
import { FilterSubscriptionDto } from './dto/filter-subscription.dto';
import { UpdateSubscriptionDto } from './dto/update-subscription.dto';
import { GetSubscriptionQuery, GetSubscriptionsQuery } from './queries';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@ApiTags('Subscription')
@ApiExcludeController()
@Controller('subscriptions')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SubscriptionsController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  create(
    @Body() createSubscriptionDto: CreateSubscriptionDto,
    @User() user: any,
  ) {
    const { sub } = user;
    return this.commandBus.execute(
      new CreateSubscriptionCommand(createSubscriptionDto, sub),
    );
  }

  @Get()
  findAll(@Query() filter: FilterSubscriptionDto) {
    return this.queryBus.execute(new GetSubscriptionsQuery(filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetSubscriptionQuery(id));
  }

  @Patch(':id')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  update(
    @Param('id') id: string,
    @Body() updateSubscriptionDto: UpdateSubscriptionDto,
  ) {
    return this.commandBus.execute(
      new UpdateSubscriptionCommand(id, updateSubscriptionDto),
    );
  }

  @Delete(':id')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  remove(@Param('id') id: string) {
    return this.commandBus.execute(new DeleteSubscriptionCommand(id));
  }
}
