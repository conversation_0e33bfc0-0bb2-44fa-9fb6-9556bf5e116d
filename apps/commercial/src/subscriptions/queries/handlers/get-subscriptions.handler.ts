import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetSubscriptionsQuery } from '../impl';

@QueryHandler(GetSubscriptionsQuery)
export class GetSubscriptionsHandler
  implements IQueryHandler<GetSubscriptionsQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetSubscriptionsQuery) {
    // const items = await this.prisma.subscriptions.findMany();
    // return items;
  }
}
