import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetSubscriptionQuery } from '../impl';

@QueryHandler(GetSubscriptionQuery)
export class GetSubscriptionHandler
  implements IQueryHandler<GetSubscriptionQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetSubscriptionQuery) {
    const { id } = query;
    // const item = await this.prisma.subscriptions.findFirst({
    //   where: { id: id },
    // });
    // if (!item) {
    //   throw new NotFoundException();
    // }
    // return item;
  }
}
