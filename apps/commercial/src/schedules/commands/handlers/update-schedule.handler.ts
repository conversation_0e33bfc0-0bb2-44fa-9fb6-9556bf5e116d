import { BadRequestException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateScheduleCommand } from '../impl';
import { ScheduleReminderModel } from 'apps/commercial/src/schedule-reminder/models/schedule-remainder.model';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';

@CommandHandler(UpdateScheduleCommand)
export class UpdateScheduleHandler
  implements ICommandHandler<UpdateScheduleCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async execute(command: UpdateScheduleCommand) {
    const { user, id, args } = command;
    let modeValue: string[] = [];

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const expiredDate = new Date(args.expiredDate);

    if (expiredDate < today) {
      throw new BadRequestException('expiredDate cannot be less than today');
    }

    switch (args.mode) {
      case 'daily':
        modeValue = [
          'sunday',
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
        ];
        break;
      case 'weekday':
        modeValue = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        break;
      case 'weekend':
        modeValue = ['saturday', 'sunday'];
        break;
      default:
        modeValue = args.modeValue;
        break;
    }
    const item = await this.prisma.schedule.findFirst({
      where: { AND: [{ id: id }, { property: { id: user.propertyId } }] },
    });

    if (!item) {
      throw new NotFoundException();
    }

    try {
      const existingScheduleWithName = await this.prisma.schedule.findFirst({
        where: {
          name: args.name,
          propertyId: user.propertyId,
          id: {
            not: id,
          },
        },
      });

      if (existingScheduleWithName) {
        throw new BadRequestException(
          `Schedule name with ${existingScheduleWithName.name} already exists`,
        );
      }

      const overlappingSchedules = await this.prisma.schedule.findMany({
        where: {
          id: {
            not: id,
          },
          activationId: user.deviceId,
          scheduleZone: { some: { activationId: { in: args.deviceIds } } },
          mode: args.mode,
          modeValue: {
            hasSome: modeValue,
          },
          expiredDate: {
            gte: expiredDate,
          },
          OR: [
            {
              startTime: {
                gte: args.startTime,
                lte: args.endTime,
              },
            },
            {
              endTime: {
                gte: args.startTime,
                lte: args.endTime,
              },
            },
            {
              AND: [
                { startTime: { lte: args.startTime } },
                { endTime: { gte: args.endTime } },
              ],
            },
          ],
        },
      });

      if (overlappingSchedules.length > 0) {
        throw new BadRequestException(
          'Schedule overlaps with existing schedules',
        );
      }

      const schedule = await this.prisma.schedule.update({
        where: { id: item.id },
        data: {
          name: args.name,
          mode: args.mode,
          startTime: args.startTime,
          endTime: args.endTime,
          modeValue: modeValue,
          expiredDate: new Date(args.expiredDate).toISOString(),
          setVolume: args.setVolume,
          volume: args.volume,
          playMode: args.playMode,
          repeatEnd: args.repeatEnd,
          property: { connect: { id: user.propertyId } },
          contents: {
            delete: {},
            create: { playlist: { connect: { id: args.playlistId } } },
          },
        },
      });

      if (args.deviceIds && args.deviceIds.length > 0) {
        await this.prisma.$transaction(async (tr) => {
          await tr.scheduleZone.deleteMany({
            where: {
              scheduleId: schedule.id,
            },
          });
          await tr.scheduleZone.createMany({
            data: args.deviceIds.map((deviceId) => ({
              scheduleId: schedule.id,
              activationId: deviceId,
            })),
            skipDuplicates: true,
          });
        });
      }

      const startTime = new Date(schedule.startTime);
      const endTime = new Date(schedule.endTime);
      const now = new Date();
      const thirtyMinutes = 30 * 60 * 1000;
      const difference = startTime.getTime() - thirtyMinutes;
      const startNow =
        now.getTime() >= difference && now.getTime() <= endTime.getTime();

      if (startNow || args.playNow) {
        let scheduleType = '';

        if (args.playNow) {
          scheduleType = 'execute';
        } else {
          if (now.getTime() >= startTime.getTime() && now.getTime() <= endTime.getTime()) {
            scheduleType = 'execute'
          } else {
            scheduleType = 'reminder';
          }
        }

        const item = await this.prisma.scheduleReminder.create({
          data: { schedule: { connect: { id: schedule.id } } },
        });

        const devices = await this.prisma.activation.findMany({
          where: {
            id: {
              in: args.deviceIds,
            },
          },
          select: {
            id: true,
          },
        });

        const scheduleReminderModel = this.publisher.mergeClassContext(
          ScheduleReminderModel,
        );
        const model = new scheduleReminderModel(item.id);
        model.ScheduleReminderCreated(
          schedule.propertyId,
          args.playNow,
          devices.map((dv) => dv.id),
          scheduleType,
        );
      }

      if (!startNow) {
        for (const deviceId of args.deviceIds) {
          // console.log('deviceId', deviceId);
          await this.redis.del(`schedule_execute-${deviceId}`);
        }
      }

      // if (
      //   (now.getTime() >= difference && now.getTime() <= endTime.getTime()) ||
      //   args.playNow
      // ) {
      //   const item = await this.prisma.scheduleReminder.create({
      //     data: {
      //       schedule: { connect: { id: schedule.id } },
      //       isRead: false,
      //     },
      //   });

      //   const devices = await this.prisma.activation.findMany({
      //     where: {
      //       id: {
      //         in: args.deviceIds,
      //       },
      //     },
      //   });

      //   const deviceIds = devices.map((dev) => dev.id)

      //   const scheduleReminderModel = this.publisher.mergeClassContext(
      //     ScheduleReminderModel,
      //   );
      //   const model = new scheduleReminderModel(item.id);
      //   model.ScheduleReminderCreated(
      //     schedule.propertyId,
      //     args.playNow,
      //     deviceIds,
      //     'execute',
      //   );
      // }
      return 'successfully updated schedule';
    } catch (error) {
      return 'failed updated schedule';
    }
  }
}
