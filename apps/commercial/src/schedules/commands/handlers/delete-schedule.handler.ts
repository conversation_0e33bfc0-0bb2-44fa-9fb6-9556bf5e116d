import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { DeleteScheduleCommand } from '../impl';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeleteScheduleCommand)
export class DeleteScheduleHandler
  implements ICommandHandler<DeleteScheduleCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher
  ) {}

  async execute(command: DeleteScheduleCommand) {
    const { user, id } = command;
    const item = await this.prisma.schedule.findFirst({
      where: { AND: [{ property: { id: user.propertyId } }, { id: id }] },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.schedule.delete({ where: { id: item.id } });
      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.increaseFeatureQty({
        id: FEATURE.Schedule,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });
      return 'successfully deleted schedule';
    } catch (error) {
      return 'failed deleted schedule';
    }
  }
}
