import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CreateScheduleCommand } from '../impl';
import {
  BadRequestException,
  ForbiddenException,
  InternalServerErrorException,
} from '@nestjs/common';
import { ScheduleReminderModel } from 'apps/commercial/src/schedule-reminder/models/schedule-remainder.model';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(CreateScheduleCommand)
export class CreateScheduleHandler
  implements ICommandHandler<CreateScheduleCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
  ) {}

  async execute(command: CreateScheduleCommand) {
    const { user, args } = command;

    const schedulePackage = await this.prisma.zoneFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.Schedule,
          },
          {
            activationId: user.deviceId,
          },
        ],
      },
    });

    if (!schedulePackage) {
      throw new ForbiddenException('Insufficient schedule quota');
    }

    if (schedulePackage.qouta < 1 && schedulePackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient schedule quota');
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const expiredDate = new Date(args.expiredDate);

    if (expiredDate < today) {
      throw new BadRequestException('expiredDate cannot be less than today');
    }

    let modeValue: string[] = [];
    switch (args.mode) {
      case 'daily':
        modeValue = [
          'sunday',
          'monday',
          'tuesday',
          'wednesday',
          'thursday',
          'friday',
          'saturday',
        ];
        break;
      case 'weekday':
        modeValue = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'];
        break;
      case 'weekend':
        modeValue = ['saturday', 'sunday'];
        break;
      default:
        modeValue = args.modeValue;
        break;
    }

    try {
      const existingScheduleWithName = await this.prisma.schedule.findFirst({
        where: {
          AND: [
            {
              name: args.name,
            },
            {
              activationId: user.deviceId,
            },
            {
              propertyId: user.propertyId,
            },
          ],
        },
      });

      if (existingScheduleWithName) {
        throw new BadRequestException(
          `Schedule name with ${existingScheduleWithName.name} already exists`,
        );
      }

      if (args.deviceIds.length === 0) {
        args.deviceIds.push(user.deviceId);
      }

      const overlappingSchedules = await this.prisma.schedule.findMany({
        where: {
          scheduleZone: { some: { activationId: { in: args.deviceIds } } },
          // mode: args.mode,
          modeValue: {
            hasSome: modeValue,
          },
          // expiredDate: {
          //   gte: expiredDate,
          // },
          // playMode: args.playMode,
          OR: [
            {
              startTime: {
                gte: args.startTime,
                lte: args.endTime,
              },
            },
            {
              endTime: {
                gte: args.startTime,
                lte: args.endTime,
              },
            },
            {
              startTime: {
                gte: args.endTime,
                lte: args.startTime,
              },
            },
            {
              endTime: {
                gte: args.endTime,
                lte: args.startTime,
              },
            },
            {
              AND: [
                { startTime: { lte: args.startTime } },
                { endTime: { gte: args.endTime } },
              ],
            },
          ],
        },
      });

      if (overlappingSchedules.length > 0) {
        throw new BadRequestException(
          'Schedule overlaps with existing schedules',
        );
      }

      // const time = new Date(value).toISOString().substr(11, 8);

      // const item = await this.prisma.$queryRawUnsafe<
      //   string[]
      // >(`
      //   SELECT
      //     "s"."id"
      //   FROM "public"."Schedule" AS "s"
      //   JOIN "public"."ScheduleZone" AS "sz"
      //     ON s.id = sz."scheduleId"
      //   WHERE (CAST("s"."startTime" AS timestamp))::time <= (CAST(${time} AS timestamp))::time
      //     AND (CAST("s"."endTime" AS timestamp))::time > (CAST(${time} AS timestamp))::time
      //     AND "sz"."activationId" = ANY (${deviceId}::text[])
      //     AND "s"."modeValue" @> ${modeValue}::text[]
      //   LIMIT 1 OFFSET 0
      // `);
      // const firstValue = item[0];

      // if (!firstValue && item.length < 1) {
      //   return true;
      // } else {
      //   if (ignore && firstValue['id'] == context.params['id']) {
      //     return true;
      //   } else {
      //     return false;
      //   }
      // }

      const newSchedule = await this.prisma.schedule.create({
        data: {
          name: args.name,
          mode: args.mode,
          startTime: args.startTime,
          endTime: args.endTime,
          expiredDate: expiredDate.toISOString(),
          setVolume: args.setVolume,
          volume: args.volume,
          playMode: args.playMode,
          modeValue: modeValue,
          repeatEnd: args.repeatEnd,
          activation: { connect: { id: user.deviceId } },
          property: { connect: { id: user.propertyId } },
          contents: {
            create: { playlist: { connect: { id: args.playlistId } } },
          },
        },
      });

      if (args.deviceIds && args.deviceIds.length > 0) {
        await this.prisma.scheduleZone.createMany({
          data: args.deviceIds.map((deviceId) => ({
            scheduleId: newSchedule.id,
            activationId: deviceId,
          })),
          skipDuplicates: true,
        });
      }

      const startTime = new Date(newSchedule.startTime);
      const endTime = new Date(newSchedule.endTime);
      const now = new Date();
      const thirtyMinutes = 30 * 60 * 1000;
      const difference = startTime.getTime() - thirtyMinutes;
      // const difference = startTime.getTime() - now.getTime();
      const startNow =
        now.getTime() >= difference && now.getTime() <= endTime.getTime();

      if (startNow || args.playNow) {
        let scheduleType = '';

        if (args.playNow) {
          scheduleType = 'execute';
        } else {
          if (now.getTime() >= startTime.getTime() && now.getTime() <= endTime.getTime()) {
            scheduleType = 'execute'
          } else {
            scheduleType = 'reminder';
          }
        }

        const item = await this.prisma.scheduleReminder.create({
          data: {
            schedule: { connect: { id: newSchedule.id } },
            isRead: false,
          },
        });

        const devices = await this.prisma.activation.findMany({
          where: {
            id: {
              in: args.deviceIds,
            },
          },
        });

        const deviceIds = devices.map((dev) => dev.id);

        const scheduleReminderModel = this.publisher.mergeClassContext(
          ScheduleReminderModel,
        );
        const model = new scheduleReminderModel(item.id);
        model.ScheduleReminderCreated(
          newSchedule.propertyId,
          args.playNow,
          deviceIds,
          scheduleType,
        );
      }

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.decreaseFeatureQty({
        id: FEATURE.Schedule,
        propertyId: user.propertyId,
        deviceId: user.deviceId,
      });

      return 'successfully created schedule';
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      } else {
        console.error('Error creating schedule: ', error.message);
        throw new InternalServerErrorException('failed created schedule');
      }
    }
  }
}
