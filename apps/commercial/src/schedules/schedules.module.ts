import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { EventsModule } from '../events/events.module';
import { ScheduleCommandHandlers } from './commands';
import { ScheduleQueryHandlers } from './queries';
import { SchedulesController } from './schedules.controller';
import { TaskSchedule } from './task.schedule';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AppsModule } from '../app/app.module';

@Module({
  imports: [
    AppsModule,
    CqrsModule,
    EventsModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [SchedulesController],
  providers: [
    ...ScheduleQueryHandlers,
    ...ScheduleCommandHandlers,
    TaskSchedule,
  ],
})
export class SchedulesModule {}
