import { ContextDto } from '@app/common/dto/context.dto';
import { ApiProperty, ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { DateFormat, IsExist } from '@validator/validator';
import {
  ArrayMaxSize,
  ArrayUnique,
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsIn,
  IsMilitaryTime,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Max,
  MaxLength,
  Min,
  ValidateIf,
} from 'class-validator';
import { IsScheduleUnique } from '../../validator/schedule-unique.validate';
import { IsTimeLessThen } from '../../validator/time-less-then.validate';
import { IsTimeMoreThen } from '../../validator/time-more-then.validate';

export class UpdateScheduleDto extends PartialType(ContextDto) {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ required: false })
  @MaxLength(150)
  name: string;
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  // @IsMilitaryTime({
  //   message: 'start time must be a valid format HH:MM',
  // })
  // @IsScheduleUnique(
  //   { ignore: true },
  //   { message: 'You already have a schedule at the same time' },
  // )
  @IsTimeLessThen({ message: 'startTime must be less then endTime' })
  startTime: string;
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  // @IsMilitaryTime({
  //   message: 'end time must be a valid format HH:MM',
  // })
  @IsTimeMoreThen({ message: 'endTime must be more then startTime' })
  // @IsScheduleUnique(
  //   { ignore: true },
  //   { message: 'You already have a schedule at the same time' },
  // )
  endTime: string;
  @ApiProperty({
    enum: $Enums.ScheduleType,
    default: $Enums.ScheduleType.daily,
  })
  @IsEnum($Enums.ScheduleType)
  mode: $Enums.ScheduleType;
  @ApiProperty({ type: [String] })
  @IsArray()
  @ArrayUnique()
  @ArrayMaxSize(7)
  @IsString({ each: true })
  @IsIn(
    [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ],
    { each: true },
  )
  modeValue: string[];
  @ApiProperty()
  @IsString()
  @IsExist({ model: 'playlist', field: 'id' })
  playlistId: string;
  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  repeatEnd: boolean;
  @ApiProperty()
  @IsBoolean()
  playNow: boolean;
  @ApiProperty()
  @IsBoolean()
  setVolume: boolean;
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(100)
  volume: number;
  @ApiProperty({ required: false, format: 'YYYY-MM-DD' })
  @ValidateIf((prop) => prop.checkout != '')
  @DateFormat('YYYY-MM-DD')
  expiredDate?: string;
  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  deviceIds?: string[];
  @ApiProperty({ enum: ['immediately', 'after-song'] })
  @IsEnum(['immediately', 'after-song'])
  playMode: string
}
