import { ApiPropertyOptional } from '@nestjs/swagger';
import { DateFormat } from '@validator/validator';
import { IsArray, IsOptional, IsString, ValidateIf } from 'class-validator';

export class FilterScheduleDto {
  // @ApiPropertyOptional()
  // @IsOptional()
  // @ValidateIf((prop) => prop.date != '')
  // @IsString()
  // @DateFormat('YYYY-MM-DD')
  // date: string;
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.date != '')
  @IsString()
  @DateFormat('YYYY-MM-DD')
  startDate: string;
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.date != '')
  @IsString()
  @DateFormat('YYYY-MM-DD')
  endDate: string;

  @ApiPropertyOptional({ type: [String] }) // Indicate it's an array of strings
  @IsOptional()
  @IsArray()
  @IsString({ each: true }) // Validate each element in the array as a string
  deviceIds?: string[]; 
}
