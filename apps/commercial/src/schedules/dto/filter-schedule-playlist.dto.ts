import { BaseFilterDto } from '@app/common';
import { ApiPropertyOptional, PartialType } from '@nestjs/swagger';
import { DateFormat } from '@validator/validator';
import { IsOptional, IsString, ValidateIf } from 'class-validator';

export class FilterSchedulePlaylistDto extends PartialType(BaseFilterDto) {
  @ApiPropertyOptional()
  @IsOptional()
  @ValidateIf((prop) => prop.date != '')
  @IsString()
  @DateFormat('YYYY-MM-DD')
  date: string;
}
