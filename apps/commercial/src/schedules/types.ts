import { Prisma } from '@prisma/client';

export type ScheduleWithContent = Prisma.ScheduleGetPayload<{
  include: {
    contents: {
      include: {
        playlist: {
          select: {
            id: true;
            name: true;
            color: true;
            _count: { select: { tracks: true } };
          };
        };
      };
    };
  };
}>;

export type SchedulesRaw = {
  id: string;
  propertyId: string;
  playlistId: string;
  startTime: string;
  endTime: string;
  repeatEnd: boolean;
};

export type SchedulePlaylistRaw = {
  playlistId: string;
  trackId: string;
  order: number;
  uri: string;
};

export type ScheduleZoneRow = {
  activationId: string;
  scheduleId: string;
  propertyId: string;
};
