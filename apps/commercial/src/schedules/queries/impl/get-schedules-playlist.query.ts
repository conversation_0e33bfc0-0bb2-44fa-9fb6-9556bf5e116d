import { IQuery } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { FilterSchedulePlaylistDto } from '../../dto/filter-schedule-playlist.dto';

export class GetSchedulesPlaylistQuery implements IQuery {
  constructor(
    public readonly user: ICurrentUser,
    public readonly playlistId: string,
    public readonly args: FilterSchedulePlaylistDto,
  ) {}
}
