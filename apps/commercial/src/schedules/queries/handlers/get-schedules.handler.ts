import { rangeDate } from '@app/common';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetSchedulesQuery } from '../impl';
import { Prisma } from '@prisma/client';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GetSchedulesQuery)
export class GetSchedulesHandler implements IQueryHandler<GetSchedulesQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetSchedulesQuery) {
    const { user, args } = query;
    const current = new Date()
      .toLocaleString('en-US', { weekday: 'long' })
      .toLowerCase();

    let daySchedule = [current];
    if (
      (args.startDate !== undefined && args.startDate !== '') ||
      (args.endDate !== undefined && args.endDate !== '')
    ) {
      daySchedule = rangeDate(args.startDate, args.endDate);
    }

    const allDays = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const weekdayList = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
    ];
    const weekendList = ['saturday', 'sunday'];

    const tgl = args.startDate
      ? new Date(args.startDate)
          .toLocaleString('en-US', { weekday: 'long' })
          .toLowerCase()
      : undefined;

    const weekDay = tgl
      ? weekdayList.includes(tgl)
        ? 'weekday'
        : undefined
      : undefined;

    const weekEnd = tgl
      ? weekendList.includes(tgl)
        ? 'weekend'
        : undefined
      : undefined;

    const everyDay = tgl
      ? allDays.includes(tgl)
        ? 'daily'
        : undefined
      : undefined;

    // const whereClause: Prisma.ScheduleWhereInput = {
    //   AND: [
    //     { property: { id: user.propertyId } },
    //     {
    //       OR: [
    //         { mode: weekDay },
    //         { mode: weekEnd },
    //         { mode: everyDay },
    //         { modeValue: tgl ? { hasEvery: [tgl] } : undefined },
    //       ],
    //     },
    //   ],
    // };

    // if (!Array.isArray(whereClause.AND)) {
    //   whereClause.AND = [];
    // }

    // if (args.deviceIds && args.deviceIds.length > 0) {
    //   whereClause.AND = [
    //     ...whereClause.AND,
    //     {
    //       scheduleZone: {
    //         some: {
    //           activationId: { in: args.deviceIds },
    //         },
    //       },
    //     },
    //   ];
    // }

    const conditions = [];

    if (user.isAdmin && user.isMultizone) {
      conditions.push({ property: { id: user.propertyId } });
    } else {
      conditions.push({ property: { id: user.propertyId } });
      conditions.push({ OR : [
        {
          scheduleZone: {
            some: {
              activationId: user.deviceId
            }
          }
        },
        {
          activation: { id: user.deviceId }
        }
      ]})
    }

    if (daySchedule.length > 0) {
      conditions.push({ modeValue: { hasSome: daySchedule } });
    }

    const items = await this.prisma.schedule.findMany({
      orderBy: { startTime: 'asc' },
      where: {
        AND: conditions.length > 0 ? conditions : undefined,
      },
      include: {
        contents: {
          where: { playlist: { isNot: null } },
          include: {
            playlist: {
              select: {
                id: true,
                name: true,
                color: true,
                _count: { select: { tracks: true } },
                tracks: {
                  include: {
                    track: true,
                  },
                },
              },
            },
          },
        },
        scheduleZone: true,
      },
    });

    const serializeData = items.map((item) => {
      return {
        id: item.id,
        name: item.name,
        mode: item.mode,
        modeValue: item.modeValue,
        startTime: item.startTime,
        endTime: item.endTime,
        setVolume: item.setVolume,
        volume: item.volume,
        repeatEnd: item.repeatEnd,
        createdAt: item.createdAt,
        expiredDate: item.expiredDate,
        thumbnail: item?.contents?.playlist?.tracks[0]?.track?.source[
          'images'
        ] || [
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_64_URL'),
            width: 64,
            height: 64,
          },
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_300_URL'),
            width: 300,
            height: 300,
          },
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_640_URL'),
            width: 640,
            height: 640,
          },
        ],
        playlist: item.contents?.playlist
          ? {
              id: item.contents.playlist.id,
              name: item.contents.playlist.name,
              color: item.contents.playlist.color,
              duration: Number(
                item.contents.playlist.tracks.reduce(
                  (sum, item) => sum + (item.track?.source?.['duration'] || 0),
                  0,
                ),
              ),
              _count: item.contents.playlist._count,
            }
          : null,
        deviceIds: item.scheduleZone.map((sd) => sd.activationId),
      };
    });
    return serializeData;
  }
}
