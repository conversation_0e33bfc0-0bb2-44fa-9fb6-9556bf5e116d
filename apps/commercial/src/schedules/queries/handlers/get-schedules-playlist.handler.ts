import { Pagination } from '@app/common';
import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { Prisma, Schedule } from '@prisma/client';
import { PrismaService } from 'libs/prisma'; 
import { GetSchedulesPlaylistQuery } from '../impl';

@QueryHandler(GetSchedulesPlaylistQuery)
export class GetSchedulesPlaylistHandler
  implements IQueryHandler<GetSchedulesPlaylistQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetSchedulesPlaylistQuery) {
    const { user, playlistId, args } = query;
    const tgl = args.date
      ? new Date(args.date)
          .toLocaleString('en-US', { weekday: 'long' })
          .toLowerCase()
      : undefined;

    const allDays = [
      'sunday',
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
    ];
    const weekdayList = [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
    ];
    const weekendList = ['saturday', 'sunday'];

    const weekDay = tgl
      ? weekdayList.includes(tgl)
        ? 'weekday'
        : undefined
      : undefined;

    const weekEnd = tgl
      ? weekendList.includes(tgl)
        ? 'weekend'
        : undefined
      : undefined;

    const everyDay = tgl
      ? allDays.includes(tgl)
        ? 'daily'
        : undefined
      : undefined;
    const items = await Pagination<Schedule, Prisma.ScheduleFindManyArgs>(
      this.prisma.schedule,
      {
        orderBy: { startTime: 'asc' },
        where: {
          AND: [
            { contents: { playlist: { id: playlistId } } },
            { property: { id: user.propertyId } },
            user.isAdmin ? { activation: { id: user.deviceId } } : null,
            {
              OR: [
                { mode: weekDay },
                { mode: weekEnd },
                { mode: everyDay },
                { modeValue: tgl ? { hasEvery: [tgl] } : undefined },
              ],
            },
          ],
        },
      },
      { page: args.page, limit: args.limit, exclude: ['propertyId'] },
    );
    return items;
  }
}
