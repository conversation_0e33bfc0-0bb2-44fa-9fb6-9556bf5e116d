import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetScheduleQuery } from '../impl/get-schedule.query';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GetScheduleQuery)
export class GetScheduleHandler implements IQueryHandler<GetScheduleQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetScheduleQuery) {
    const { user, id } = query;
    const conditions = [];
    conditions.push({ id });

    if (user.isAdmin) {
      conditions.push({ property: { id: user.propertyId } });
    } else {
      conditions.push({ property: { id: user.propertyId } });
      conditions.push({ OR : [
        {
          scheduleZone: {
            some: {
              activationId: user.deviceId
            }
          }
        },
        {
          activation: { id: user.deviceId }
        }
      ]})
    }
    const item = await this.prisma.schedule.findFirst({
      where: { AND: conditions.length > 0 ? conditions : undefined },
      include: {
        contents: {
          include: {
            playlist: true
          }
        },
        scheduleZone: {
          include: {
            activation: {
              include: {
                activationDevice: {
                  include: {
                    device: true,
                  },
                  orderBy: {
                    updatedAt: 'desc'
                  },
                  take: 1
                },
              },
            }
          }
        }
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    
    const sampleImage = await this.prisma.playlist.findFirst({
      where: {
        id: item.contents?.playlist?.id
      },
      include: {
        tracks: {
          include: {
            track: true
          }
        }
      }
    });

    if (item.scheduleZone.length > 0) {
      item.scheduleZone.map((dev) => {
        if (dev.activation.activationDevice.length > 0) {
          return {
            id: dev.activation.id,
            zone: dev.activation.zone,
            name: dev.activation.activationDevice[0]?.device?.name
          }
        }
      })
    }

    return Object.assign(item, {
      thumbnail: sampleImage?.tracks[0]?.track?.source['images'] || [
        {
          url: this.configService.get<string>('DEFAULT_IMAGE_64_URL'),
          width: 64,
          height: 64
        },
        {
          url: this.configService.get<string>('DEFAULT_IMAGE_300_URL'),
          width: 300,
          height: 300
        },
        {
          url: this.configService.get<string>('DEFAULT_IMAGE_640_URL'),
          width: 640,
          height: 640
        },
      ],
    });
  }
}
