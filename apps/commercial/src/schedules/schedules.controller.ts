import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  CreateScheduleCommand,
  DeleteScheduleCommand,
  UpdateScheduleCommand,
} from './commands';
import { CreateScheduleDto } from './dto/create-schedule.dto';
import { FilterSchedulePlaylistDto } from './dto/filter-schedule-playlist.dto';
import { FilterScheduleDto } from './dto/filter-schedule.dto';
import { UpdateScheduleDto } from './dto/update-schedule.dto';
import {
  GetScheduleQuery,
  GetSchedulesPlaylistQuery,
  GetSchedulesQuery,
} from './queries';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';
@ApiTags('Schedules')
@Controller('schedules')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SchedulesController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  create(
    @User() user: ICurrentUser,
    @Body() createScheduleDto: CreateScheduleDto,
  ) {
    return this.commandBus.execute(
      new CreateScheduleCommand(user, createScheduleDto),
    );
  }

  @Get()
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  findAll(@User() user: ICurrentUser, @Query() filter: FilterScheduleDto) {
    return this.queryBus.execute(new GetSchedulesQuery(user, filter));
  }

  @Get(':id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetScheduleQuery(user, id));
  }

  @Get('by/:playlistId')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  findBy(
    @User() user: ICurrentUser,
    @Param('playlistId') playlistId: string,
    @Query() filter: FilterSchedulePlaylistDto,
  ) {
    return this.queryBus.execute(
      new GetSchedulesPlaylistQuery(user, playlistId, filter),
    );
  }

  @Patch(':id')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  update(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Body() updateScheduleDto: UpdateScheduleDto,
  ) {
    return this.commandBus.execute(
      new UpdateScheduleCommand(user, id, updateScheduleDto),
    );
  }

  @Delete(':id')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  remove(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.commandBus.execute(new DeleteScheduleCommand(user, id));
  }
}
