import { Inject, Injectable, Logger } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'libs/prisma';
import { PlayerGateway } from '../events/player.gateway';
import { CreateScheduleReminderCommand } from '../schedule-reminder/commands';
import { SchedulesRaw, ScheduleZoneRow } from './types';
import { ClientProxy } from '@nestjs/microservices';
import { AppService } from '../app/app.service';

@Injectable()
export class TaskSchedule {
  private readonly logger = new Logger(TaskSchedule.name);
  constructor(
    private prisma: PrismaService,
    private playerGateway: PlayerGateway,
    private commandBus: CommandBus,
    @Inject('mobile') private client: ClientProxy,
    private appService: AppService,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTask() {
    const data = await this.appService.checkMaintenanceStatus();
    if (data) {
      return true;
    }

    const tgl = new Date();
    const dayName = tgl
      .toLocaleString('en-Us', {
        weekday: 'long',
      })
      .toLowerCase();
    const hour = `${('0' + tgl.getUTCHours()).slice(-2)}:${('0' + tgl.getUTCMinutes()).slice(-2)}:00`;
    const schedules = await this.prisma.$queryRawUnsafe<SchedulesRaw[]>(
      `
      SELECT
        "sc"."id",
        "sc"."repeatEnd",
        "sc"."startTime",
        "sc"."propertyId",
        "scc"."playlistId",
        "sc"."expiredDate"
      FROM "public"."ScheduleContent" as "scc"
      INNER JOIN "public"."Schedule" as "sc" ON "scc"."scheduleId" = "sc"."id"
      WHERE ("sc"."modeValue" @> $1::text[]
      AND (CAST("sc"."startTime" AS timestamp))::time = $2::time)
      AND "sc"."expiredDate"::timestamptz != $3::timestamptz
      OFFSET 0`,
      [dayName],
      hour,
      tgl.toISOString(),
    );

    // console.log([dayName], hour, tgl.toISOString(), schedules);
    if (schedules.length > 0) {
      // const scheduleIds = [
      //   ...new Set(
      //     schedules.map((sc) => {
      //       return `'${sc.id}'`;
      //     }),
      //   ),
      // ].join(',');

      for (let idx = 0; idx < schedules.length; idx++) {
        const elm = schedules[idx];
        const zones = await this.prisma.$queryRawUnsafe<ScheduleZoneRow[]>(
          `
          SELECT sz."activationId" , sz."scheduleId", a."propertyId"
          FROM "public"."Activation" a 
          INNER JOIN "public"."ScheduleZone" sz ON sz."activationId" = a.id
          WHERE sz."scheduleId" = $1
          OFFSET 0`,
          elm.id,
        );

        // console.log('CEKK ZONES ', zones);
        await this.commandBus.execute(
          new CreateScheduleReminderCommand({
            scheduleId: elm.id,
            propertyId: elm.propertyId,
            zonesId: zones.filter(
              (o) => o.propertyId == elm.propertyId,
            ) as unknown as object[],
            type: 'execute',
          }),
        );
      }
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async stopSchedule() {
    const data = await this.appService.checkMaintenanceStatus();
    if (data) {
      return true;
    }

    const tgl = new Date();
    const dayName = tgl
      .toLocaleString('en-Us', {
        weekday: 'long',
      })
      .toLowerCase();

    const hour = `${('0' + tgl.getUTCHours()).slice(-2)}:${('0' + tgl.getUTCMinutes()).slice(-2)}:00`;
    const schedules = await this.prisma.$queryRawUnsafe<SchedulesRaw[]>(
      `
      SELECT
        "sc"."id",
        "sc"."repeatEnd",
        "sc"."startTime",
        "sc"."endTime",
        "sc"."propertyId",
        "scc"."playlistId",
        "sc"."expiredDate"
      FROM "public"."ScheduleContent" as "scc"
      INNER JOIN "public"."Schedule" as "sc"
        ON "scc"."scheduleId" = "sc"."id"
      WHERE ("sc"."modeValue" @> $1::text[]
      AND (CAST("sc"."endTime" AS timestamp))::time = $2::time)
      AND "sc"."expiredDate"::timestamptz >= $3::timestamptz
      OFFSET 0`,
      [dayName],
      hour,
      tgl.toISOString(),
    );

    if (schedules.length > 0) {
      const scheduleIds = [
        ...new Set(
          schedules.map((sc) => {
            return `'${sc.id}'`;
          }),
        ),
      ].join(',');

      const zones = await this.prisma.$queryRawUnsafe<ScheduleZoneRow[]>(
        `
        SELECT sz."activationId" , sz."scheduleId", a."propertyId"
        FROM "public"."Activation" a 
        INNER JOIN "public"."ScheduleZone" sz ON sz."activationId" = a.id
        WHERE sz."scheduleId" IN (${scheduleIds})
        OFFSET 0`,
      );

      for (let idx = 0; idx < schedules.length; idx++) {
        const elm = schedules[idx];
        const zoneIds = zones
          .filter((o) => o.propertyId == elm.propertyId)
          .map((o) => o.activationId);

        // if (zoneIds.length > 0) {
        //   this.playerGateway.server
        //     .to(zoneIds)
        //     .emit('schedule:stop', { scheduleId: elm.id });

        //   this.client.emit('schedule-stop', {
        //     property_id: elm.propertyId,
        //     schedule_id: elm.id,
        //     zones: zoneIds,
        //   });
        // }

        if (zoneIds.length > 0) {
          const startAt = new Date(elm.startTime);
          const endAt = new Date(elm.endTime);

          if (
            startAt.toISOString().substr(11, 8) !== '17:00:00' &&
            endAt.toISOString().substr(11, 8) !== '16:59:00'
          ) {
            this.playerGateway.server
              .to(zoneIds)
              .emit('schedule:stop', { scheduleId: elm.id });

            this.client.emit('schedule-stop', {
              property_id: elm.propertyId,
              schedule_id: elm.id,
              zones: zoneIds,
            });
          }
        }
      }
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async Reminder() {
    const data = await this.appService.checkMaintenanceStatus();
    if (data) {
      return true;
    }

    const tgl = new Date();
    const dayName = tgl
      .toLocaleString('en-Us', {
        weekday: 'long',
      })
      .toLowerCase();

    const hour = `${('0' + tgl.getUTCHours()).slice(-2)}:${('0' + tgl.getUTCMinutes()).slice(-2)}:00`;

    const schedules = await this.prisma.$queryRawUnsafe<SchedulesRaw[]>(
      `
      SELECT
        "sc"."id",
        "sc"."repeatEnd",
        "sc"."startTime",
        "sc"."propertyId",
        "scc"."playlistId",
        "sc"."expiredDate"
      FROM "public"."ScheduleContent" as "scc"
      INNER JOIN "public"."Schedule" as "sc"
      ON "scc"."scheduleId" = "sc"."id"
      WHERE ("sc"."modeValue" @> $1::text[]
      AND ((CAST("sc"."startTime" AS timestamp))::time - INTERVAL '30 Minutes') = $2::time)
      AND "sc"."expiredDate"::timestamptz >= $3::timestamptz
      OFFSET 0`,
      [dayName],
      hour,
      tgl.toISOString(),
    );

    if (schedules.length > 0) {
      const scheduleIds = [
        ...new Set(
          schedules.map((sc) => {
            return `'${sc.id}'`;
          }),
        ),
      ].join(',');

      const zones = await this.prisma.$queryRawUnsafe<ScheduleZoneRow[]>(
        `
        SELECT sz."activationId" , sz."scheduleId", a."propertyId"
        FROM "public"."Activation" a 
        INNER JOIN "public"."ScheduleZone" sz ON sz."activationId" = a.id
        WHERE sz."scheduleId" IN (${scheduleIds})
        OFFSET 0`,
      );

      // const devices = await this.prisma.$queryRawUnsafe<ScheduleZoneRow[]>(
      //   `
      //   SELECT
      //     "d"."id",
      //     "a"."propertyId",
      //     "d"."type",
      //     "d"."serialNumber"
      //   FROM "public"."Device" as "d"
      //   INNER JOIN "public"."ScheduleDevice" as "sd" on "d".id = "sd"."deviceId"
      //   INNER JOIN "public"."ActivationDevice" as "ad" on "d".id = "ad"."deviceId"
      //   INNER JOIN "public"."Activation" as "a" on "ad"."activationId" = "a"."id"
      //   WHERE ("sd"."scheduleId" IN (${scheduleIds}))
      //   AND "d".type IN ('web', 'mob')
      //   OFFSET 0`,
      // );

      for (let idx = 0; idx < schedules.length; idx++) {
        const elm = schedules[idx];
        await this.commandBus.execute(
          new CreateScheduleReminderCommand({
            scheduleId: elm.id,
            propertyId: elm.propertyId,
            zonesId: zones.filter(
              (o) => o.propertyId == elm.propertyId,
            ) as unknown as object[],
            type: 'reminder',
          }),
        );
      }
    }
  }

  @Cron(CronExpression.EVERY_MINUTE)
  async autoReadReminder() {
    const data = await this.appService.checkMaintenanceStatus();
    if (data) {
      return true;
    }

    const tgl = new Date();
    // const hour = `${('0' + tgl.getHours()).slice(-2)}:${('0' + tgl.getMinutes()).slice(-2)}:00`;
    await this.prisma.$executeRaw`
      UPDATE
        "ScheduleReminder" AS "sr"
      SET "isRead" = true
      FROM "Schedule" AS "s"
      WHERE (("sr"."scheduleId" = "s"."id")
      AND "s"."endTime"::timestamptz <= ${tgl.toISOString()}::timestamptz
      AND "isRead"=false
      AND "s"."expiredDate"::timestamptz >= ${tgl}::timestamptz)
    `;
  }
}
