import { Id, Status } from '@app/proto-schema/index.common';
import { CancelOrderReq, CheckoutResp, CheckVoucherExistResponse, ORDER_PAYMENT_SERVICE_NAME, OrderPaymentServiceController, Voucher, VOUCHER_SERVICE_NAME, VoucherCodeRequest, VoucherServiceController } from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { CancelOrderPaymentCommand, PaymentGatewayCommand } from './commands';

@Controller()
export class OrderPaymentRpcController implements OrderPaymentServiceController {
  constructor(
    private readonly commandBus: CommandBus,
  ) {}
  @GrpcMethod(ORDER_PAYMENT_SERVICE_NAME, 'cancelOrder')
  cancelOrder(request: CancelOrderReq, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new CancelOrderPaymentCommand(request),
    );
  }
  @GrpcMethod(ORDER_PAYMENT_SERVICE_NAME, 'orderCheckout')
  orderCheckout(request: Id, metadata: Metadata, ...rest: any): Promise<CheckoutResp> | Observable<CheckoutResp> | CheckoutResp {
    return this.commandBus.execute(
      new PaymentGatewayCommand(request.id),
    );
  }
}
