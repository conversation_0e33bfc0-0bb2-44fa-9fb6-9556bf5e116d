import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { OrderPaymentCommandHandlers } from './commands';
import { HttpModule } from '@nestjs/axios';
import { MembershipClientModule } from '../membership-client/membership-client.module';
import { OrderPaymentRpcController } from './order-payment.rpc.controller';

@Module({
  imports: [
    CqrsModule,
    HttpModule,
    MembershipClientModule
  ],
  controllers: [
    OrderPaymentRpcController,
  ],
  providers: [...OrderPaymentCommandHandlers],
})
export class OrderPaymentModule {}
