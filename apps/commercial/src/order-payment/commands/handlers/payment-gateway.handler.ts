import { <PERSON><PERSON>us, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { PaymentGatewayCommand } from '../impl';
import { dayjs, generateMd5 } from '@app/common';
import { ITokenIseller } from 'apps/commercial/src/iseller/types';
import { TokenIsellerCommand } from 'apps/commercial/src/iseller/commands';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { OrderService } from 'apps/commercial/src/membership-client/order.service';
import { PropertyService } from 'apps/commercial/src/membership-client/property.service';

@CommandHandler(PaymentGatewayCommand)
export class PaymentGatewayHandler
  implements ICommandHandler<PaymentGatewayCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private commandBus: CommandBus,
    private httpService: HttpService,
    private readonly orderRpcService: OrderService,
    private readonly propertyRpcService: PropertyService,
  ) {}

  async execute(command: PaymentGatewayCommand) {
    const { orderId } = command;

    try {
      const order = await lastValueFrom(
        this.orderRpcService.getOrderDetail(orderId),
      );

      const property = await lastValueFrom(
        this.propertyRpcService.getPropertyUser(order.propertyId),
      );

      const token: ITokenIseller = await this.commandBus.execute(
        new TokenIsellerCommand(),
      );

      const config = await this.prisma.setting.findFirst({
        where: {
          type: 'iseller',
        },
      });

      if (!config) {
        throw new RpcException({
          code: status.INTERNAL,
          message: 'Order payment failed',
        });
      }
      const data = {
        amount: order.totalPrice,

        items: order.orderDetail
          .filter((detail) => detail.itemType !== 'LICENSE')
          .map((detail) => ({
            quantity: Number(detail.qty),
            name: detail.name,
            sku: detail.sku,
            id: detail.id,
            price: detail.totalPrice / Number(detail.qty),
          })),

        customer: {
          firstname: `${property?.property?.brandName} (${property?.property.companyName})`,
          id: property?.property?.id,
          email: property?.property?.companyEmail,
        },
        setting: {
          expired_date: dayjs().add(1, 'day'),
          payment_methods: order.orderDetail.some(
            (item) => item.duration === 'monthly',
          )
            ? ['creditcard', 'bank_transfer']
            : [
                'bank_transfer',
                'ebanking',
                'ebanking',
                'mbanking',
                'creditcard',
                'convenient_store',
                'gopay',
                'ovo',
                'shopeepay',
                'dana',
                'yukk',
                'voucher',
              ],
          disable_payment_email: false,
          ui_mode: 'standard',
        },
        signature: '',
      };

      const itemIds = data.items.map((item) => item.id).join('.');

      const signatureString = `${config.option['clientId']}.${itemIds}.${data.amount}.${config.option['clientSecret']}`;
      const signature = generateMd5(signatureString).toUpperCase();

      data.signature = signature;

      const requestPayment = await lastValueFrom(
        this.httpService.post(config.option['paymentUrl'], data, {
          headers: {
            'Content-Type': 'application/json',
            'X-Notification-Url': config.option['hookUrl'],
            Authorization: `Bearer ${token.access_token}`,
          },
          timeout: 15000,
        }),
      );

      if (!order.orderPayment) {
        const dateNow = new Date();
        await this.orderRpcService
          .updateWaitingOrder(
            orderId,
            false,
            requestPayment.data.payment_request_id,
            requestPayment.data.checkout_url,
            dateNow.setDate(dateNow.getDate() + 1).toString(),
          )
          .toPromise();
      }

      return {
        checkoutUrl: requestPayment.data.checkout_url,
        paymentRequestId: requestPayment.data.payment_request_id,
        status: requestPayment.data.status,
        error: requestPayment.data.error_message,
        time: requestPayment.data.time,
      };
    } catch (err) {
      console.log(err);
      if (err.code == 5) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: err.message,
        });
      } else {
        throw new RpcException({
          code: status.INTERNAL,
          message: 'Internal Server Error',
        });
      }
    }
  }
}
