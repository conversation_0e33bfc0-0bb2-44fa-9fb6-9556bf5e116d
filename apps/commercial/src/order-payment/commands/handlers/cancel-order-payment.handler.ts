import { <PERSON>Bus, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CancelOrderPaymentCommand } from '../impl';
import { ITokenIseller } from 'apps/commercial/src/iseller/types';
import { TokenIsellerCommand } from 'apps/commercial/src/iseller/commands';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@CommandHandler(CancelOrderPaymentCommand)
export class CancelOrderPaymentHandler
  implements ICommandHandler<CancelOrderPaymentCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private commandBus: CommandBus,
    private httpService: HttpService,
  ) { }

  async execute(command: CancelOrderPaymentCommand) {
    const { args } = command;

    const token: ITokenIseller = await this.commandBus.execute(
      new TokenIsellerCommand(),
    );

    const config = await this.prisma.setting.findFirst({
      where: {
        type: 'iseller'
      }
    });

    if (!config) {
      throw new RpcException({ code: status.INTERNAL, message: 'cancel order payment failed' })
    }

    try {
      await lastValueFrom(
        this.httpService.post(
          config.option['cancelPaymentUrl'],
          {
            payment_id: args.paymentId,
            reason: args.reason
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'X-Notification-Url': config.option['hookUrl'],
              Authorization: `Bearer ${token.access_token}`,
            },
            timeout: 15000,
          },
        ),
      );

      return {
        code: status.OK,
        message: 'success cancel order payment'
      };
    } catch (err) {
      console.log(err)
      if (err.status == 404) {
        throw new RpcException({ code: status.NOT_FOUND, message: err.message });
      } else {
        throw new RpcException({ code: status.INTERNAL, message: 'Internal Server Error' })
      }
    }
  }
}