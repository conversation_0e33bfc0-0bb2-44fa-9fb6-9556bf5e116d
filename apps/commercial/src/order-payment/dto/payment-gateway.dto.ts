import { ApiProperty } from '@nestjs/swagger';
import {
  Allow,
  IsArray,
  IsBoolean,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';

export class ItemsDto {
  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  quantity: number;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  sku: string;

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsNumber()
  price: number;
}

class CustomersDto {
  @ApiProperty()
  @IsString()
  firstname: string;

  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsString()
  email: string;
}

class SettingDto {
  @ApiProperty()
  @IsString()
  expired_date: string;

  @ApiProperty({ type: Array })
  @IsArray()
  payment_methods: string[];

  @ApiProperty({ default: true })
  @IsBoolean()
  disable_payment_email: boolean;
}

export class PaymentGatewayDto {
  @ApiProperty()
  @IsNumber()
  amount: number;

  @ApiProperty({ type: [ItemsDto] })
  @Allow()
  items: ItemsDto[];

  @ApiProperty({ type: () => CustomersDto })
  @Allow()
  customer: CustomersDto;

  @ApiProperty({ type: () => SettingDto })
  @Allow()
  setting: SettingDto;
}
