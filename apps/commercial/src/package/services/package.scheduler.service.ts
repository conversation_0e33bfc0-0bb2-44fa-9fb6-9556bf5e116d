import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'libs/prisma';
import { OrderService } from '../../membership-client/order.service';
import { firstValueFrom } from 'rxjs';
import { RenewalType } from '@app/proto-schema/index.membership';

@Injectable()
export class PackageSchedulerService {
  private readonly logger = new Logger(PackageSchedulerService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly orderService: OrderService,
  ) {}

  private async findPackages(dateField: 'expiredAt' | 'contractEndAt') {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return this.prisma.package.findMany({
      where: {
        [dateField]: {
          equals: BigInt(today.getTime()),
        },
        status: 'active',
      },
      include: {
        property: true,
      },
    });
  }

  private async processRenewalOrders(
    packages: any[],
    renewalType: RenewalType,
    logPrefix: string,
  ) {
    return Promise.all(
      packages.map(async (pkg) => {
        try {
          await firstValueFrom(
            this.orderService.renewOrder(
              pkg.crmPropertyId,
              pkg.itemType,
              pkg.itemId,
              renewalType,
            ),
          );
          this.logger.log(
            `Created ${logPrefix} renewal order for package ${pkg.id}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to create ${logPrefix} renewal order for package ${pkg.id}: ${error.message}`,
          );
        }
      }),
    );
  }

  @Cron('1 * * * * *')
  async handleExpiredPackages() {
    try {
      const packages = await this.findPackages('expiredAt');
      // console.log(packages);
      await this.processRenewalOrders(packages, RenewalType.CONTRACT, '');
    } catch (error) {
      this.logger.error(`Error in handleExpiredPackages: ${error.message}`);
      throw error;
    }
  }

  @Cron('0 0 0 * * *')
  async handleContractEndPackages() {
    try {
      const packages = await this.findPackages('contractEndAt');
      await this.processRenewalOrders(
        packages,
        RenewalType.SUBSCRIPTION,
        'contract',
      );
    } catch (error) {
      this.logger.error(`Error in handleContractEndPackages: ${error.message}`);
      throw error;
    }
  }
}
