import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPackageFeaturesQuery } from '../impl';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetPackageFeaturesQuery)
export class GetPackageFeaturesHandler implements IQueryHandler<GetPackageFeaturesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPackageFeaturesQuery) {
    const { propertyId, id } = query;
    const item = await this.prisma.package.findFirst({
      where: {
        AND: [
          {
            id
          },
          {
            propertyId: propertyId
          }
        ]
      },
      include: {
        features: {
          include: {
            feature: true
          }
        },
      }
    });

    if (!item) {
      throw new NotFoundException('package not found');
    }

    return item.features;
  }
}
