import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetListExpiredPackageQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import {
  ListExpiredPackageResponse,
  PropertyWithPackages,
} from '@app/proto-schema/index.internal';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetListExpiredPackageQuery)
export class GetListExpiredPackageHandler
  implements IQueryHandler<GetListExpiredPackageQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(
    query: GetListExpiredPackageQuery,
  ): Promise<ListExpiredPackageResponse> {
    const { Ids } = query;
    const currentTimestamp = Date.now();
    const properties = await this.prisma.property.findMany({
      where: {
        AND: [
          {
            crmPropertyId: {
              in: Ids,
            },
          },
          {
            packages: {
              some: {
                OR: [
                  {
                    isActive: false,
                  },
                  {
                    expiredAt: {
                      lte: currentTimestamp,
                    },
                  },
                ],
              },
            },
          },
        ],
      },
      include: {
        packages: {
          where: {
            OR: [
              {
                isActive: false,
              },
              {
                expiredAt: {
                  lte: currentTimestamp,
                },
              },
            ],
          },
          include: {
            features: true,
          },
        },
      },
    });

    if (!properties) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'property not found',
      });
    }

    const formattedData: PropertyWithPackages[] = properties.map(
      (property) => ({
        property: {
          id: property.id,
          cid: property.cid,
          companyName: property.companyName,
          brandName: property.brandName,
          companyEmail: property.companyEmail,
          companyPhoneNumber: property.companyPhoneNumber,
          npwp: property.npwp,
          address: property.address,
          createdAt: property.createdAt.toISOString(),
          updatedAt: property.updatedAt.toISOString(),
          postalId: property.postalId,
          propertyTypeId: property.propertyTypeId,
          configuration: null,
        },
        packages: property.packages.map((pkg) => ({
          id: pkg.id,
          name: pkg.name,
          status: pkg.status,
          itemType: pkg.itemType,
          itemId: pkg.itemId,
          isActive: pkg.isActive,
          activeAt: pkg.activeAt ? Number(pkg.activeAt) : 0,
          expiredAt: pkg.expiredAt ? Number(pkg.expiredAt) : 0,
          contractEndAt: pkg.contractEndAt ? Number(pkg.contractEndAt) : 0,
          trialEndAt: pkg.trialEndAt ? Number(pkg.trialEndAt) : 0,
          isTrial: pkg.isTrial,
          createdAt: pkg.createdAt.toISOString(),
          updatedAt: pkg.updatedAt.toISOString(),
          propertyId: pkg.propertyId,
          packageFeature: pkg.features,
        })),
      }),
    );

    return { data: formattedData };
  }
}
