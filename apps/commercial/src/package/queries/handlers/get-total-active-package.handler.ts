import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetTotalActivePackageQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { GetTotalActivePackageResponse } from '@app/proto-schema/index.internal';
import { NotFoundException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetTotalActivePackageQuery)
export class GetTotalActivePackageHandler
  implements IQueryHandler<GetTotalActivePackageQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(
    query: GetTotalActivePackageQuery,
  ): Promise<GetTotalActivePackageResponse> {
    const currentTimestamp = Date.now();
    const packages = await this.prisma.package.findMany({
      where: {
        AND: [
          {
            OR: [
              {
                isActive: true,
              },
              {
                expiredAt: {
                  gt: currentTimestamp,
                },
              },
            ],
          },
          {
            property: {
              crmPropertyId: { in: query.Ids },
            },
          },
        ],
      },
      select: {
        id: true,
      },
    });
    if (!packages) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Package not found',
      });
    }

    return { total: packages.length };
  }
}
