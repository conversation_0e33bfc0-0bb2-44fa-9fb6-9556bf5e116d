import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPackageQuery, GetPackagesQuery } from '../impl';
import { NotFoundException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';

@QueryHandler(GetPackageQuery)
export class GetP<PERSON>ageHandler implements IQueryHandler<GetPackageQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPackageQuery) {
    const { propertyId, id, transport } = query;
    const item = await this.prisma.package.findFirst({
      where: {
        AND: [
          {
            id
          },
          {
            propertyId: propertyId
          }
        ]
      },
      include: {
        features: {
          include: {
            feature: true
          }
        },
        plan: {
          include: {
            propertyType: true
          }
        }
      }
    });

    if (!item) {
      if (transport === 'grpc') {
        throw new RpcException({ code: status.NOT_FOUND, message: 'package not found'});
      } else {
        throw new NotFoundException('package not found');
      }
    }

    // return {
    //   id: item.id,
    //   name: item.name,
    //   isActive: item.isActive,
    //   activeAt: item.activeAt,
    //   expiredAt: item.expiredAt,
    //   plan: item.plan
    // };
    return item;
  }
}
