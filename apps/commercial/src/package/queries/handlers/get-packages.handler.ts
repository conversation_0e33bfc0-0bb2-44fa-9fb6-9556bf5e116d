import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPackageQuery, GetPackagesQuery } from '../impl';

@QueryHandler(GetPackagesQuery)
export class GetPackagesHandler implements IQueryHandler<GetPackagesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPackagesQuery) {
    const { propertyId, args } = query;
    return await this.prisma.package.findMany({
      where: {
        propertyId: propertyId
      },
      include: {
        features: {
          include: {
            feature: true
          }
        }
      }
    });
  }
}
