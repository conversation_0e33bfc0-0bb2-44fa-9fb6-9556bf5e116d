import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetListPackageQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { NotFoundException } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';

@QueryHandler(GetListPackageQuery)
export class GetListPackageQueryHandler
  implements IQueryHandler<GetListPackageQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetListPackageQuery) {
    const { propertyId, args = {} } = query;

    const prop = await this.prisma.property.findFirst({
      where: { crmPropertyId: propertyId },
    });

    if (!prop) {
      return [];
    }

    const result = await this.prisma.package.findMany({
      where: {
        propertyId: prop.id,
        isActive: true,
        status: 'active',
      },
      include: {
        features: {
          include: {
            feature: true,
          },
        },
        property: {
          include: {
            activation: true,
          },
        },
      },
    });

    const totalIsusedZone = result
      .flatMap((pkg) => pkg.property?.activation || [])
      .filter((act) => act.isUsed === true)
      .reduce((uniqueIds, act) => {
        uniqueIds.add(act.id);
        return uniqueIds;
      }, new Set());

    // console.log('totalIsusedZone ', totalIsusedZone.size);

    const formattedResult = result.map((pkg) => ({
      id: pkg.id,
      name: pkg.name,
      status: pkg.status || '',
      itemType: pkg.itemType || '',
      itemId: pkg.itemId || '',
      isActive: pkg.isActive,
      activeAt: pkg.activeAt
        ? this.formatDateToDDMMYYYY(Number(pkg.activeAt))
        : null,
      expiredAt: pkg.expiredAt ? Number(pkg.expiredAt) : 0,
      contractEndAt: pkg.contractEndAt ? Number(pkg.contractEndAt) : 0,
      trialEndAt: pkg.trialEndAt ? Number(pkg.trialEndAt) : 0,
      isTrial: pkg.isTrial || false,
      billingCycle: pkg.duration,
      createdAt: pkg.createdAt.toISOString(),
      updatedAt: pkg.updatedAt.toISOString(),
      propertyId: pkg.propertyId || '',
      totalUsedActivation: totalIsusedZone.size.toString(),
      packageFeature: pkg.features.map((feature) => ({
        id: feature.feature.id,
        name: feature.feature.name,
        valueType: feature.feature.valueType || '',
        qty: feature.qty || 0,
        quota: totalIsusedZone.size,
      })),
    }));
    return formattedResult;
  }

  private formatDateToDDMMYYYY(
    timestamp: number,
    timeZone: string = 'Asia/Jakarta',
  ): string {
    const date = new Date(timestamp);
    const formatter = new Intl.DateTimeFormat('id-ID', {
      timeZone,
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
    return formatter.format(date);
  }
}
