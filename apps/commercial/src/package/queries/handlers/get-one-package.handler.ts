import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { GetOnePackageQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { InternalServerErrorException } from '@nestjs/common';

@QueryHandler(GetOnePackageQuery)
export class GetOnePackageHandler implements IQueryHandler<GetOnePackageQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetOnePackageQuery) {
    const { args } = query;

    // console.log('args', args);

    try {
      const prop = await this.prisma.property.findFirst({
        where: {
          crmPropertyId: args.orderId,
        },
      });

      const packageData = await this.prisma.package.findFirst({
        where: {
          itemId: args.id,
          propertyId: prop.id,
        },
      });

      const transformedPackage = {
        ...packageData,
        activeAt: packageData.activeAt
          ? Number(packageData.activeAt)
          : Number(0),
        expiredAt: packageData.expiredAt
          ? Number(packageData.expiredAt)
          : Number(0),
        contractEndAt: packageData.contractEndAt
          ? Number(packageData.contractEndAt)
          : Number(0),
        trialEndAt: packageData.trialEndAt
          ? Number(packageData.trialEndAt)
          : Number(0),
      };

      return transformedPackage;
    } catch (e) {
      console.error(e);
      throw new InternalServerErrorException(e);
    }
  }
}
