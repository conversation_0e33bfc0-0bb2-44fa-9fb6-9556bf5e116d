import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { PackageController } from './package.controller';
import { PackageQueryHandlers } from './queries/handlers';
import { PackageRpcController } from './package.rpc.controller';
import { PackageCommandHandlers } from './commands';
import { IntegrationModule } from '../integration/integration.module';
import { MembershipClientModule } from '../membership-client/membership-client.module';
import { PackageSchedulerService } from './services/package.scheduler.service';

@Module({
  imports: [CqrsModule, IntegrationModule, MembershipClientModule],
  controllers: [PackageController, PackageRpcController],
  providers: [
    ...PackageQueryHandlers,
    ...PackageCommandHandlers,
    // PackageSchedulerService,
  ],
})
export class PackageModule {}
