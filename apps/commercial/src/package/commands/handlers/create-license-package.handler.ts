import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { IntegrationService } from 'apps/commercial/src/integration/integration.service';
import { CreateLicensePackageCommand } from '../impl';
import { LicenseService } from 'apps/commercial/src/membership-client/license.service';
import moment from 'moment';

@CommandHandler(CreateLicensePackageCommand)
export class CreateLicensePackageHandler
  implements ICommandHandler<CreateLicensePackageCommand>
{
  constructor(
    private prisma: PrismaService,
    private integrationService: IntegrationService,
    private licenseService: LicenseService,
  ) {}

  async execute(command: CreateLicensePackageCommand) {
    const { account, args } = command;

    try {
      const dateNow = moment();
      const currentYear = dateNow.year();
      let endOfYear = moment.utc({
        year: currentYear,
        month: 11,
        day: 31,
        hour: 23,
        minute: 59,
        second: 0,
      });
      if (dateNow.isAfter(endOfYear)) {
        endOfYear = endOfYear.add(1, 'year');
      }
      const expiredDateStr = endOfYear.valueOf().toString();

      const propertyType = await this.prisma.propertyType.findFirst({
        where: {
          id: account.property.categoryCode,
        },
      });

      const postal = await this.prisma.postal.findFirst({
        where: {
          id: account.property.postalId,
        },
      });

      for (const i in args.orderDetail) {
        if (args.orderDetail[i].itemType === 'LICENSE') {
          switch (account.property.licenseType) {
            case 'DLM':
              await this.prisma.$transaction(async (tr) => {
                await tr.package.create({
                  data: {
                    name: 'DLM',
                    isActive: false,
                    propertyId: account.property.id,
                    duration: 'yearly',
                    status: 'inActive',
                    // activeAt: null,
                    // expiredAt: null,
                    // contractEndAt: null,
                    itemType: 'LICENSE',
                    itemId: args.orderDetail[i].itemId,
                    orderId: args.id,
                  },
                });
              });

              break;
            case 'TEMPORARY':
              await this.prisma.$transaction(async (tr) => {
                await tr.package.create({
                  data: {
                    name: 'TEMPORARY',
                    isActive: false,
                    propertyId: account.property.id,
                    duration: 'yearly',
                    status: 'inActive',
                    itemType: 'LICENSE',
                    // activeAt: null,
                    // expiredAt: null,
                    // contractEndAt: null,
                    orderId: args.id,
                  },
                });
              });
              break;
            case 'CONVENTIONAL':
              await this.prisma.$transaction(async (tr) => {
                await tr.package.create({
                  data: {
                    name: 'CONVENTIONAL',
                    isActive: false,
                    propertyId: account.property.id,
                    duration: 'yearly',
                    status: 'inActive',
                    itemType: 'LICENSE',
                    // activeAt: null,
                    // expiredAt: null,
                    // contractEndAt: null,
                    orderId: args.id,
                  },
                });
              });
              break;
          }
        }
      }
      return;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }
}
