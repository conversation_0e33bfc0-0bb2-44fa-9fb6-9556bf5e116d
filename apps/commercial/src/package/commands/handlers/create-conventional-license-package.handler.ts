import { <PERSON><PERSON>andler, ICommandHandler } from '@nestjs/cqrs';
import { CreateConventionalLicensePackageCommand } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { IntegrationService } from '../../../integration/integration.service';
import { LicenseService } from '../../../membership-client/license.service';
import moment from 'moment';
import { lastValueFrom } from 'rxjs';

@CommandHandler(CreateConventionalLicensePackageCommand)
export class CreateConventionalLicensePackageHandler
  implements ICommandHandler<CreateConventionalLicensePackageCommand>
{
  constructor(
    private prisma: PrismaService,
    private integrationService: IntegrationService,
    private licenseService: LicenseService,
  ) {}

  async execute(command: CreateConventionalLicensePackageCommand) {
    const { args } = command;

    try {
      const dateNow = moment();
      const currentYear = dateNow.year();
      let endOfYear = moment.utc({
        year: currentYear,
        month: 11,
        day: 31,
        hour: 23,
        minute: 59,
        second: 0,
      });
      if (dateNow.isAfter(endOfYear)) {
        endOfYear = endOfYear.add(1, 'year');
      }
      const expiredDateStr = endOfYear.valueOf().toString();
      const property = await this.prisma.property.findFirst({
        where: {
          crmPropertyId: args.id,
        },
        include: {
          propertyType: true,
          postal: true,
        },
      });
      if (property) {
        const resp = await this.integrationService.DlpRenewLicense({
          licenseType: 'TEMPORARY',
          licenseKey: args.code,
          companyEmail: property.companyEmail,
          companyPhoneNumber: property.companyPhoneNumber,
          unit: Number(args.unit),
          requestId: args.requestId,
        });

        const pkg = await this.prisma.package.findFirst({
          where: {
            AND: [{ propertyId: args.id }, { itemType: 'LICENSE' }],
          },
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            features: true,
          },
        });

        await this.prisma.$transaction(async (tr) => {
          await tr.package.create({
            data: {
              name: 'Conventional License',
              isActive: true,
              propertyId: property.id,
              duration: 'yearly',
              status: 'active',
              activeAt: dateNow.valueOf(),
              expiredAt: resp?.expiredAt
                ? BigInt(Date.parse(resp?.expiredAt))
                : BigInt(expiredDateStr),
              contractEndAt: resp?.expiredAt
                ? BigInt(Date.parse(resp?.expiredAt))
                : BigInt(expiredDateStr),
              itemType: 'LICENSE',
              // orderId: args.id
            },
          });

          await tr.property.update({
            where: {
              id: property.id,
            },
            data: {
              licenseKey: resp.code,
              licenseType: resp.type,
            },
          });
        });

        await lastValueFrom(
          this.licenseService.setPerformingLicense(
            resp.id,
            resp?.code,
            resp?.type,
            resp?.requestId,
          ),
        );
      }
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }
}
