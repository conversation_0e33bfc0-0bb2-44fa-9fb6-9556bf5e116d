import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateExtendPackageCommand, UpdateNextPackageCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { features } from 'process';
import moment from 'moment';

@CommandHandler(UpdateExtendPackageCommand)
export class UpdateExtendPackageHandler
  implements ICommandHandler<UpdateExtendPackageCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: UpdateExtendPackageCommand) {
    const { account, args } = command;

    try {
      const dateNow = new Date();
      const now = moment();

      for (const i in args.orderDetail) {
        if (args.orderDetail[i].itemType === 'PLAN') {
          let expiredDateStr: string = '';

          const pkg = await this.prisma.package.findFirst({
            where: {
              AND: [
                {
                  property: {
                    crmPropertyId: account.property.id
                  }
                },
                { itemType: args.orderDetail[i].itemType },
                {
                  status: {
                    in: ['active', 'suspend']
                  }
                }
              ],
            },
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              features: true,
            },
          });

          switch (args.orderDetail[i].duration) {
            case 'monthly':
              const dateNow = moment(Number(pkg.expiredAt));
              let nextMonthSameDay = dateNow.clone().add(1, 'month');
              if (nextMonthSameDay.date() !== dateNow.date()) {
                nextMonthSameDay = nextMonthSameDay.subtract(nextMonthSameDay.date(), 'days');
              }
              expiredDateStr = nextMonthSameDay.valueOf().toString();

              break;
            case 'yearly':
              const nextYear = now.clone().add(1, 'year').set({
                hour: 16,
                minute: 59,
                second: 0,
                millisecond: 0,
              });

              expiredDateStr = nextYear.valueOf().toString();
              break;
            default:
              return;
          }

          const contractEndAt = new Date(
            dateNow.getFullYear() + 1,
            dateNow.getMonth(),
            dateNow.getDate(),
            16, 59, 0
          ).getTime().toString();
          
          if (pkg) {
            await this.prisma.$transaction( 
              async (tr) => {
                const newPkg = await tr.package.create({
                  data: {
                    isActive: true,
                    activeAt: now.valueOf(),
                    status: 'active',
                    expiredAt: BigInt(expiredDateStr),
                    duration: args.orderDetail[i].duration,
                    name: args.orderDetail[i].name,
                    propertyId: pkg.propertyId,
                    qty: Number(args.orderDetail[i].qty),
                    contractEndAt: BigInt(contractEndAt),
                    itemType: args.orderDetail[i].itemType,
                    itemId: args.orderDetail[i].itemId,
                    orderId: args.id
                  },
                });

                for (
                  let j = 0;
                  j <= args.orderDetail[i].orderPackageDetail.length - 1;
                  j++
                ) {
                  const newPkgFeature = await tr.packageFeature.create({
                    data: {
                      qty: Number(
                        args.orderDetail[i].orderPackageDetail[j].qty,
                      ),
                      packageId: newPkg.id,
                      featureId:
                        args.orderDetail[i].orderPackageDetail[j].featureId,
                      qouta: Number(
                        args.orderDetail[i].orderPackageDetail[j].qty,
                      ),
                    },
                  });
                  if (
                    args.orderDetail[i].orderPackageDetail[j].featureId ==
                      FEATURE.PlayerNode ||
                    args.orderDetail[i].orderPackageDetail[j].featureId ==
                      FEATURE.Playlist ||
                    args.orderDetail[i].orderPackageDetail[j].featureId ==
                      FEATURE.Schedule
                  ) {
                    const feature = pkg.features.find(
                      (ft) =>
                        ft.featureId ===
                        args.orderDetail[i].orderPackageDetail[j].featureId,
                    );
                    await tr.packageFeature.update({
                      where: {
                        id: newPkgFeature.id,
                      },
                      data: {
                        qty: feature.qty,
                        qouta: feature.qouta,
                      },
                    });
                  }
                  if (
                    args.orderDetail[i].orderPackageDetail[j].featureId ===
                      FEATURE.SongQuota ||
                    args.orderDetail[i].orderPackageDetail[j].featureId ===
                      FEATURE.Playlist ||
                    args.orderDetail[i].orderPackageDetail[j].featureId ===
                      FEATURE.Schedule
                  ) {
                    const activation = await tr.activation.findMany({
                      where: {
                        propertyId: pkg.propertyId,
                      },
                    });
                    for (const act of activation) {
                      await tr.zoneFeature.create({
                        data: {
                          activation: {
                            connect: {
                              id: act.id,
                            },
                          },
                          feature: {
                            connect: {
                              id: args.orderDetail[i].orderPackageDetail[j].featureId,
                            },
                          },
                          package: {
                            connect: {
                              id: newPkg.id
                            }
                          },
                          qouta:
                          Number(args.orderDetail[i].orderPackageDetail[j].qty) !== -1 ?
                            Number(args.orderDetail[i].orderPackageDetail[j].qty)/Number(args.orderDetail[i].qty) :
                            Number(args.orderDetail[i].orderPackageDetail[j].qty)
                        },
                      });
                    }
                  }
                }

                await tr.package.update({
                  where: {
                    id: pkg.id,
                  },
                  data: {
                    status: 'inActive',
                    isActive: false
                  },
                });

                await tr.property.update({
                  where: {
                    id: pkg.propertyId
                  },
                  data: {
                    status: 'active',
                    flag: 'PAID'
                  }
                });
              },
              {
                maxWait: 20000, // 20 seconds max wait to connect
                timeout: 50000, // 50 seconds timeout
                retry: 3,
              },
            );
          }
        }
      }

      return;
    } catch (err) {
      console.log(err);
    }
  }
}
