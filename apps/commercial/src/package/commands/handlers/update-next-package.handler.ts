import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateNextPackageCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import moment from 'moment';

@CommandHandler(UpdateNextPackageCommand)
export class UpdateNextPackageHandler
  implements ICommandHandler<UpdateNextPackageCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: UpdateNextPackageCommand) {
    const { account, args } = command;

    try {
      const now = moment();
      for (const i in args.orderDetail) {
        if (args.orderDetail[i].itemType === 'PLAN') {
          let expiredDateStr: string = '';

          const pkg = await this.prisma.package.findFirst({
            where: {
              AND: [
                {
                  property: {
                    crmPropertyId: account.property.id
                  }
                },
                { itemType: args.orderDetail[i].itemType },
                {
                  status: {
                    in: ['active', 'suspend']
                  }
                }
              ],
            },
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              features: true,
            },
          });

          switch (args.orderDetail[i].duration) {
            case 'monthly':
              const dateNow = moment(Number(pkg.expiredAt));
              let nextMonthSameDay = dateNow.clone().add(1, 'month');
              if (nextMonthSameDay.date() !== dateNow.date()) {
                nextMonthSameDay = nextMonthSameDay.subtract(nextMonthSameDay.date(), 'days');
              }
              expiredDateStr = nextMonthSameDay.valueOf().toString();

              break;
            case 'yearly':
              const nextYear = now.clone().add(1, 'year').set({
                hour: 16,
                minute: 59,
                second: 0,
                millisecond: 0,
              });

              expiredDateStr = nextYear.valueOf().toString();
              break;
            default:
              return;
          }

          if (pkg) {
            await this.prisma.$transaction(
              async (tr) => {
                await tr.package.update({
                  where: {
                    id: pkg.id
                  },
                  data: {
                    isActive: true,
                    activeAt: now.valueOf(),
                    status: 'active',
                    expiredAt: BigInt(expiredDateStr),
                    orderId: args.id
                  },
                });

                for (
                  let j = 0;
                  j <= args.orderDetail[i].orderPackageDetail.length - 1;
                  j++
                ) {
                  if (
                    args.orderDetail[i].orderPackageDetail[j].featureId ===
                      FEATURE.SongQuota
                  ) {
                    const activation = await tr.activation.findMany({
                      where: {
                        propertyId: pkg.propertyId,
                      },
                      include: {
                        zoneFeature: true
                      }
                    });
                    for (const act of activation) {
                      for (const zf of act.zoneFeature) {
                        await tr.zoneFeature.update({
                          where: {
                            id: zf.id
                          },
                          data: {
                            qouta: Number(args.orderDetail[i].orderPackageDetail[j].qty)
                          },
                        });
                      }
                    }
                  }
                }

                await tr.property.update({
                  where: {
                    id: pkg.propertyId
                  },
                  data: {
                    status: 'active',
                    flag: 'PAID'
                  }
                });
              },
              {
                maxWait: 10000, // 10 seconds max wait to connect
                timeout: 20000, // 20 seconds timeout
                retry: 3,
              },
            );
          }
        }
      }
      return;
    } catch (err) {
      console.log(err);
    }
  }
}
