import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { SetPackageLicenseTemporaryExpirationCommand } from '../impl';
import moment from 'moment';
import { RpcException } from '@nestjs/microservices';
import { Status } from '@grpc/grpc-js/build/src/constants';

@CommandHandler(SetPackageLicenseTemporaryExpirationCommand)
export class SetPackageLicenseTemporaryExpirationHandler
  implements ICommandHandler<SetPackageLicenseTemporaryExpirationCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: SetPackageLicenseTemporaryExpirationCommand) {
    const { args } = command;

    try {
      const dateNow = moment();
      const property = await this.prisma.property.findFirst({
        where: { crmPropertyId: args.propertyId },
      });

      if (!property) {
        throw new Error('Property not found');
      }

      if (['TEMPORARY', 'CONVENTIONAL'].includes(args.licenseType)) {
        return await this.handleTemporaryOrConventionalLicense(
          property.id,
          args,
          dateNow,
        );
      }

      if (args.licenseType === 'DLM') {
        return await this.handleDLMLicense(property.id, args, dateNow);
      }

      throw new Error('Invalid license type');
    } catch (err) {
      console.error(err.message);
      throw new RpcException({
        code: Status.INTERNAL,
        staus: 'internal server error',
      });
    }
  }

  private async handleTemporaryOrConventionalLicense(
    propertyId: string,
    args: any,
    dateNow: moment.Moment,
  ) {
    const pkgLicense = await this.prisma.package.findFirst({
      where: {
        AND: [{ propertyId }, { itemType: 'LICENSE' }],
      },
    });

    const packageData = {
      name: args.licenseType,
      isActive: true,
      status: 'active',
      expiredAt: Number(args.expiredAt) || null,
      contractEndAt: Number(args.expiredAt) || null,
      activeAt: dateNow.valueOf(),
    };

    if (!pkgLicense) {
      await this.prisma.package.create({
        data: {
          ...packageData,
          propertyId,
          duration: 'yearly',
          itemType: 'LICENSE',
          orderId: null,
        },
      });
    } else {
      await this.prisma.package.update({
        where: { id: pkgLicense.id },
        data: packageData,
      });
    }

    return new RpcException({
      code: Status.OK,
      message: 'success',
    });
  }

  private async handleDLMLicense(
    propertyId: string,
    args: any,
    dateNow: moment.Moment,
  ) {
    const pkg = await this.prisma.package.findFirst({
      where: {
        AND: [
          { propertyId },
          { isActive: false },
          { activeAt: null },
          { status: 'inActive' },
          { itemType: 'LICENSE' },
        ],
      },
      orderBy: { createdAt: 'desc' },
    });

    if (pkg) {
      await this.prisma.package.update({
        where: { id: pkg.id },
        data: {
          expiredAt: Number(args.expiredAt),
          contractEndAt: Number(args.expiredAt),
          activeAt: dateNow.valueOf(),
          status: 'active',
          isActive: true,
        },
      });

      return new RpcException({
        code: Status.OK,
        message: 'success',
      });
    }

    throw new Error('No inactive DLM package found');
  }
}
