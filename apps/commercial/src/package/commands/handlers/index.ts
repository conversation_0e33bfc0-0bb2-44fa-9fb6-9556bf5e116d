import { CreateLicense<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from './create-license-package.handler';
import { CreatePackageHandler } from './create-package.handler';
import { UpdateLicensePackageHandler } from './update-license-package.handler';
import { UpdateNextPackageHandler } from './update-next-package.handler';
import { DemoOrderHandler } from './create-demo.handler';
import { CreateTemporaryLicensePackageHandler } from './create-temporary-license-package.handler';
import { UpdateExtendPackageHandler } from './update-extend-package.handler';
import { CreateConventionalLicensePackageHandler } from './create-conventional-license-package.handler';
import { SetPackageLicenseTemporaryExpirationHandler } from './set-package-temporary-license-expiration.command';

export const PackageCommandHandlers = [
  CreatePackage<PERSON>and<PERSON>,
  CreateLicensePackageHandler,
  UpdateNextPackageHandler,
  UpdateExtendPackageH<PERSON><PERSON>,
  UpdateLicensePackage<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>reate<PERSON>emporaryLicensePackageH<PERSON><PERSON>,
  CreateConventionalLicensePackageHandler,
  SetPackageLicenseTemporaryExpirationHandler,
];
