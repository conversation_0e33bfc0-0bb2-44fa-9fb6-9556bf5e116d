import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { CreatePackageCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { randStr } from 'libs/utils/rand-str.util';
import { randomDeviceID } from 'libs/utils/rand-device-id.util';
import { randomNumber } from '@app/common';
import moment from 'moment';

@CommandHandler(CreatePackageCommand)
export class CreatePackageHandler
  implements ICommandHandler<CreatePackageCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: CreatePackageCommand) {
    const { account, args } = command;

    try {
      const dateNow = new Date();
      const now = moment();

      // TODO : optimize for better performance
      for (const i in args.orderDetail) {
        if (args.orderDetail[i].itemType === 'PLAN') {
          let expiredDateStr: string = '';
          let pkgId;

          switch (args.orderDetail[i].duration) {
            case 'monthly':
              {
                let nextMonthSameDay = now.clone().add(1, 'month');
                if (nextMonthSameDay.date() !== now.date()) {
                  nextMonthSameDay = nextMonthSameDay.endOf('month');
                }
                nextMonthSameDay = nextMonthSameDay.subtract(1, 'day');

                expiredDateStr = nextMonthSameDay.valueOf().toString();
              }
              break;

            case 'yearly':
              {
                const nextYearMinusOneDay = now
                  .clone()
                  .add(1, 'year')
                  .subtract(1, 'day')
                  .set({ hour: 16, minute: 59, second: 0, millisecond: 0 });

                expiredDateStr = nextYearMinusOneDay.valueOf().toString();
              }
              break;

            default:
              return;
          }

          const contractEndAt = new Date(
            dateNow.getFullYear() + 1,
            dateNow.getMonth(),
            dateNow.getDate() - 1,
            16,
            59,
            0,
          )
            .getTime()
            .toString();

          // FOR TESTING ONLY
          // const contractEndAt = (Date.now() + 15 * 60 * 1000).toString();
          const activationIds = await this.prisma.$transaction(
            async (tr) => {
              const activationIds = [];
              const pkg = await tr.package.create({
                data: {
                  name: args.orderDetail[i].name,
                  isActive: true,
                  propertyId: account.property.id,
                  activeAt: now.valueOf(),
                  qty: Number(args.orderDetail[i].qty),
                  status: 'active',
                  duration: args.orderDetail[i].duration,
                  expiredAt: BigInt(expiredDateStr),
                  contractEndAt: BigInt(contractEndAt),
                  itemType: args.orderDetail[i].itemType,
                  itemId: args.orderDetail[i].itemId,
                  orderId: args.id,
                  //expiredAt: Math.floor(new Date(expiredDateStr).getTime() / 1000),
                },
              });

              for (
                let j = 0;
                j <= args.orderDetail[i].orderPackageDetail.length - 1;
                j++
              ) {
                if (
                  args.orderDetail[i].orderPackageDetail[j].featureId ==
                  FEATURE.PlayerNode
                ) {
                  // Batch create activation
                  const qty = Number(
                    args.orderDetail[i].orderPackageDetail[j].qty,
                  );
                  const batchSize = 500; // boleh di adjust sesuai kebutuhan
                  const activationData = [];
                  const activationCodes = [];
                  for (let k = 0; k < qty; k++) {
                    const code = randStr(12);
                    activationData.push({
                      propertyId: account.property.id,
                      code,
                      licenseKey: `V${Number(randomNumber(10))}`,
                      qty: 0,
                    });
                    activationCodes.push(code);
                  }
                  // Insert in batches
                  for (let b = 0; b < activationData.length; b += batchSize) {
                    const batch = activationData.slice(b, b + batchSize);
                    await tr.activation.createMany({ data: batch });
                  }
                  // Fetch inserted activations to get their IDs
                  const acts = await tr.activation.findMany({
                    where: {
                      propertyId: account.property.id,
                      code: { in: activationCodes },
                    },
                    select: { id: true },
                  });
                  acts.forEach((a) => activationIds.push(a.id));

                  await tr.packageFeature.create({
                    data: {
                      packageId: pkg.id,
                      featureId:
                        args.orderDetail[i].orderPackageDetail[j].featureId,
                      qty: Number(
                        args.orderDetail[i].orderPackageDetail[j].qty,
                      ),
                      qouta: args.orderDetail[i].orderPackageDetail[j].qty - 1,
                    },
                  });

                  const activation = await tr.activation.findFirst({
                    where: {
                      propertyId: account.property.id,
                    },
                  });

                  if (account?.user) {
                    await tr.user.update({
                      where: {
                        id: account.user.id,
                      },
                      data: {
                        activation: {
                          connect: {
                            id: activation.id,
                          },
                        },
                      },
                    });

                    await tr.activation.update({
                      where: {
                        id: activation.id,
                      },
                      data: {
                        isUsed: true,
                      },
                    });
                    // TODO : enable this code when needed
                    // await tr.packageFeature.updateMany({
                    //   where: {
                    //     AND: [
                    //       {
                    //         package: {
                    //           id: pkg.id
                    //         },
                    //       },
                    //       {
                    //         feature: {
                    //           id: FEATURE.PlayerNode
                    //         }
                    //       },
                    //     ]
                    //   },
                    //   data: {
                    //     qouta: args.orderDetail[i].orderPackageDetail[j].qty-1
                    //   }
                    // })
                  }
                } else {
                  await tr.packageFeature.create({
                    data: {
                      packageId: pkg.id,
                      featureId:
                        args.orderDetail[i].orderPackageDetail[j].featureId,
                      qty: Number(
                        args.orderDetail[i].orderPackageDetail[j].qty /
                          Number(args.orderDetail[i].qty),
                      ),
                      qouta:
                        Number(
                          args.orderDetail[i].orderPackageDetail[j].qty,
                        ) !== -1
                          ? Number(
                              args.orderDetail[i].orderPackageDetail[j].qty,
                            ) / Number(args.orderDetail[i].qty)
                          : Number(
                              args.orderDetail[i].orderPackageDetail[j].qty,
                            ),
                    },
                  });
                }
              }
              pkgId = pkg.id;

              return activationIds;
            },
            {
              maxWait: 10000, // 10 seconds max wait to connect
              timeout: 20000, // 20 seconds timeout
              retry: 3,
            },
          );

          for (
            let m = 0;
            m <= args.orderDetail[i].orderPackageDetail.length - 1;
            m++
          ) {
            if (
              args.orderDetail[i].orderPackageDetail[m].featureId ===
                FEATURE.SongQuota ||
              args.orderDetail[i].orderPackageDetail[m].featureId ===
                FEATURE.Playlist ||
              args.orderDetail[i].orderPackageDetail[m].featureId ===
                FEATURE.Schedule
            ) {
              for (const act of activationIds) {
                await this.prisma.zoneFeature.create({
                  data: {
                    activation: {
                      connect: {
                        id: act,
                      },
                    },
                    feature: {
                      connect: {
                        id: args.orderDetail[i].orderPackageDetail[m].featureId,
                      },
                    },
                    package: {
                      connect: {
                        id: pkgId,
                      },
                    },
                    qouta:
                      Number(args.orderDetail[i].orderPackageDetail[m].qty) !==
                      -1
                        ? Number(
                            args.orderDetail[i].orderPackageDetail[m].qty,
                          ) / Number(args.orderDetail[i].qty)
                        : Number(args.orderDetail[i].orderPackageDetail[m].qty),
                  },
                });
              }
            }
          }
        }
      }
      return;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }
}
