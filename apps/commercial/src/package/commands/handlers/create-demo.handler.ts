import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { CreateDemoCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { randStr } from '../../../../../../libs/utils/rand-str.util';
import { randomNumber } from '@app/common';
import { randomDeviceID } from '../../../../../../libs/utils/rand-device-id.util';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(CreateDemoCommand)
export class DemoOrderHandler implements ICommandHandler<CreateDemoCommand> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateDemoCommand) {
    const { account, args, endDate } = command;
    try {
      const dateNow = new Date();
      const now = Date.now();
      for (const i in args.details) {
        if (args.details[i].itemType === 'PLAN') {
          let expiredDateStr: string = '';
          let device;
          let pkgId;

          switch (args.details[i].duration) {
            case 'monthly':
              const firstDayNextMonth = new Date(
                dateNow.getFullYear(),
                dateNow.getMonth() + 1,
                1,
                0,
                0,
                0,
              );
              expiredDateStr = firstDayNextMonth.getTime().toString();
              break;
            case 'yearly':
              const nextYear = dateNow.getFullYear() + 1;
              const januaryFirstNextYear = new Date(nextYear, 0, 1, 0, 0, 0);
              expiredDateStr = januaryFirstNextYear.getTime().toString();
              break;
            default:
              return;
          }

          const dateObject = new Date(endDate); // Create a Date object
          const timestampInMilliseconds = dateObject.getTime();

          // FOR TESTING ONLY
          // const contractEndAt = (Date.now() + 15 * 60 * 1000).toString();
          const activationIds = await this.prisma.$transaction(
            async (tr) => {
              const activationIds = [];
              const pkg = await tr.package.create({
                data: {
                  name: args.details[i].name,
                  isActive: true,
                  propertyId: account.property.id,
                  activeAt: now,
                  // qty: args.orderDetail[i].qty,
                  status: 'active',
                  duration: args.details[i].duration,
                  expiredAt: timestampInMilliseconds,
                  contractEndAt: timestampInMilliseconds,
                  trialEndAt: timestampInMilliseconds,
                  itemType: args.details[i].itemType,
                  itemId: args.details[i].itemId,
                  isTrial: true,
                  orderId: args.id,
                  //expiredAt: Math.floor(new Date(expiredDateStr).getTime() / 1000),
                },
              });

              for (
                let j = 0;
                j <= args.details[i].orderPackageDetail.length - 1;
                j++
              ) {
                if (
                  args.details[i].orderPackageDetail[j].featureId ==
                  FEATURE.PlayerNode
                ) {
                  // TODO : make prisma create many not using loop
                  for (
                    let k = 0;
                    k <= Number(args.details[i].orderPackageDetail[j].qty) - 1;
                    k++
                  ) {
                    const act = await tr.activation.create({
                      data: {
                        propertyId: account.property.id,
                        code: randStr(12),
                        licenseKey: `V${Number(randomNumber(10))}`, // random 11 digit
                        qty: 0,
                      },
                    });
                    activationIds.push(act.id);
                  }
                  await tr.packageFeature.create({
                    data: {
                      packageId: pkg.id,
                      featureId:
                        args.details[i].orderPackageDetail[j].featureId,
                      qty: Number(args.details[i].orderPackageDetail[j].qty),
                      qouta: args.details[i].orderPackageDetail[j].qty - 1,
                    },
                  });

                  const activation = await tr.activation.findFirst({
                    where: {
                      propertyId: account.property.id,
                    },
                  });

                  if (account?.user) {
                    await tr.user.update({
                      where: {
                        id: account.user.id,
                      },
                      data: {
                        activation: {
                          connect: {
                            id: activation.id,
                          },
                        },
                      },
                    });

                    await tr.activation.update({
                      where: {
                        id: activation.id,
                      },
                      data: {
                        isUsed: true,
                      },
                    });
                    // TODO : enable this code when needed
                    // await tr.packageFeature.updateMany({
                    //   where: {
                    //     AND: [
                    //       {
                    //         package: {
                    //           id: pkg.id
                    //         },
                    //       },
                    //       {
                    //         feature: {
                    //           id: FEATURE.PlayerNode
                    //         }
                    //       },
                    //     ]
                    //   },
                    //   data: {
                    //     qouta: args.orderDetail[i].orderPackageDetail[j].qty-1
                    //   }
                    // })
                  }
                } else {
                  await tr.packageFeature.create({
                    data: {
                      packageId: pkg.id,
                      featureId:
                        args.details[i].orderPackageDetail[j].featureId,
                        qty: Number(
                          args.details[i].orderPackageDetail[j].qty / Number(args.details[i].qty)
                        ),
                        qouta: Number(args.details[i].orderPackageDetail[j].qty) !==
                        -1
                          ? Number(
                              args.details[i].orderPackageDetail[j].qty,
                            ) / Number(args.details[i].qty)
                          : Number(args.details[i].orderPackageDetail[j].qty),
                    },
                  });
                }
              }
              pkgId = pkg.id;

              return activationIds;
            },
            {
              maxWait: 10000, // 10 seconds max wait to connect
              timeout: 20000, // 20 seconds timeout
              retry: 3,
            },
          );

          for (
            let m = 0;
            m <= args.details[i].orderPackageDetail.length - 1;
            m++
          ) {
            if (
              args.details[i].orderPackageDetail[m].featureId ===
                FEATURE.SongQuota ||
              args.details[i].orderPackageDetail[m].featureId ===
                FEATURE.Playlist ||
              args.details[i].orderPackageDetail[m].featureId ===
                FEATURE.Schedule
            ) {
              for (const act of activationIds) {
                await this.prisma.zoneFeature.create({
                  data: {
                    activation: {
                      connect: {
                        id: act,
                      },
                    },
                    feature: {
                      connect: {
                        id: args.details[i].orderPackageDetail[m].featureId,
                      },
                    },
                    package: {
                      connect: {
                        id: pkgId,
                      },
                    },
                    qouta:
                      Number(args.details[i].orderPackageDetail[m].qty) !== -1
                        ? Number(args.details[i].orderPackageDetail[m].qty) /
                          Number(args.details[i].qty)
                        : Number(args.details[i].orderPackageDetail[m].qty),
                  },
                });
              }
            }
          }

          if (account?.user) {
            if (activationIds.length > 0) {
              for (
                let j = 0;
                j <= args.details[i].orderPackageDetail.length - 1;
                j++
              ) {
                if (
                  args.details[i].orderPackageDetail[j].featureId ==
                  FEATURE.SongQuota
                ) {
                  await this.prisma.activation.updateMany({
                    where: {
                      propertyId: account.property.id,
                    },
                    data: {
                      qty:
                        args.details[i].orderPackageDetail[j].qty === -1
                          ? Number(args.details[i].orderPackageDetail[j].qty)
                          : Number(args.details[i].orderPackageDetail[j].qty) /
                            Number(args.details[i].qty),
                    },
                  });
                }
              }
            }
          }
        }
      }
      return;
    } catch (err) {
      console.log(err);
      throw new err();
    }
  }
}
