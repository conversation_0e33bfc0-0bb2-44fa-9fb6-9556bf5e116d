import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { IntegrationService } from 'apps/commercial/src/integration/integration.service';
import { UpdateLicensePackageCommand } from '../impl';
import { LicenseService } from 'apps/commercial/src/membership-client/license.service';
import moment from 'moment';

@CommandHandler(UpdateLicensePackageCommand)
export class UpdateLicensePackageHandler
  implements ICommandHandler<UpdateLicensePackageCommand>
{
  constructor(
    private prisma: PrismaService,
    private integrationService: IntegrationService,
    private licenseService: LicenseService,
  ) {}

  async execute(command: UpdateLicensePackageCommand) {
    const { account, args } = command;

    try {
      const dateNow = moment();
      const currentYear = dateNow.year();
      let endOfYear = moment.utc({
        year: currentYear,
        month: 11,
        day: 31,
        hour: 23,
        minute: 59,
        second: 0,
      });
      if (dateNow.isAfter(endOfYear)) {
        endOfYear = endOfYear.add(1, 'year');
      }
      const expiredDateStr = endOfYear.valueOf().toString();

      for (const i in args.orderDetail) {
        if (args.orderDetail[i].itemType === 'LICENSE') {
          const property = await this.prisma.property.findFirst({
            where: {
              crmPropertyId: account.property.id,
            },
          });

          const pkg = await this.prisma.package.findFirst({
            where: {
              AND: [{ propertyId: property.id }, { itemType: 'LICENSE' }],
            },
            orderBy: {
              createdAt: 'desc',
            },
            include: {
              features: true,
            },
          });

          if (pkg) {
            const resp = await this.integrationService.DlpRenewLicense({
              licenseType: 'DLM',
              licenseKey: property.licenseKey,
              companyEmail: property.companyEmail,
              companyPhoneNumber: property.companyPhoneNumber,
              unit: account.property.unit,
            });

            await this.prisma.$transaction(async (tr) => {
              await tr.package.create({
                data: {
                  name: args.orderDetail[i].name,
                  isActive: true,
                  propertyId: property.id,
                  duration: args.orderDetail[i].duration,
                  status: 'active',
                  activeAt: dateNow.valueOf(),
                  expiredAt: resp?.license.expiredAt
                    ? BigInt(Date.parse(resp?.license.expiredAt))
                    : BigInt(expiredDateStr),
                  contractEndAt: resp?.license.expiredAt
                    ? BigInt(Date.parse(resp?.license.expiredAt))
                    : BigInt(expiredDateStr),
                  itemType: args.orderDetail[i].itemType,
                  itemId: args.orderDetail[i].itemId,
                  orderId: args.id,
                },
              });

              await tr.property.update({
                where: {
                  id: property.id,
                },
                data: {
                  licenseKey: resp.code,
                  licenseType: resp.type,
                },
              });

              await tr.package.update({
                where: {
                  id: pkg.id,
                },
                data: {
                  status: 'inActive',
                  isActive: false,
                },
              });
            });

            await this.licenseService
              .setPerformingLicense(
                account.property.crmPropertyId,
                resp?.code,
                resp?.type,
                resp?.requestId,
              )
              .toPromise();
          }
        }

        const property = await this.prisma.property.findFirst({
          where: {
            crmPropertyId: account.property.id,
          },
          include: {
            propertyType: true,
            postal: true,
          },
        });

        if (property) {
          if (!property.licenseType) {
            const resp = await this.integrationService.DlpRenewLicense({
              licenseType: 'CONVENTIONAL',
              licenseKey: account.property.licenseKey,
              companyEmail: property.companyEmail,
              companyPhoneNumber: property.companyPhoneNumber,
              unit: account.property.unit,
            });

            await this.prisma.$transaction(async (tr) => {
              await tr.package.create({
                data: {
                  name: 'Conventional License',
                  isActive: true,
                  propertyId: property.id,
                  duration: 'yearly',
                  status: 'active',
                  activeAt: dateNow.valueOf(),
                  expiredAt: resp?.expiredAt
                    ? BigInt(Date.parse(resp?.expiredAt))
                    : BigInt(expiredDateStr),
                  contractEndAt: resp?.expiredAt
                    ? BigInt(Date.parse(resp?.expiredAt))
                    : BigInt(expiredDateStr),
                  itemType: 'LICENSE',
                  orderId: args.id,
                },
              });

              await tr.property.update({
                where: {
                  id: property.id,
                },
                data: {
                  licenseKey: resp.code,
                  licenseType: resp.type,
                },
              });
            });

            await this.licenseService
              .setPerformingLicense(
                account.property.crmPropertyId,
                resp?.code,
                resp?.type,
                resp?.requestId,
              )
              .toPromise();
          }
        }
      }
      return;
    } catch (err) {
      console.log(err);
    }
  }
}
