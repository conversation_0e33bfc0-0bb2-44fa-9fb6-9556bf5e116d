import { Empty, Id, Query, Status } from '@app/proto-schema/index.common';
import {
  CreateTemporaryLicenseRequest,
  GetTotalActivePackageRequest,
  GetTotalActivePackageResponse,
  ListExpiredPackageResponse,
  ListPackageResponse,
  Package,
  PACKAGE_SERVICE_NAME,
  PackageServiceController,
  RequestOnePackage,
  SetPackageLicenseTemporaryExpirationRequest,
} from '@app/proto-schema/index.internal';
import { Metadata, status } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import {
  GetListExpiredPackageQuery,
  GetListPackageQuery,
  GetOnePackageQuery,
  GetPackageQuery,
  GetTotalActivePackageQuery,
} from './queries';
import {
  CreateConventionalLicensePackageCommand,
  CreateTemporaryLicensePackageCommand,
  SetPackageLicenseTemporaryExpirationCommand,
} from './commands';

@Controller()
export class PackageRpcController implements PackageServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'setPackageLicenseTemporaryExpiration')
  setPackageLicenseTemporaryExpiration(request: SetPackageLicenseTemporaryExpirationRequest, metadata: Metadata, ...rest: any): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new SetPackageLicenseTemporaryExpirationCommand(request),
    );
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'createTemporaryLicensePackage')
  createTemporaryLicensePackage(
    request: CreateTemporaryLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new CreateTemporaryLicensePackageCommand(request),
    );
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'createConventionalLicensePackage')
  createConventionalLicensePackage(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(
      new CreateConventionalLicensePackageCommand(request),
    );
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'detailPackage')
  detailPackage(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<Package> | Observable<Package> | Package {
    const { propertyId, id } = JSON.parse(request.query);
    return this.queryBus.execute(new GetPackageQuery(propertyId, id, 'grpc'));
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'listPackage')
  async listPackage(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ): Promise<ListPackageResponse> {
    try {
      const query = request.query ? JSON.parse(request.query) : {};
      const { propertyId } = query;

      if (!propertyId) {
        return {
          status: {
            code: status.INVALID_ARGUMENT,
            message: 'Property ID is required',
          },
          data: [],
        };
      }

      const resp = await this.queryBus.execute(
        new GetListPackageQuery(
          {
            limit: query?.limit,
            page: query?.page,
            search: query?.search,
            sort: query?.sort,
            sortType: query?.sortType,
          },
          propertyId,
        ),
      );

      return {
        status: {
          code: status.OK,
          message: 'success',
        },
        data: resp,
      };
    } catch (error) {
      return {
        status: {
          code: status.INTERNAL,
          message: error.message || 'Internal server error',
        },
        data: [],
      };
    }
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'getTotalActivePackage')
  getTotalActivePackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetTotalActivePackageResponse>
    | Observable<GetTotalActivePackageResponse>
    | GetTotalActivePackageResponse {
    return this.queryBus.execute(new GetTotalActivePackageQuery(request.Ids));
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'listExpiredPackage')
  listExpiredPackage(
    request: GetTotalActivePackageRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListExpiredPackageResponse>
    | Observable<ListExpiredPackageResponse>
    | ListExpiredPackageResponse {
    return this.queryBus.execute(new GetListExpiredPackageQuery(request.Ids));
  }

  @GrpcMethod(PACKAGE_SERVICE_NAME, 'getOnePackage')
  getOnePackage(
    request: RequestOnePackage,
    metadata: Metadata,
    ...rest
  ): Promise<Package> | Observable<Package> | Package {
    return this.queryBus.execute(new GetOnePackageQuery(request));
  }
}
