import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { FilterPackagesDto } from './dto/filter-packages.dto';
import {
  GetPackageFeaturesQuery,
  GetPackageQuery,
  GetPackagesQuery,
} from './queries/impl';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('package')
@ApiTags('Package')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class PackageController {
  constructor(private queryBus: QueryBus) {}

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterPackagesDto) {
    return this.queryBus.execute(new GetPackagesQuery(user.propertyId, filter));
  }

  @Get(':id')
  findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(
      new GetPackageQuery(user.propertyId, id, 'rest'),
    );
  }

  @Get(':id/features')
  findAllFeature(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(
      new GetPackageFeaturesQuery(user.propertyId, id),
    );
  }
}
