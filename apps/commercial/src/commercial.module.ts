import { MailerModule } from '@nestjs-modules/mailer';
import { HandlebarsAdapter } from '@nestjs-modules/mailer/dist/adapters/handlebars.adapter';
import { BullModule } from '@nestjs/bull';
import {
  ForbiddenException,
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { join } from 'path';
import { AuthModule } from './auth/auth.module';
import { AvatarModule } from './avatar/avatar.module';
import { ConfigurationModule } from './configuration/configuration.module';
import { CountModule } from './count/count.module';
import { DeviceModule } from './device/device.module';
import { EventsModule } from './events/events.module';
import { FeatureModule } from './feature/feature.module';
import { HistoryModule } from './history/history.module';
import { IsellerModule } from './iseller/iseller.module';
import { LicenseModule } from './license/license.module';
import { MembershipModule } from './membership/membership.module';
import { OrderPaymentModule } from './order-payment/order-payment.module';
import { PlanModule } from './plan/plan.module';
import { PlaylistTrackModule } from './playlist-track/playlist-track.module';
import { PlaylistModule } from './playlist/playlist.module';
import { PostalModule } from './postal/postal.module';
import { PropertyTypeModule } from './property-type/property-type.module';
import { PropertyModule } from './property/property.module';
import { RemoteModule } from './remote/remote.module';
import { ScheduleReminderModule } from './schedule-reminder/schedule-reminder.module';
import { SchedulesModule } from './schedules/schedules.module';
import { SpotifyModule } from './spotify/spotify.module';
import { SubscriptionsModule } from './subscriptions/subscriptions.module';
import { TrackModule } from './track/track.module';
import { UserModule } from './user/user.module';
import { ValidatorModule } from './validator/validator.module';
import { WebhookModule } from './webhook/webhook.module';
import { SongLikeModule } from './song-like/song-like.module';
import { SummaryModule } from './summary/summary.module';
import { MapperModule } from './mapper/mapper.module';
import { AddOnModule } from './add-on/add-on.module';
import { SongModule } from './song/song.module';
import { GmiModule } from './gmi/gmi.module';
import { QueueModule } from './queue/queue.module';
import { ActivationCodeModule } from './activation-code/activation-code.module';
import { NotificationModule } from './notification/notification.module';
import { PackageModule } from './package/package.module';
import { LimiterModule } from './limiter/limiter.module';
import { CurrentModule } from './current/current.module';
import { ListModule } from './list/list.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { RedisModule, RedisModuleOptions } from '@nestjs-modules/ioredis';
import { IntegrationModule } from './integration/integration.module';
import { RepositoryModule } from './repository/repository.module';
import { SessionModule } from './session/session.module';
import { PlayerModule } from './player/player.module';
import { PrismaModule } from '../../../libs/prisma';
import { MembershipClientModule } from './membership-client/membership-client.module';
import { VoucherModule } from './voucher/voucher.module';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { SearchModule } from './search/search.module';
import { HealthModule } from './health/health.module';
import { BannerModule } from './banner/banner.module';
import { ReferralModule } from './referral/referral.module';
import { JobPositionModule } from './job-position/job-position.module';
import { PartnershipModule } from './partnership/partnership.module';
import { DemoCodeModule } from './demo-code/demo-code.module';
import { AppsModule } from './app/app.module';
import { ZoneTypeModule } from 'apps/admin/src/zone-type/zone-type.module';
import { JinggleModule } from './jinggle/jinggle.module';
import { MonitorModule } from './monitor/monitor.module';
import { TokenModule } from './token/token.module';
import { CsrfModule } from './csrf/csrf.module';
import { ForbiddenExceptionFilter } from '@app/common/middlewares';
import { CsrfMiddleware } from '@app/common/middlewares/csrf.middleware';
import { MaintenanceMiddleware } from '@app/common/middlewares/maintenance.middleware';
import { HttpModule } from '@nestjs/axios';
import { ProductModule } from './product/product.module';
import { ProductVariantModule } from './product-variant/variant.module';

@Module({
  imports: [
    HttpModule,
    MailerModule.forRootAsync({
      useFactory: async (config: ConfigService) => ({
        transport: {
          host: config.get<string>('MAIL_HOST'),
          port: 465,
          secure: true,
          auth: {
            user: config.get<string>('MAIL_USER'),
            pass: config.get<string>('MAIL_PASSWORD'),
          },
        },
        defaults: {
          from: `"No Reply" <${config.get<string>('MAIL_FROM')}>`,
        },
        template: {
          dir: join(__dirname, './mail/templates'),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
      inject: [ConfigService],
    }),
    // ThrottlerModule.forRoot([
    //   {
    //     ttl: 60000,
    //     limit: 25,
    //   },
    // ]),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        () => ({
          MEMBERSHIP_GRPC_URL:
            process.env.MEMBERSHIP_GRPC_URL || 'localhost:7000',
        }),
      ],
    }),
    PrismaModule.forRoot({
      isGlobal: true,
      prismaServiceOptions: {
        prismaOptions: {
          log: [
            {
              emit: 'stdout',
              level: 'error',
            },
            {
              emit: 'stdout',
              level: 'warn',
            },
          ],
        },
      },
    }),
    BullModule.forRootAsync({
      useFactory: async (config: ConfigService) => ({
        redis: `${config.get<string>('REDIS')}`,
      }),
      inject: [ConfigService],
    }),
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (
        config: ConfigService,
      ): Promise<RedisModuleOptions> => ({
        type: 'single',
        url: config.get<string>('REDIS'),
      }),
      inject: [ConfigService],
    }),
    ScheduleModule.forRoot(),
    UserModule,
    AuthModule,
    PlanModule,
    ProductModule,
    ProductVariantModule,
    DeviceModule,
    SchedulesModule,
    // CountModule,
    PlaylistModule,
    SubscriptionsModule, //nyalain
    ConfigurationModule,
    MembershipModule,
    PropertyModule,
    PropertyTypeModule,
    PostalModule, //nyalain
    FeatureModule, //nyalain
    ValidatorModule,
    SpotifyModule,
    HistoryModule,
    PartnershipModule,
    OrderPaymentModule,
    TrackModule, //nyalain
    LicenseModule,
    PlaylistTrackModule,
    AvatarModule,
    IsellerModule,
    WebhookModule,
    EventsModule,
    RemoteModule,
    ScheduleReminderModule,
    SongLikeModule,
    PlayerModule,
    QueueModule,
    SummaryModule,
    // MapperModule,
    AddOnModule,
    GmiModule,
    SongModule,
    ActivationCodeModule,
    LimiterModule,
    NotificationModule,
    PackageModule,
    CurrentModule,
    ListModule,
    IntegrationModule,
    // RepositoryModule,
    // SessionModule,
    // RunningTextModule,
    MembershipClientModule,
    VoucherModule,
    ReferralModule,
    DemoCodeModule,
    SearchModule,
    HealthModule,
    BannerModule,
    JobPositionModule, //nyalain
    AppsModule,
    ZoneTypeModule,
    JinggleModule,
    MonitorModule,
    TokenModule,
    CsrfModule,
  ],
})
// export class CommercialModule { }
export class CommercialModule implements NestModule {
  constructor(private readonly configService: ConfigService) {}

  configure(consumer: MiddlewareConsumer) {
    const serviceId = this.configService.get<string>('COMMERCIAL_API_KEY');
    const isLocal = this.configService.get<string>('NODE_ENV') === 'local';
    MaintenanceMiddleware.serviceId = serviceId;

    consumer
      .apply(MaintenanceMiddleware)
      .exclude(
        { path: 'v1/app/maintenance', method: RequestMethod.ALL },
        { path: 'v1/app/hook-maintenance', method: RequestMethod.ALL },
        { path: 'v1/webhook', method: RequestMethod.ALL },
      )
      .forRoutes('*');

    if (!isLocal) {
      consumer
        .apply(CsrfMiddleware)
        .exclude(
          { path: 'v1/app/maintenance', method: RequestMethod.ALL },
          { path: 'v1/app/hook-maintenance', method: RequestMethod.ALL },
          { path: 'v1/webhook', method: RequestMethod.ALL },
        )
        .forRoutes('*');
    }
  }
}
