import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetRepositoryQuery } from '../impl';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetRepositoryQuery)
export class GetRepositoryHandler implements IQueryHandler<GetRepositoryQuery> {
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
  ) {}

  async execute(query: GetRepositoryQuery) {
    const { id } = query;
    const item = await this.prisma.repository.findFirst({
      where: {
        id
      }
    });

    if (!item) {
      throw new NotFoundException()
    }

    return {
      id: item.id,
      name: item.name,
      versionName: item.versionName,
      packageName: item.packageName,
      description: item.description,
      size: item.size,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      url: item
      ? `https://${this.config.get<string>('MINIO_ENDPOINT')}/${item.path}/${item.fileName}`
      : null,
    };
  }
}
