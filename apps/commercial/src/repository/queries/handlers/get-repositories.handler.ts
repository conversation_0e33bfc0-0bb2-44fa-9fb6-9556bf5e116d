import { Pagination } from '@app/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma'; 
import { GetRepositoriesQuery } from '../impl';

@QueryHandler(GetRepositoriesQuery)
export class GetRepositoriesHandler implements IQueryHandler<GetRepositoriesQuery> {
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
  ) {}

  async execute(query: GetRepositoriesQuery) {
    const { args } = query;
    const search = args.search || '';
    const items = await Pagination<
      any,
      Prisma.RepositoryFindManyArgs
    >(
      this.prisma.repository,
      {
        orderBy: { createdAt: 'desc' },
        where: {
          OR: [
            {
              name: { contains: search, mode: 'insensitive' },
            },
            { fileName: { contains: search, mode: 'insensitive' } },
            { versionName: { contains: search, mode: 'insensitive' } },
            { packageName: { contains: search, mode: 'insensitive' } },
          ],
        },
      },
      { page: args.page, limit: args.limit },
    );

    const serializeData = items.data.map((item) => {
      return {
        id: item.id,
        name: item.name,
        versionName: item.versionName,
        packageName: item.packageName,
        description: item.description,
        size: item.size,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        url: item
        ? `https://${this.config.get<string>('MINIO_ENDPOINT')}/${item.path}/${item.fileName}`
        : null,
      };
    });
    return Object.assign(items, { data: serializeData });
  }
}
