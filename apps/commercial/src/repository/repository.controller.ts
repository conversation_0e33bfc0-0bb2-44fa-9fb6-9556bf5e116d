import {
  Controller,
  Get,
  Param,
  Query,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';
import { FilterRepositoriesDto } from './dto/filter-repositories.dto';
import { GetRepositoriesQuery, GetRepositoryQuery } from './queries';

@Controller({ version: '1', path: 'repository' })
@ApiTags('Repository')
export class RepositoryController {
  constructor(
    private readonly queryBus: QueryBus,
  ) {}
  @Get()
  findAll(@Query() filter: FilterRepositoriesDto) {
    return this.queryBus.execute(new GetRepositoriesQuery(filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetRepositoryQuery(id));
  }
}
