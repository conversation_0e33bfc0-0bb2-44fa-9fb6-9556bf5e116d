import { <PERSON>Publisher, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { CreateJingleEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { Subject } from 'rxjs';
import {
  CreateTrackRequest,
  VELODIVA_SERVICE_NAME,
  VelodivaServiceClient,
} from '@app/proto-schema/index.jinggle';
import { Metadata } from '@grpc/grpc-js';
import { Inject, OnModuleInit } from '@nestjs/common';
import { JINGGLE_PACKAGE } from '@app/common';
import { ClientGrpc, ClientProxy } from '@nestjs/microservices';
import { bufferToStreamChunks } from 'libs/utils/buffer-to-stream-chunks.utils';
import {
  NotificationTags,
  NotificationType,
} from 'apps/commercial/src/common/constans/notification';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';

@EventsHandler(CreateJingleEvent)
export class CreateJingleEventHandler
  implements IEventHandler<CreateJingleEvent>, OnModuleInit
{
  private velodivaClient: VelodivaServiceClient;

  constructor(
    private prisma: PrismaService,
    @Inject(JINGGLE_PACKAGE) private readonly velodivaClientService: ClientGrpc,
    @Inject('mobile') private client: ClientProxy,
    private playerGateway: PlayerGateway,
  ) {}

  async handle(event: CreateJingleEvent) {
    const { args } = event;
    try {
      const subject = new Subject<CreateTrackRequest>();
      const meta = new Metadata();
      const fileName = args.file.originalname;
      const uploadStream = this.velodivaClient.createTrack(
        subject.asObservable(),
        meta,
      );
      const chunkSize = 64 * 1024;

      const fileStream = bufferToStreamChunks(args.file.buffer, chunkSize);
      fileStream.on('data', (chunk: Buffer) => {
        subject.next({
          file: { chunk, fileName },
          data: {
            artist: args.artist,
            propertyId: args.propertyId,
            propertyName: args.properties[0].property.companyName,
            title: args.title,
            category: args.category,
            source: args.source,
            type: args.type,
            cid: args.properties[0].property.cid,
            originalName: args.originalname,
          },
        });
      });

      fileStream.on('end', () => subject.complete());

      fileStream.on('error', (err) => {
        console.error('Stream Error:', err);
        subject.error(err);
      });

      await new Promise<void>((resolve, reject) => {
        uploadStream.subscribe({
          next: () => {},
          complete: () => resolve(),
          error: (err) => {
            console.error('gRPC Upload Error:', err);
            this.playerGateway.server
              .to(args.activationId)
              .emit('jingle-create-status', {
                trackId: '-',
                title: args.title,
                status: 'Failed',
              });
            reject(err);
          },
        });
      });

      const adminData = await this.prisma.user.findFirst({
        where: {
          role: {
            name: 'super admin',
          },
        },
      });

      await this.prisma.notification.create({
        data: {
          toId: adminData.id,
          title: 'Jingle track added',
          message: 'There is a new jingle track that has been added',
          type: NotificationType.Jingle,
          tags: [NotificationTags.JingleAdded],
          isRead: false,
          fromId: args.userId,
        },
      });

      this.client.emit('jingle-admin', {
        type: NotificationTags.JingleAdded,
        userId: adminData.id,
      });
    } catch (error) {
      this.playerGateway.server
        .to(args.activationId)
        .emit('jingle-create-status', 'Failed');

      console.log('create Jingle Event', error);
    }
  }

  onModuleInit() {
    this.velodivaClient =
      this.velodivaClientService.getService<VelodivaServiceClient>(
        VELODIVA_SERVICE_NAME,
      );
  }
}
