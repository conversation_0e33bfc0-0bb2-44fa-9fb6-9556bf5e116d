import { CreateJinggleDevicesHandler } from './create-jinggle-devices.handler';
import { CreateJingglePlaylistHandler } from './create-jinggle-playlist.handler';
import { CreateJinggleScheduleHandler } from './create-jinggle-schedule.handler';
import { CreateJinggleHandler } from './create-jinggle.handler';
import { DeleteJinggleScheduleHandler } from './delete-jinggle-schedule.handler';
import { DeleteJinglePlaylistHandler } from './delete-jingle-playlist.handler';
import { DeleteJingleZoneHandler } from './delete-jingle-zone.handler';
import { DelteJingleHandler } from './delete-jingle.handler';
import { UpdateJinggleScheduleHandler } from './update-jinggle-schedule.handler';
import { UpdateJingleDeviceHandler } from './update-jingle-device.handler';
import { UpdateJinglePlaylistTrackSequenceHandler } from './update-jingle-playlist-track-sequence.handler';
import { UpdateJingglePlaylistHandler } from './update-jingle-playlist.handler';
import { UpdateJingleHandler } from './update-jingle.handler';

export const JinggleCommanHandlers = [
  CreateJinggleHandler,
  CreateJinggleDevicesHandler,
  CreateJingglePlaylistHandler,
  DeleteJinggleScheduleHandler,
  UpdateJinggleScheduleHandler,
  CreateJinggleScheduleHandler,
  DelteJingleHandler,
  DeleteJingleZoneHandler,
  UpdateJingleHandler,
  UpdateJingleDeviceHandler,
  DeleteJinglePlaylistHandler,
  UpdateJingglePlaylistHandler,
  UpdateJinglePlaylistTrackSequenceHandler,
];
