import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateJingleDeviceCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from 'libs/prisma';
import { HttpStatusCode } from 'axios';

@CommandHandler(UpdateJingleDeviceCommand)
export class UpdateJingleDeviceHandler
  implements ICommandHandler<UpdateJingleDeviceCommand>
{
  constructor(
    private jingleService: JinggleService,
    private prisma: PrismaService,
  ) {}

  async execute(command: UpdateJingleDeviceCommand): Promise<any> {
    const { id, request, user } = command;

    const zones = await this.prisma.activation.findMany({
      where: {
        id: { in: request.deviceIds },
      },
      include: {
        activationDevice: {
          take: 1,
          orderBy: { createdAt: 'desc' },
          include: {
            device: true,
          },
        },
      },
    });

    const zoneData = zones.map((zone) => ({
      deviceName: zone.activationDevice[0]?.device?.name,
      id: zone.id,
      name: zone.zone,
      serialNumber: zone.activationDevice[0]?.device?.serialNumber,
    }));

    await firstValueFrom(
      this.jingleService.updateJingleZone({
        jingleDefaultRequest: { id, propertyId: user.propertyId },
        data: {
          description: request.description,
          name: request.name,
          zoneData,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Update Jinggle Group Device Schedule',
      data: null,
    };
  }
}
