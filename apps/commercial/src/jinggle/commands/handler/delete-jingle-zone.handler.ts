import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatus } from '@nestjs/common';
import { DeleteJingleZoneCommand } from '../impl';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeleteJingleZoneCommand)
export class DeleteJingleZoneHandler
  implements ICommandHandler<DeleteJingleZoneCommand>
{
  constructor(
    private jingleService: JinggleService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DeleteJingleZoneCommand): Promise<any> {
    const { user, id } = command;

    await firstValueFrom(
      this.jingleService.deleteJingleZone({
        id: id,
        propertyId: user.propertyId,
      }),
    );

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.increaseFeatureQty({
      id: FEATURE.JingleGroupDevice,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Succesfully Detele Jingle Group Device',
      data: null,
    };
  }
}
