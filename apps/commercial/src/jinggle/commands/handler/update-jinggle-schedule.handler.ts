import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { UpdateJinggleScheduleCommand } from '../impl/update-jinggle-schedule.command';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { PrismaService } from 'libs/prisma';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@CommandHandler(UpdateJinggleScheduleCommand)
export class UpdateJinggleScheduleHandler
  implements ICommandHandler<UpdateJinggleScheduleCommand>
{
  constructor(
    private jingleService: JinggleService,
    private playerGateway: PlayerGateway,
    private prisma: PrismaService,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: UpdateJinggleScheduleCommand): Promise<any> {
    const { id, request, user } = command;
    const expiredAt = new Date(`${request.expiredDate}T23:59:59.999`).getTime();

    const data = await firstValueFrom(
      this.jingleService.updateJingleSchedule({
        jingleDefaultRequest: { id, propertyId: user.propertyId },
        data: {
          endTime: request.endTime,
          expiredAt,
          isCrossFade: request.isCrossFade,
          isLockMode: request.isLockMode,
          isPlaynow: request.isPlaynow,
          mode: request.mode,
          modeValue: request.modeValue,
          name: request.name,
          playlistIds: request.playlistIds,
          propertyId: user.propertyId,
          ratio: request.ratio,
          startTime: request.startTime,
          groupDeviceIds: request.groupDeviceIds,
        },
      }),
    );

    const uniqueZoneIds = [
      ...new Set(
        data.zoneData.flatMap((zoneGroup) =>
          zoneGroup.data.flatMap((zone) => zone.zoneId),
        ),
      ),
    ];

    const zoneData = await this.prisma.activation.findMany({
      where: { id: { in: uniqueZoneIds } },
    });

    const zoneGroupsWithTimes = data.zoneData.map((zoneGroup) => {
      const zoneTimeData = zoneGroup.data.map((zone) => {
        const zonedata = zoneData.find((z) => z.id === zone.zoneId);
        return {
          zoneId: zone.zoneId,
          timeZone: zonedata ? zonedata.timezone : 'Asia/Jakarta',
          isPlayed: zone.isPLayed,
        };
      });

      return {
        zoneGroupId: zoneGroup.zoneGroupId,
        zoneTimes: zoneTimeData,
      };
    });

    const requests = {
      scheduleId: data.scheduleId,
      zones: zoneGroupsWithTimes.flatMap((zoneGroup) => zoneGroup.zoneTimes),
    };

    await firstValueFrom(
      this.jingleService.updateJingleScheduleZoneTime(requests),
    );

    this.playerGateway.server.to(uniqueZoneIds).emit('queue:update', true);
    this.client.emit('queue-update-mob', {
      zoneIds: uniqueZoneIds,
      data: true,
    });
    this.client.emit('queue-update-atv', {
      zoneIds: uniqueZoneIds,
      data: true,
    });

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Update Jinggle Schedule',
      data: null,
    };
  }
}
