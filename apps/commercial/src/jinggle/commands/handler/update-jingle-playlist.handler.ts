import { Command<PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateJingglePlaylistCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@CommandHandler(UpdateJingglePlaylistCommand)
export class UpdateJingglePlaylistHandler
  implements ICommandHandler<UpdateJingglePlaylistCommand>
{
  constructor(private jingleService: JinggleService) {}

  async execute(command: UpdateJingglePlaylistCommand): Promise<any> {
    const { request, user, id } = command;

    await firstValueFrom(
      this.jingleService.updateJinglePlaylist({
        jingleDefaultRequest: { id, propertyId: user.propertyId },
        data: {
          description: request.description,
          name: request.name,
          ids: request.jinggleId,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Update Jingle Playlist',
      data: null,
    };
  }
}
