import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { DelteJingleCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatus } from '@nestjs/common';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DelteJingleCommand)
export class DelteJingleHandler implements ICommandHandler<DelteJingleCommand> {
  constructor(
    private jingleService: JinggleService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DelteJingleCommand): Promise<any> {
    const { user, id } = command;
    // console.log('EROORR ');

    const data = await firstValueFrom(
      this.jingleService.deleteJingle({ id: id, propertyId: user.propertyId }),
    );
    // console.log('KASD ASD ', data);

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.increaseFeatureQty({
      id: FEATURE.JingleLibrary,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Succesfully Detele Jingle',
      data: null,
    };
  }
}
