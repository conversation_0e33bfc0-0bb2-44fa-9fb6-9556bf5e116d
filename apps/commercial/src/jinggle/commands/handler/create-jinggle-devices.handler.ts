import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { CreateJinggleDevicesCommand } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { ForbiddenException } from '@nestjs/common';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(CreateJinggleDevicesCommand)
export class CreateJinggleDevicesHandler
  implements ICommandHandler<CreateJinggleDevicesCommand>
{
  constructor(
    private jingleService: JinggleService,
    private prisma: PrismaService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: CreateJinggleDevicesCommand): Promise<any> {
    const { user, request } = command;

    // ------ <PERSON>ECK FEATURE QUOTA ----------- //
    const jingleLibraryPackage = await this.prisma.packageFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.JingleGroupDevice,
          },
          {
            packageId: user.packageId,
          },
        ],
      },
    });

    if (!jingleLibraryPackage) {
      throw new ForbiddenException('Insufficient jingle group device quota');
    }
    if (jingleLibraryPackage.qouta < 1 && jingleLibraryPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient jingle group device quota');
    }
    // ------ END OF CHECK FEATURE QUOTA ----------- //

    const zones = await this.prisma.activation.findMany({
      where: {
        id: { in: request.deviceIds },
      },
      include: {
        activationDevice: {
          take: 1,
          orderBy: { createdAt: 'desc' },
          include: {
            device: true,
          },
        },
      },
    });

    const zoneData = zones.map((zone) => ({
      deviceName: zone.activationDevice[0]?.device?.name,
      id: zone.id,
      name: zone.zone,
      serialNumber: zone.activationDevice[0]?.device?.serialNumber,
    }));

    await firstValueFrom(
      this.jingleService.createJingleZone({
        description: request.description,
        zoneData,
        name: request.name,
        propertyId: user.propertyId,
      }),
    );

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.decreaseFeatureQty({
      id: FEATURE.JingleGroupDevice,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Create Jinggle Devices',
      data: null,
    };
  }
}
