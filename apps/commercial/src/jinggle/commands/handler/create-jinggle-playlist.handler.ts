import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { CreateJingglePlaylistCommand } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { lastValueFrom } from 'rxjs';
import { ForbiddenException } from '@nestjs/common';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { PrismaService } from 'libs/prisma';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(CreateJingglePlaylistCommand)
export class CreateJingglePlaylistHandler
  implements ICommandHandler<CreateJingglePlaylistCommand>
{
  constructor(
    private jinggleService: JinggleService,
    private prisma: PrismaService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: CreateJingglePlaylistCommand): Promise<any> {
    const { request, user } = command;

    // ------ <PERSON>ECK FEATURE QUOTA ----------- //
    const jingleLibraryPackage = await this.prisma.packageFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.JinglePlaylist,
          },
          {
            packageId: user.packageId,
          },
        ],
      },
    });

    if (!jingleLibraryPackage) {
      throw new ForbiddenException('Insufficient jingle playlist quota');
    }
    if (jingleLibraryPackage.qouta < 1 && jingleLibraryPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient jingle playlist quota');
    }
    // ------ END OF CHECK FEATURE QUOTA ----------- //

    await lastValueFrom(
      this.jinggleService.createJinglePlaylist({
        description: request.description,
        ids: request.jinggleId,
        name: request.name,
      }),
    );

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.decreaseFeatureQty({
      id: FEATURE.JinglePlaylist,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });
    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Create Jinggle Playlist',
      data: null,
    };
  }
}
