import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { DeleteJinglePlaylistCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatus } from '@nestjs/common';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeleteJinglePlaylistCommand)
export class DeleteJinglePlaylistHandler
  implements ICommandHandler<DeleteJinglePlaylistCommand>
{
  constructor(
    private jingleService: JinggleService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DeleteJinglePlaylistCommand): Promise<any> {
    const { user, id } = command;

    await firstValueFrom(
      this.jingleService.deleteJinglePlaylist({
        id: id,
        propertyId: user.propertyId,
      }),
    );

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.increaseFeatureQty({
      id: FEATURE.JinglePlaylist,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });

    return {
      statusCode: HttpStatus.OK,
      message: 'Succesfully Detele Jingle Playlist',
      data: null,
    };
  }
}
