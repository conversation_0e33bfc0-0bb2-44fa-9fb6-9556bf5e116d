import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateJinglePlaylistTrackSequenceCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { firstValueFrom } from 'rxjs';
import { JinggleService } from '../../jinggle.service';
import { HttpStatusCode } from 'axios';

@CommandHandler(UpdateJinglePlaylistTrackSequenceCommand)
export class UpdateJinglePlaylistTrackSequenceHandler
  implements ICommandHandler<UpdateJinglePlaylistTrackSequenceCommand>
{
  constructor(
    private prisma: PrismaService,
    private jingleService: JinggleService,
  ) {}

  async execute(
    command: UpdateJinglePlaylistTrackSequenceCommand,
  ): Promise<any> {
    const { id, trackId, user, request } = command;

    await firstValueFrom(
      this.jingleService.updateSequencePlaylistTrack({
        playlistId: id,
        propertyId: user.propertyId,
        sequence: request.sequence,
        trackId: trackId,
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Update Playlist Track Jingle Sequence',
      data: null,
    };
  }
}
