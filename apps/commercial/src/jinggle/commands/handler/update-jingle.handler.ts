import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { UpdateJingleCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatus } from '@nestjs/common';
import { HttpStatusCode } from 'axios';

@CommandHandler(UpdateJingleCommand)
export class UpdateJingleHandler
  implements ICommandHandler<UpdateJingleCommand>
{
  constructor(private jingleService: JinggleService) {}

  async execute(command: UpdateJingleCommand): Promise<any> {
    const { request, user, id } = command;

    await firstValueFrom(
      this.jingleService.updateJingle({
        jingleDefaultRequest: { id, propertyId: user.propertyId },
        userData: {
          artist: request.artist,
          title: request.title,
          category: request.category,
          // expiredAt: new Date(request.expiredAt).getTime(),
          source: request.source,
          type: request.type,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Update Jingle',
      data: null,
    };
  }
}
