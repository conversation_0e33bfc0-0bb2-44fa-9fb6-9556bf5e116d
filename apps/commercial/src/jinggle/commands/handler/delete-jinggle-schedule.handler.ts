import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { DeleteJinggleScheduleCommand } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { Inject } from '@nestjs/common';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeleteJinggleScheduleCommand)
export class DeleteJinggleScheduleHandler
  implements ICommandHandler<DeleteJinggleScheduleCommand>
{
  constructor(
    private jingleservice: JinggleService,
    private playerGateway: PlayerGateway,
    @Inject('mobile') private client: <PERSON>lient<PERSON>roxy,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DeleteJinggleScheduleCommand): Promise<any> {
    const { id, user } = command;
    const data = await firstValueFrom(
      this.jingleservice.deleteJingleSchedule({ id }),
    );

    this.playerGateway.server.to(data.zoneIds).emit('queue:update', true);
    this.client.emit('queue-update-mob', {
      zoneIds: data.zoneIds,
      data: true,
    });
    this.client.emit('queue-update-atv', {
      zoneIds: data.zoneIds,
      data: true,
    });

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.increaseFeatureQty({
      id: FEATURE.JingleSchedule,
      propertyId: user.propertyId,
      deviceId: user.deviceId
    });

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Delete Jinggle Schedule',
      data: null,
    };
  }
}
