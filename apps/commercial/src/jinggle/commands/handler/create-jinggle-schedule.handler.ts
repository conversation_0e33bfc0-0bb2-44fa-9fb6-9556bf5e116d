import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { CreateJinggleScheduleCommand } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';
import { PrismaService } from 'libs/prisma';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { ForbiddenException, Inject } from '@nestjs/common';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(CreateJinggleScheduleCommand)
export class CreateJinggleScheduleHandler
  implements ICommandHandler<CreateJinggleScheduleCommand>
{
  constructor(
    private jingleService: JinggleService,
    private prisma: PrismaService,
    private playerGateway: PlayerGateway,
    @Inject('mobile') private client: ClientProxy,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: CreateJinggleScheduleCommand): Promise<any> {
    const { user, request } = command;

    // ------ CHECK FEATURE QUOTA ----------- //
    const jingleLibraryPackage = await this.prisma.packageFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.JingleSchedule,
          },
          {
            packageId: user.packageId,
          },
        ],
      },
    });

    if (!jingleLibraryPackage) {
      throw new ForbiddenException('Insufficient jingle schedule quota');
    }
    if (jingleLibraryPackage.qouta < 1 && jingleLibraryPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient jingle schedule quota');
    }
    // ------ END OF CHECK FEATURE QUOTA ----------- //

    const userData = await this.prisma.user.findFirst({
      where: { id: user.id },
      include: {
        activation: true,
      },
    });
    const expiredAt = new Date(`${request.expiredDate}T23:59:59.999`).getTime();

    const data = await firstValueFrom(
      this.jingleService.createJingleSchedule({
        endTime: request.endTime,
        expiredAt,
        isCrossFade: request.isCrossFade,
        isLockMode: request.isLockMode,
        isPlaynow: request.isPlaynow,
        mode: request.mode,
        modeValue: request.modeValue,
        name: request.name,
        playlistIds: request.playlistIds,
        propertyId: user.propertyId,
        ratio: request.ratio,
        startTime: request.startTime,
        groupDeviceIds: request.groupDeviceIds,
        timeZone: userData.activation.timezone,
      }),
    );
    this.playerGateway.server.to(data.zoneIds).emit('queue:update', true);
    this.client.emit('queue-update-mob', {
      zoneIds: data.zoneIds,
      data: true,
    });
    this.client.emit('queue-update-atv', {
      zoneIds: data.zoneIds,
      data: true,
    });

    // if (data.isPlaynow && data.zoneIds.length > 0 && data.zoneIds !== null) {
    //   this.playerGateway.server
    //     .to(data.zoneIds)
    //     .emit('jingle-schedule-playnow:receive', {
    //       tracksIds: data.trackIds,
    //       isCrossFade: request.isCrossFade,
    //       isLockMode: request.isLockMode,
    //     });

    //   this.client.emit('jingle-schedule-playnow-mob', {
    //     zoneIds: data.zoneIds,
    //     data: {
    //       tracksIds: data.trackIds,
    //       isCrossFade: request.isCrossFade,
    //       isLockMode: request.isLockMode,
    //     },
    //   });
    //   this.client.emit('jingle-schedule-playnow-atv', {
    //     zoneIds: data.zoneIds,
    //     data: {
    //       tracksIds: data.trackIds,
    //       isCrossFade: request.isCrossFade,
    //       isLockMode: request.isLockMode,
    //     },
    //   });
    // }

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.decreaseFeatureQty({
      id: FEATURE.JingleSchedule,
      propertyId: user.propertyId,
      deviceId: user.deviceId,
    });

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Create Jinggle Schedule',
      data: null,
    };
  }
}
