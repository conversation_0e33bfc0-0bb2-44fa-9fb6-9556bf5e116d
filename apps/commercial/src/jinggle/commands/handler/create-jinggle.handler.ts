import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { CreateJinggleCommand } from '../impl';
import { basename, join } from 'path';
import { createReadStream, existsSync, unlinkSync, writeFileSync } from 'fs';
import { cwd } from 'process';
import { Subject } from 'rxjs';
import { JinggleService } from '../../jinggle.service';
import {
  BadRequestException,
  ForbiddenException,
  Inject,
  InternalServerErrorException,
  OnModuleInit,
  UnauthorizedException,
} from '@nestjs/common';
import { JINGGLE_PACKAGE } from '@app/common';
import { ClientGrpc, ClientProxy } from '@nestjs/microservices';
import { Metadata, status } from '@grpc/grpc-js';
import {
  CreateTrackRequest,
  VELODIVA_SERVICE_NAME,
  VelodivaServiceClient,
} from '@app/proto-schema/index.jinggle';
import { HttpStatusCode } from 'axios';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import {
  NotificationTags,
  NotificationType,
} from 'apps/commercial/src/common/constans/notification';
import { randomUUID } from 'crypto';
import { bufferToStreamChunks } from 'libs/utils/buffer-to-stream-chunks.utils';
import { JingleModel } from '../../model/jingle.model';

@CommandHandler(CreateJinggleCommand)
export class CreateJinggleHandler
  implements ICommandHandler<CreateJinggleCommand>, OnModuleInit
{
  private velodivaClient: VelodivaServiceClient;

  constructor(
    @Inject(JINGGLE_PACKAGE) private velodivaClientService: ClientGrpc,
    private prisma: PrismaService,
    private readonly publisher: EventPublisher,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: CreateJinggleCommand): Promise<any> {
    // return {
    //   statusCode: 200,
    //   message: 'Succesfully Created Jinggle',
    //   data: null,
    // };

    const { file, request, user } = command;

    // ------ CHECK FEATURE QUOTA ----------- //
    const jingleLibraryPackage = await this.prisma.packageFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.JingleLibrary,
          },
          {
            packageId: user.packageId,
          },
        ],
      },
    });

    if (!jingleLibraryPackage) {
      throw new ForbiddenException('Insufficient jingle library quota');
    }
    if (jingleLibraryPackage.qouta < 1 && jingleLibraryPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient jingle library quota');
    }
    // ------ END OF CHECK FEATURE QUOTA ----------- //

    if (request.type === 'audio' && file.mimetype !== 'audio/mpeg') {
      throw new BadRequestException('Invalid file type');
    }

    if (request.type === 'video' && file.mimetype !== 'video/mp4') {
      throw new BadRequestException('Invalid file type');
    }

    const userData = await this.prisma.user.findFirst({
      where: {
        id: user.id,
      },
      include: {
        properties: {
          where: {
            isDefault: true,
          },
          include: {
            property: true,
          },
        },
      },
    });

    if (!userData.isAdmin) {
      throw new UnauthorizedException('Youre not admin');
    }

    const jingleModel = this.publisher.mergeClassContext(JingleModel);
    const event = new jingleModel();
    event.createJingle({
      file,
      artist: request.artist,
      properties: userData.properties,
      title: request.title,
      category: request.category,
      source: request.source,
      type: request.type,
      originalname: file.originalname,
      propertyId: user.propertyId,
      userId: user.id,
      activationId: user.deviceId,
    });

    const limiterModel = this.publisher.mergeClassContext(LimiterModel);
    const limiter = new limiterModel();
    limiter.decreaseFeatureQty({
      id: FEATURE.JingleLibrary,
      propertyId: user.propertyId,
      deviceId: user.deviceId,
    });

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully processing jingle creation',
      data: null,
    };
  }

  onModuleInit() {
    this.velodivaClient =
      this.velodivaClientService.getService<VelodivaServiceClient>(
        VELODIVA_SERVICE_NAME,
      );
  }
}
