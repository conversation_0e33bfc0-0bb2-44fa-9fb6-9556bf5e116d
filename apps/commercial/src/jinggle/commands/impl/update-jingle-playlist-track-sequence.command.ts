import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { UpdateJinglePlaylistTrackSequenceDto } from '../../dto/update-jingle-playlist-track-sequence.dto';

export class UpdateJinglePlaylistTrackSequenceCommand implements ICommand {
  constructor(
    public readonly id: string,
    public readonly trackId: string,
    public readonly user: ICurrentUser,
    public readonly request: UpdateJinglePlaylistTrackSequenceDto,
  ) {}
}
