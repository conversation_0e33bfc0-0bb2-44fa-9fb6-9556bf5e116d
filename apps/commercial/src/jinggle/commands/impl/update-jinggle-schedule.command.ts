import { UpdateJingleScheduleRequest } from '@app/proto-schema/index.jinggle';
import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { CreateJinggleScheduleDto } from '../../dto/create-jinggle-schedule.dto';

export class UpdateJinggleScheduleCommand implements ICommand {
  constructor(
    public readonly request: CreateJinggleScheduleDto,
    public readonly user: ICurrentUser,
    public readonly id: string,
  ) {}
}
