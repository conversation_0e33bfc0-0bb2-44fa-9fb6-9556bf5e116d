import { UpdateJingleRequest } from '@app/proto-schema/index.jinggle';
import { ICommand } from '@nestjs/cqrs';
import { CreateJinggleDto } from '../../dto/create-jinggle.dto';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';

export class UpdateJingleCommand implements ICommand {
  constructor(
    public readonly request: CreateJinggleDto,
    public readonly user: ICurrentUser,
    public readonly id: string,
  ) {}
}
