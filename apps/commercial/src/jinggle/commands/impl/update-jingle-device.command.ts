import { UpdateJingleScheduleRequest } from '@app/proto-schema/index.jinggle';
import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { CreateJinggleDevicesDto } from '../../dto/create-jinggle-devices.dto';

export class UpdateJingleDeviceCommand implements ICommand {
  constructor(
    public readonly request: CreateJinggleDevicesDto,
    public readonly user: ICurrentUser,
    public readonly id: string,
  ) {}
}
