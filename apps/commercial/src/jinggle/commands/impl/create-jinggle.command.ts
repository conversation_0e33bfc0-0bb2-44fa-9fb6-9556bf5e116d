import { ICommand } from '@nestjs/cqrs';
import { CreateJinggleDto } from '../../dto/create-jinggle.dto';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';

export class CreateJinggleCommand implements ICommand {
  constructor(
    public readonly file: Express.Multer.File,
    public readonly request: CreateJinggleDto,
    public readonly user: ICurrentUser,
  ) {}
}
