import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { CreateJingglePlaylistDto } from '../../dto/create-jinggle-playlist.dto';

export class UpdateJingglePlaylistCommand implements ICommand {
  constructor(
    public readonly request: CreateJingglePlaylistDto,
    public readonly user: ICurrentUser,
    public readonly id: string,
  ) {}
}
