import { forwardRef, Module } from '@nestjs/common';
import { JinggleController } from './jinggle.controller';
import { JinggleService } from './jinggle.service';
import { JinggleCommanHandlers } from './commands';
import { CqrsModule } from '@nestjs/cqrs';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JINGGLE_PACKAGE, JinggleClient } from '@app/common';
import { JinggleQueryHandlers } from './queries';
import { EventsModule } from '../events/events.module';
import { JingleRpcController } from './jingle.rpc.controller';
import { JingleEventHandler } from './events';

@Module({
  imports: [
    CqrsModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) =>
          JinggleClient(configService),
        name: JINGGLE_PACKAGE,
        imports: [ConfigModule],
      },
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    forwardRef(() => EventsModule), // Use forwardRef here
  ],
  controllers: [JinggleController, JingleRpcController],
  providers: [
    JinggleService,
    ...JinggleCommanHandlers,
    ...JinggleQueryHandlers,
    ...JingleEventHandler,
  ],
  exports: [JinggleService],
})
export class JinggleModule {}
