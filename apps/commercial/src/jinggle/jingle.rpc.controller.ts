import { Empty } from '@app/proto-schema/index.common';
import {
  GetJingleSchedulerRequest,
  JINGLE_SERVICE_NAME,
  JingleServiceController,
  TrackJingleStatusRequest,
} from '@app/proto-schema/index.internal';
import { Metadata, status } from '@grpc/grpc-js';
import { Controller, Inject } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { ClientProxy, GrpcMethod, RpcException } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { PlayerGateway } from '../events/player.gateway';
import { PrismaService } from 'libs/prisma';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { RemoteSocket } from 'socket.io';

@Controller()
export class JingleRpcController implements JingleServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    @Inject('mobile') private client: ClientProxy,
    private playerGateway: PlayerGateway,
    private prisma: PrismaService,
    @InjectRedis() private readonly redis: Redis,
  ) {}
  @GrpcMethod(JINGLE_SERVICE_NAME, 'trackJingleStatus')
  async trackJingleStatus(
    request: TrackJingleStatusRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> {
    const data = await this.prisma.user.findMany({
      where: {
        properties: {
          some: {
            propertyId: request.propertyId,
          },
        },
        isAdmin: true,
      },
      select: { activationId: true },
    });

    this.playerGateway.server
      .to(data.flatMap((item) => item.activationId))
      .emit('jingle-create-status', {
        trackId: request.trackId,
        title: request.title,
        status: request.status,
      });

    return {};
  }

  @GrpcMethod(JINGLE_SERVICE_NAME, 'getJingleScheduler')
  async getJingleScheduler(
    request: GetJingleSchedulerRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Empty> {
    // console.log('CEASD ', request);
    this.playerGateway.server.to([request.zoneId]).emit('queue:update', true);
    this.client.emit('queue-update-mob', {
      zoneIds: [request.zoneId],
      data: true,
    });
    this.client.emit('queue-update-atv', {
      zoneIds: [request.zoneId],
      data: true,
    });
    const curentPlay = await this.prisma.currentPlayer.findFirst({
      where: {
        activationId: request.zoneId,
      },
    });
    if (curentPlay) {
      await this.redis.set(
        `schedule_jingle_isCurrentPLaying${request.zoneId}`,
        1,
      );
    }

    if (request.isPlaynow) {
      const deviceData = await this.prisma.activation.findFirst({
        where: {
          id: request.zoneId,
        },
        include: {
          activationDevice: {
            orderBy: { createdAt: 'desc' },
            where: { isActive: true },
            take: 1,
            include: { device: true },
          },
        },
      });

      const deviceType = deviceData?.activationDevice[0]?.device?.type;
      if (!deviceType) {
        console.log('Device type not found');
        return {};
      }

      const payload = {
        zoneId: request.zoneId,
        data: {
          tracksIds: request.trackIds,
          isCrossFade: request.isCrossfade,
          isLockMode: request.isLockMode,
          playlistId: request.playlistId,
          scheduleId: request.scheduleId,
        },
      };

      switch (deviceType) {
        case 'web': {
          const connectedDevice = await Promise.race([
            this.playerGateway.server
              .in(request.zoneId)
              .fetchSockets() as Promise<RemoteSocket<any, any>[]>,
            new Promise<RemoteSocket<any, any>[]>((_, reject) =>
              setTimeout(
                () =>
                  reject(
                    new RpcException({
                      code: status.INTERNAL,
                      message: 'Timeout fetching sockets',
                    }),
                  ),
                10000,
              ),
            ),
          ]);

          // console.log('CEKK CONECTED DEVICE', connectedDevice);
          if (connectedDevice.length <= 0) {
            await this.redis.set(
              `schedule_jingle_${request.zoneId}`,
              JSON.stringify(payload.data),
            );
            return {};
          }

          this.playerGateway.server
            .to(request.zoneId)
            .emit('jingle-schedule-playnow:receive', {
              tracksIds: request.trackIds,
              isCrossFade: request.isCrossfade,
              isLockMode: request.isLockMode,
              playlistId: request.playlistId,
              scheduleId: request.scheduleId,
            });
          break;
        }

        case 'mob':
          this.client.emit('jingle-schedule-playnow-mob', payload);
          break;

        case 'atv':
          this.client.emit('jingle-schedule-playnow-atv', payload);
          break;

        default:
          console.log(`Unknown device type: ${deviceType}`);
      }
    } else {
    }

    return {};
  }
}
