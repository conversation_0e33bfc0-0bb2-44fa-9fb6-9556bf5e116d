import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  OnModuleInit,
} from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Metadata, status } from '@grpc/grpc-js';
import { catchError, Observable, throwError } from 'rxjs';
import { RpcException } from '@nestjs/microservices';

import { JINGGLE_PACKAGE } from '@app/common';
import {
  CreateTrackRequest,
  VELODIVA_SERVICE_NAME,
  VelodivaServiceClient,
  CreateJinglePlaylistRequest,
  CreateJingleZoneRequest,
  CreateJingleScheduleRequest,
  GetJingleUrlTrackRatioRequest,
  IsScheduleAvailableRequest,
  JingleDefaultRequest,
  UpdateJingleRequest,
  UpdateJinglePlaylistRequest,
  UpdateJingleZoneRequest,
  UpdateJingleScheduleRequest,
  UpdateScheduleTimeRequest,
  GetQueueRequest,
  UpdateCurrentTrackRequest,
  UpdateJingleScheduleZoneTimeRequest,
  UpdateSequencePlaylistTrackRequest,
  GetUrlJinggleRequest,
} from '@app/proto-schema/index.jinggle';
import { Id, Query } from '@app/proto-schema/index.common';

@Injectable()
export class JinggleService implements OnModuleInit {
  private velodivaClient: VelodivaServiceClient;

  constructor(
    @Inject(JINGGLE_PACKAGE) private velodivaClientService: ClientGrpc,
  ) {}

  onModuleInit() {
    this.velodivaClient =
      this.velodivaClientService.getService<VelodivaServiceClient>(
        VELODIVA_SERVICE_NAME,
      );
  }

  private handleRpc<T>(rpcCall: Observable<T>): Observable<T> {
    return rpcCall.pipe(
      catchError((error) => {
        const grpcCode = error.code;
        const grpcMessage = error.details || error.message;
        switch (grpcCode) {
          case status.NOT_FOUND:
            return throwError(
              () =>
                new HttpException(
                  {
                    statusCode: HttpStatus.NOT_FOUND,
                    message: grpcMessage || 'Data not found',
                  },
                  HttpStatus.NOT_FOUND,
                ),
            );

          case status.INVALID_ARGUMENT:
          case status.ALREADY_EXISTS:
            let parsedData: any;

            try {
              parsedData = JSON.parse(grpcMessage);
            } catch {
              parsedData = grpcMessage;
            }

            return throwError(
              () =>
                new HttpException(
                  {
                    statusCode: HttpStatus.BAD_REQUEST,
                    message: 'Bad request',
                    data: parsedData,
                  },
                  HttpStatus.BAD_REQUEST,
                ),
            );

          case status.PERMISSION_DENIED:
            return throwError(
              () =>
                new HttpException(
                  {
                    statusCode: HttpStatus.FORBIDDEN,
                    message: grpcMessage || 'Permission denied',
                  },
                  HttpStatus.FORBIDDEN,
                ),
            );

          case status.UNAUTHENTICATED:
            return throwError(
              () =>
                new HttpException(
                  {
                    statusCode: HttpStatus.UNAUTHORIZED,
                    message: grpcMessage || 'Unauthenticated',
                  },
                  HttpStatus.UNAUTHORIZED,
                ),
            );

          default:
            return throwError(
              () =>
                new HttpException(
                  {
                    statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
                    message: grpcMessage || 'Unexpected error',
                  },
                  HttpStatus.INTERNAL_SERVER_ERROR,
                ),
            );
        }
      }),
    );
  }

  createTrack(request: Observable<CreateTrackRequest>) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.createTrack(request, meta));
  }

  listJinggles(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.listJinggles(request, meta));
  }

  getUrlJinggler(request: GetUrlJinggleRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.getUrlJinggle(request, meta));
  }

  createJinglePlaylist(request: CreateJinglePlaylistRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.createJinglePlaylist(request, meta),
    );
  }

  listJinglePlaylist(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJinglePlaylist(request, meta),
    );
  }

  createJingleZone(request: CreateJingleZoneRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.createJingleZone(request, meta));
  }

  listJingleZone(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.listJingleZone(request, meta));
  }

  createJingleSchedule(request: CreateJingleScheduleRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.createJingleSchedule(request, meta),
    );
  }

  listJingleSchedule(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJingleSchedule(request, meta),
    );
  }

  isScheduleAvailable(request: IsScheduleAvailableRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.isScheduleAvailable(request, meta),
    );
  }

  getJingleUrlTrackRatio(request: GetJingleUrlTrackRatioRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.getJingleUrlTrackRatio(request, meta),
    );
  }

  deleteJingleSchedule(request: Id) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.deleteJingleSchedule(request, meta),
    );
  }

  getReportSumamry(request: Id) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.getReportSumamry(request, meta));
  }

  listJingleReports(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.listJingleReports(request, meta));
  }

  listJingleScheduleReports(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJingleScheduleReports(request, meta),
    );
  }

  listJingleZoneReports(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJingleZoneReports(request, meta),
    );
  }

  listJingleTrackReports(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJingleTrackReports(request, meta),
    );
  }

  listJinglePlaylistReports(request: Query) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.listJinglePlaylistReports(request, meta),
    );
  }

  deleteJingle(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.deleteJingle(request, meta));
  }

  deleteJinglePlaylist(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.deleteJinglePlaylist(request, meta),
    );
  }

  deleteJingleZone(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.deleteJingleZone(request, meta));
  }

  updateJingle(request: UpdateJingleRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.updateJingle(request, meta));
  }

  updateJinglePlaylist(request: UpdateJinglePlaylistRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateJinglePlaylist(request, meta),
    );
  }

  updateJingleZone(request: UpdateJingleZoneRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.updateJingleZone(request, meta));
  }

  updateJingleSchedule(request: UpdateJingleScheduleRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateJingleSchedule(request, meta),
    );
  }

  detailJingle(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.detailJingle(request, meta));
  }

  detailJinglePlaylist(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.detailJinglePlaylist(request, meta),
    );
  }

  detailJingleZone(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.detailJingleZone(request, meta));
  }

  detailJingleSchedule(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.detailJingleSchedule(request, meta),
    );
  }

  updateScheduleTime(request: UpdateScheduleTimeRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateScheduleTime(request, meta),
    );
  }

  getJingleQueue(request: GetQueueRequest) {
    const meta = new Metadata();
    return this.handleRpc(this.velodivaClient.getJingleQueue(request, meta));
  }

  updateCurrentTrack(request: UpdateCurrentTrackRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateCurrentTrack(request, meta),
    );
  }

  updateJingleScheduleZoneTime(request: UpdateJingleScheduleZoneTimeRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateJingleScheduleZoneTime(request, meta),
    );
  }

  updateSequencePlaylistTrack(request: UpdateSequencePlaylistTrackRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.updateSequencePlaylistTrack(request, meta),
    );
  }

  getCurrentTrackJingle(request: JingleDefaultRequest) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.getCurrentTrackJingle(request, meta),
    );
  }

  getTemporaryJingleTrack(request: Id) {
    const meta = new Metadata();
    return this.handleRpc(
      this.velodivaClient.getTemporaryJingleTrack(request, meta),
    );
  }
}
