import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { GetJingleQuery } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@QueryHandler(GetJingleQuery)
export class Get<PERSON>ingleHandler implements IQueryHandler<GetJingleQuery> {
  constructor(private jingleService: JinggleService) {}

  async execute(query: GetJingleQuery): Promise<any> {
    const { user, id } = query;

    const data = await firstValueFrom(
      this.jingleService.detailJingle({ id, propertyId: user.propertyId }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get Jinggle',
      data: data,
    };
  }
}
