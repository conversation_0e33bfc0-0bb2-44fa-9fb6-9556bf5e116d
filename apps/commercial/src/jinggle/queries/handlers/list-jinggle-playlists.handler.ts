import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJingglePlaylistsQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom, lastValueFrom } from 'rxjs';

@QueryHandler(ListJingglePlaylistsQuery)
export class ListJingglePlaylistsHandler
  implements IQueryHandler<ListJingglePlaylistsQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJingglePlaylistsQuery): Promise<any> {
    const { user, args } = query;

    const data = await firstValueFrom(
      this.jingleService.listJinglePlaylist({
        params: { id: user.propertyId, ...(args as any) },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully List Get Jinggle Playlists',
      data: data.data,
      meta: data.meta,
    };
  }
}
