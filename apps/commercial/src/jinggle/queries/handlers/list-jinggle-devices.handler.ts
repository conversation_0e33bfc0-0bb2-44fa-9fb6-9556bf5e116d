import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJinggleDevicesQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(ListJinggleDevicesQuery)
export class ListJinggleDevicesHandler
  implements IQueryHandler<ListJinggleDevicesQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJinggleDevicesQuery): Promise<any> {
    const { user, args } = query;
    const data = await firstValueFrom(
      this.jingleService.listJingleZone({
        params: { id: user.propertyId, ...(args as any) },
      }),
    );
    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get List Jinggle Devices',
      data: data.data,
      meta: data.meta,
    };
  }
}
