import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { GetJingglePlaylistQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(GetJingglePlaylistQuery)
export class GetJingglePlaylistHandler
  implements IQueryHandler<GetJingglePlaylistQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: GetJingglePlaylistQuery): Promise<any> {
    const { user, id } = query;

    const data = await firstValueFrom(
      this.jingleService.detailJinglePlaylist({
        id,
        propertyId: user.propertyId,
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get Jinggle Playlist',
      data: data,
    };
  }
}
