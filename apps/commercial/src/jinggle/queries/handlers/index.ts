import { GetJinggleDeviceHandler } from './get-jinggle-device.handler';
import { GetJingglePlaylistHandler } from './get-jinggle-playlist.handler';
import { GetJinggleScheduleHandler } from './get-jinggle-schedule.handler';
import { GetJinggleSummaryHandler } from './get-jinggle-summary.handler';
import { GetJingleProcessingHandler } from './get-jingle-processing.handler';
import { GetJingleHandler } from './get-jingle.handler';
import { GetUrlJinggleHandler } from './get-url-jinggle.handler';
import { ListJinggleDevicesHandler } from './list-jinggle-devices.handler';
import { ListJingglePlaylistsHandler } from './list-jinggle-playlists.handler';
import { ListJinggleReportsHandler } from './list-jinggle-reports.handler';
import { ListJinggleSchedulesHandler } from './list-jinggle-schedules.handler';
import { ListJIngglesHandler } from './list-jinggles.handler';
import { ListJinglePlaylistReportHandler } from './list-jingle-playlist-report.handler';
import { ListJingleScheduleReportHandler } from './list-jingle-schdule-report.handler';
import { ListJingleTrackReportHandler } from './list-jingle-track-report.handler';
import { ListJingleZoneReportHandler } from './list-jingle-zone-report.handler';

export const JinggleQueryHandlers = [
  ListJIngglesHandler,
  GetUrlJinggleHandler,
  GetJinggleDeviceHandler,
  GetJingglePlaylistHandler,
  GetJinggleScheduleHandler,
  GetJinggleScheduleHandler,
  ListJinggleDevicesHandler,
  ListJingglePlaylistsHandler,
  ListJinggleReportsHandler,
  ListJinggleSchedulesHandler,
  ListJingleScheduleReportHandler,
  ListJingleZoneReportHandler,
  GetJinggleSummaryHandler,
  ListJingleTrackReportHandler,
  ListJinglePlaylistReportHandler,
  GetJingleHandler,
  GetJingleProcessingHandler,
];
