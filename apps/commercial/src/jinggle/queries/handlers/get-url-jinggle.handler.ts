import { EventPublisher, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetUrlJinggleQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { HttpStatusCode } from 'axios';
import { lastValueFrom } from 'rxjs';
import { JinggleService } from '../../jinggle.service';
import { ForbiddenException } from '@nestjs/common';

@QueryHandler(GetUrlJinggleQuery)
export class GetUrlJinggleHandler implements IQueryHandler<GetUrlJinggleQuery> {
  constructor(
    private prisma: PrismaService,
    private jinggleService: JinggleService,
  ) {}

  async execute(query: GetUrlJinggleQuery): Promise<any> {
    const { user, request, id } = query;

    const data = await lastValueFrom(
      this.jinggleService.getUrlJinggler({
        jingleId: id,
        playlistId: request.playlistId,
        zoneId: user.deviceId,
        scheduleId: request.scheduleId,
      }),
    );

    if (data) {
      if (data.status == 'suspend') {
        throw new ForbiddenException('Your Jingle Has Suspended');
      }

      const datas = {
        type: 'jingle',
        category: data.category,
        url: data.url,
        track: {
          id: data.trackId,
          artists: [
            {
              name: data.artistName,
            },
          ],
          title: data.trackName,
          duration: data.duration,
        },
      };

      return {
        statusCode: HttpStatusCode.Ok,
        message: 'Successfully Get Url Jinggle',
        data: datas,
      };
    }
  }
}
