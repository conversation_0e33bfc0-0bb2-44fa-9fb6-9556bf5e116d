import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetJinggleDeviceQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { PrismaService } from 'libs/prisma';

@QueryHandler(GetJinggleDeviceQuery)
export class GetJinggleDeviceHandler
  implements IQueryHandler<GetJinggleDeviceQuery>
{
  constructor(
    private jingleService: JinggleService,
    private prisma: PrismaService,
  ) {}

  async execute(query: GetJinggleDeviceQuery): Promise<any> {
    const { user, id } = query;

    const data = await firstValueFrom(
      this.jingleService.detailJingleZone({ id, propertyId: user.propertyId }),
    );

    const zones = await this.prisma.activation.findMany({
      where: {
        id: { in: data.zoneIds },
      },
      include: {
        activationDevice: {
          take: 1,
          orderBy: { createdAt: 'desc' },
          include: {
            device: true,
          },
        },
      },
    });

    const zoneData = zones.map((zone) => ({
      deviceName: zone.activationDevice[0]?.device?.name,
      id: zone.id,
      name: zone.zone,
      serialNumber: zone.activationDevice[0]?.device?.serialNumber,
      timezone: zone.timezone,
    }));

    const { zoneIds, ...dataNew } = data;

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get Jinggle Device',
      data: { ...dataNew, zoneData },
    };
  }
}
