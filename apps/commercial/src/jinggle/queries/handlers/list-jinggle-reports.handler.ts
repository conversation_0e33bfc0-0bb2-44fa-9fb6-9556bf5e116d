import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJinggleReportsQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(ListJinggleReportsQuery)
export class ListJinggleReportsHandler
  implements IQueryHandler<ListJinggleReportsQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJinggleReportsQuery): Promise<any> {
    const { user, args } = query;

    const data = await firstValueFrom(
      this.jingleService.listJingleReports({
        params: {
          ...(args as any),
          propertyId: user.propertyId,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully List Get Jinggle Reports',
      data: data.data,
      meta: data.meta,
    };
  }
}
