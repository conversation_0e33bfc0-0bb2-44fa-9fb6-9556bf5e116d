import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { GetJinggleScheduleQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(GetJinggleScheduleQuery)
export class GetJinggleScheduleHandler
  implements IQueryHandler<GetJinggleScheduleQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: GetJinggleScheduleQuery): Promise<any> {
    const { user, id } = query;

    const data = await firstValueFrom(
      this.jingleService.detailJingleSchedule({
        id,
        propertyId: user.propertyId,
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get Jinggle Schedule',
      data: data,
    };
  }
}
