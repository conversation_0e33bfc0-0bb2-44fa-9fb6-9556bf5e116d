import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { ListJingleScheduleReportQuery } from '../impl/list-jingle-schedule-report.query';
import { HttpStatusCode } from 'axios';

@QueryHandler(ListJingleScheduleReportQuery)
export class ListJingleScheduleReportHandler
  implements IQueryHandler<ListJingleScheduleReportQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJingleScheduleReportQuery): Promise<any> {
    const { args, user, id } = query;

    const data = await firstValueFrom(
      this.jingleService.listJingleScheduleReports({
        params: {
          ...(args as any),
          propertyId: user.propertyId,
          playlistId: id,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Get List Jingle Schedule Report',
      data: data.data,
      meta: data.meta,
    };
  }
}
