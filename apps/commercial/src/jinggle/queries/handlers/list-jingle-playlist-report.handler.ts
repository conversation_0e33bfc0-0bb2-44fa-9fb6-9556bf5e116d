import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { ListJinglePlaylistReportQuery } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@QueryHandler(ListJinglePlaylistReportQuery)
export class ListJinglePlaylistReportHandler
  implements IQueryHandler<ListJinglePlaylistReportQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJinglePlaylistReportQuery): Promise<any> {
    const { args, id, user } = query;

    const data = await firstValueFrom(
      this.jingleService.listJinglePlaylistReports({
        params: {
          ...(args as any),
          propertyId: user.propertyId,
          playlistId: id,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Get List Jingle Playlist Report',
      data: data.data,
      meta: data.meta,
    };
  }
}
