import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJingglesQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { firstValueFrom, lastValueFrom } from 'rxjs';
import { VelodivaServiceClient } from '@app/proto-schema/index.jinggle';
import { JinggleService } from '../../jinggle.service';
import { HttpStatusCode } from 'axios';

@QueryHandler(ListJingglesQuery)
export class ListJIngglesHandler implements IQueryHandler<ListJingglesQuery> {
  constructor(
    private prisma: PrismaService,
    private jinggleService: JinggleService,
  ) {}

  async execute(query: ListJingglesQuery): Promise<any> {
    const { user, args } = query;

    const property = await this.prisma.property.findFirst({
      where: {
        id: user.propertyId,
      },
    });

    const data = await lastValueFrom(
      this.jinggleService.listJinggles({
        params: { id: user.propertyId, ...(args as any) },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfuly Get List Jinggles',
      data: data.data.map((data) => ({
        ...data,
        cid: property.cid,
      })),
      meta: data.meta,
    };
  }
}
