import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { ListJingleZoneReportQuery } from '../impl/list-jingle-zone-report.query';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@QueryHandler(ListJingleZoneReportQuery)
export class ListJingleZoneReportHandler
  implements IQueryHandler<ListJingleZoneReportQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJingleZoneReportQuery): Promise<any> {
    const { args, user, id } = query;
    const data = await firstValueFrom(
      this.jingleService.listJingleZoneReports({
        params: {
          ...(args as any),
          propertyId: user.propertyId,
          playlistId: id,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Get List Zone Report',
      data: data.data,
      meta: data.meta,
    };
  }
}
