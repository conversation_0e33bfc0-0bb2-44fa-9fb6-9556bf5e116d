import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>uery<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJingleTrackReportQuery } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@CommandHandler(ListJingleTrackReportQuery)
export class ListJingleTrackReportHandler
  implements IQueryHandler<ListJingleTrackReportQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJingleTrackReportQuery): Promise<any> {
    const { user, id, args } = query;

    const data = await firstValueFrom(
      this.jingleService.listJingleTrackReports({
        params: {
          ...(args as any),
          propertyId: user.propertyId,
          playlistId: id,
        },
      }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfuly Get List Track Report',
      data: data.data,
      meta: data.meta,
    };
  }
}
