import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetJingleProcessingQuery } from '../impl';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';
import { HttpStatusCode } from 'axios';

@QueryHandler(GetJingleProcessingQuery)
export class GetJingleProcessingHandler
  implements IQueryHandler<GetJingleProcessingQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: GetJingleProcessingQuery): Promise<any> {
    const { user } = query;

    const data = await firstValueFrom(
      this.jingleService.getTemporaryJingleTrack({ id: user.propertyId }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Succesfully Get Processing Jingle',
      data: data.data,
    };
  }
}
