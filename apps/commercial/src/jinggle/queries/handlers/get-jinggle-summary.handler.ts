import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { HttpStatusCode } from 'axios';
import { GetJinggleSummaryQuery } from '../impl';
import { firstValueFrom } from 'rxjs';
import { JinggleService } from '../../jinggle.service';

@QueryHandler(GetJinggleSummaryQuery)
export class GetJinggleSummaryHandler
  implements IQueryHandler<GetJinggleSummaryQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: GetJinggleSummaryQuery): Promise<any> {
    const { user } = query;
    const data = await firstValueFrom(
      this.jingleService.getReportSumamry({ id: user.propertyId }),
    );

    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully Get Jinggle Summary',
      data: {
        playcount: {
          title: 'Total Playcount',
          value: data.totalPlayCount,
          unit: 'Unit',
          thanYesterday: data.yesterdayTotalPlayCount,
        },
        playtime: {
          title: 'Total Playtime',
          value: data.totalPlayTime,
          unit: 'Unit',
          thanYesterday: data.yesterdayTotalPlayTime,
        },
        targetDevice: {
          title: 'Total Target Device',
          value: data.totalTargetDevice,
          unit: 'Unit',
        },
      },
    };
  }
}
