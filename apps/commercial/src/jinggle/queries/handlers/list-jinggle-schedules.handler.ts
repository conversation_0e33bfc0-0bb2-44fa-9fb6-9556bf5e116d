import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListJinggleSchedulesQuery } from '../impl';
import { HttpStatusCode } from 'axios';
import { JinggleService } from '../../jinggle.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(ListJinggleSchedulesQuery)
export class ListJinggleSchedulesHandler
  implements IQueryHandler<ListJinggleSchedulesQuery>
{
  constructor(private jingleService: JinggleService) {}

  async execute(query: ListJinggleSchedulesQuery): Promise<any> {
    const { user, args } = query;
    const data = await firstValueFrom(
      this.jingleService.listJingleSchedule({
        params: { id: user.propertyId, ...(args as any) },
      }),
    );
    return {
      statusCode: HttpStatusCode.Ok,
      message: 'Successfully List Get Jinggle Schedules',
      data: data.data,
      meta: data.meta,
    };
  }
}
