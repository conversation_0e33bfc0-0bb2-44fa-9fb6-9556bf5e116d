import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsOptional,
  IsString,
  IsDateString,
  IsNotEmpty,
  ValidateIf,
  IsEnum,
  IsArray,
  ArrayUnique,
  ArrayMaxSize,
  IsIn,
  IsNumber,
  Min,
  Max,
  isInt,
  Matches,
  Validate,
} from 'class-validator';
import { IsTimeLessThen } from '../../validator/time-less-then.validate';
import { IsTimeMoreThen } from '../../validator/time-more-then.validate';
import { $Enums } from '@prisma/client';
import { DateFormat } from '@validator/validator';
import { integer } from '@elastic/elasticsearch/lib/api/types';
import { IsTimeValidConstraint } from '../../validator/is-time-string-valid.validate';
import { IsEndTimeAfterStartTimeConstraint } from '../../validator/is-endtime-string-less-than-starttime.validate';

export class CreateJinggleScheduleDto {
  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  playlistIds: string[];

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ValidateIf((prop) => prop.address != '')
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsNotEmpty({ message: 'startTime cannot be empty' })
  @IsString({ message: 'startTime must be a string' })
  @Matches(/^([0-1][0-9]|2[0-3]):([0-5][0-9])$/, {
    message: 'startTime must be in format HH:MM (00:00 to 23:59)',
  })
  @Validate(IsTimeValidConstraint)
  startTime: string;

  @ValidateIf((prop) => prop.address != '')
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  @IsNotEmpty({ message: 'endTime cannot be empty' })
  @IsString({ message: 'endTime must be a string' })
  @Matches(/^([0-1][0-9]|2[0-3]):([0-5][0-9])$/, {
    message: 'endTime must be in format HH:MM (00:00 to 23:59)',
  })
  @Validate(IsTimeValidConstraint)
  @ValidateIf((o) => !!o.startTime)
  @Validate(IsEndTimeAfterStartTimeConstraint, {
    message: 'endTime must not be earlier than startTime',
  })
  endTime: string;

  @ApiProperty({
    enum: $Enums.ScheduleType,
    default: $Enums.ScheduleType.daily,
  })
  @IsEnum($Enums.ScheduleType)
  mode: $Enums.ScheduleType;

  @ApiProperty({ type: [String] })
  @IsArray()
  @ArrayUnique()
  @ArrayMaxSize(7)
  @IsString({ each: true })
  @IsIn(
    [
      'monday',
      'tuesday',
      'wednesday',
      'thursday',
      'friday',
      'saturday',
      'sunday',
    ],
    { each: true },
  )
  modeValue: string[];

  @ApiProperty()
  @IsBoolean()
  playNow: boolean;

  @ApiProperty({ required: false, format: 'YYYY-MM-DD' })
  @ValidateIf((prop) => prop.checkout != '')
  @DateFormat('YYYY-MM-DD')
  expiredDate?: string;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  groupDeviceIds?: string[];

  @ApiProperty()
  @IsBoolean()
  isCrossFade: boolean;

  @ApiProperty()
  @IsBoolean()
  isPlaynow: boolean;

  @ApiProperty()
  @IsBoolean()
  isLockMode: boolean;

  @ApiProperty()
  @IsNumber()
  @Min(1)
  ratio: number;
}
