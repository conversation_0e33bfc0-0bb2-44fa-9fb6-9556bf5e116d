import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsOptional,
  IsString,
  IsDateString,
  IsArray,
  IsNotEmpty,
} from 'class-validator';

export class CreateJingglePlaylistDto {
  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  jinggleId?: string[];

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  description: string;
}
