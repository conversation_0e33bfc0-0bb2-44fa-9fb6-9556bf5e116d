import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsOptional,
  IsString,
  IsDateString,
  IsEnum,
  IsNotEmpty,
} from 'class-validator';
export enum JinggleCategory {
  AUDIO = '.MP3',
  VIDEO = '.MP4',
}

export enum JinggleType {
  AUDIO = 'audio',
  VIDEO = 'video',
}

export class CreateJinggleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  artist: string;

  @ApiProperty({ enum: JinggleType })
  @IsNotEmpty()
  @IsEnum(JinggleType)
  type: JinggleType;

  @ApiProperty({ enum: JinggleCategory })
  @IsNotEmpty()
  @IsEnum(JinggleCategory)
  category: JinggleCategory;

  @ApiPropertyOptional()
  // @IsNotEmpty()
  @IsString()
  source: string;
}
