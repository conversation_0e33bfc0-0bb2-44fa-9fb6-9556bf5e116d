import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from '@nestjs/swagger';
import { diskStorage } from 'multer';
import { extname } from 'path';
import {
  CreateJinggleCommand,
  DeleteJinggleScheduleCommand,
  DeleteJinglePlaylistCommand,
  DeleteJingleZoneCommand,
  DelteJingleCommand,
  UpdateJinglePlaylistTrackSequenceCommand,
} from './commands';
import { CreateJinggleDto } from './dto/create-jinggle.dto';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { User } from '../auth/decorator/user.decorator';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ListJingglesDto } from './dto/list-jinggles.dto';
import {
  GetJingleProcessingQuery,
  GetJingleQuery,
  GetUrlJinggleQuery,
  ListJingglesQuery,
  ListJinglePlaylistReportQuery,
  ListJingleScheduleReportQuery,
  ListJingleTrackReportQuery,
  ListJingleZoneReportQuery,
} from './queries';
import { CreateJingglePlaylistDto } from './dto/create-jinggle-playlist.dto';
import { CreateJinggleScheduleDto } from './dto/create-jinggle-schedule.dto';
import { CreateJinggleDevicesDto } from './dto/create-jinggle-devices.dto';
import { ListJinggleReportsQuery } from './queries/impl/list-jinggle-reports.query';
import { GetJinggleSummaryQuery } from './queries/impl/get-jinggle-summary.query';
import { CreateJinggleDevicesCommand } from './commands/impl/create-jinggle-devices.command';
import { GetJinggleDeviceQuery } from './queries/impl/get-jinggle-device.query';
import { ListJinggleDevicesQuery } from './queries/impl/list-jinggle-devices.query';
import { ListJinggleSchedulesQuery } from './queries/impl/list-jinggle-schedules.query';
import { GetJinggleScheduleQuery } from './queries/impl/get-jinggle-schedule.query';
import { CreateJinggleScheduleCommand } from './commands/impl/create-jinggle-schedule.command';
import { ListJingglePlaylistsQuery } from './queries/impl/list-jinggle-playlists.query';
import { GetJingglePlaylistQuery } from './queries/impl/get-jinggle-playlist.query';
import { CreateJingglePlaylistCommand } from './commands/impl/create-jinggle-playlist.command';
import { UpdateJingglePlaylistCommand } from './commands/impl/update-jinggle-playlist.command';
import { UpdateJinggleScheduleCommand } from './commands/impl/update-jinggle-schedule.command';
import { UpdateJingleDeviceCommand } from './commands/impl/update-jingle-device.command';
import { UpdateJingleCommand } from './commands/impl/update-jingle.command';
import { AdminGuard } from '../auth/guards/admin-guard.guard';
import { UpdateJinglePlaylistTrackSequenceDto } from './dto/update-jingle-playlist-track-sequence.dto';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { GetJingleUrlDto } from './dto/get-jinggle-url.dto';

@Controller('jinggle')
@ApiTags('Jinggle')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class JinggleController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @UseGuards(AdminGuard)
  @Post('jinggle-playlist')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async createJingglePlaylist(
    @Body() body: CreateJingglePlaylistDto,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(
      new CreateJingglePlaylistCommand(user, body),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jinggle-playlist/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async getJingglePlaylist(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return await this.queryBus.execute(new GetJingglePlaylistQuery(id, user));
  }

  @Get('jinggle-playlists')
  // @UseGuards(AccessFeatureGuard)
  // @AccessFeature(FEATURE.Jingle)
  async listJingglePlaylists(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJingglePlaylistsQuery(user, args),
    );
  }

  @Get('jinggle-processing')
  // @UseGuards(AccessFeatureGuard)
  // @AccessFeature(FEATURE.Jingle)
  async jingleProcessing(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(new GetJingleProcessingQuery(user));
  }

  @UseGuards(AdminGuard)
  @Delete('jinggle-playlist/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async deleteJingglePlaylist(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(
      new DeleteJinglePlaylistCommand(user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Patch('jinggle-playlist/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async updateJingglePlaylist(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Body() body: CreateJingglePlaylistDto,
  ) {
    return await this.commandBus.execute(
      new UpdateJingglePlaylistCommand(body, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Patch('jinggle-playlist/:id/:trackId/sequence')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async updateJinglePlaylistTrackSequence(
    @Param('id') id: string,
    @Param('trackId') trackId: string,
    @User() user: ICurrentUser,
    @Body() body: UpdateJinglePlaylistTrackSequenceDto,
  ) {
    return await this.commandBus.execute(
      new UpdateJinglePlaylistTrackSequenceCommand(id, trackId, user, body),
    );
  }

  @UseGuards(AdminGuard)
  @Post('jinggle-schedule')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async createJinggleSchedule(
    @Body() body: CreateJinggleScheduleDto,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(
      new CreateJinggleScheduleCommand(body, user),
    );
  }

  @UseGuards(AdminGuard)
  @Delete('jinggle-schedule/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async deleteJinggleSchedule(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(
      new DeleteJinggleScheduleCommand(user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Patch('jinggle-schedule/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async updateJinggleSchedule(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Body() body: CreateJinggleScheduleDto,
  ) {
    return await this.commandBus.execute(
      new UpdateJinggleScheduleCommand(body, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jinggle-schedule/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async getJinggleSchedule(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return await this.queryBus.execute(new GetJinggleScheduleQuery(user, id));
  }

  @UseGuards(AdminGuard)
  @Get('jinggle-schedules')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJinggleSchedules(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJinggleSchedulesQuery(user, args),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jinggle-devices')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJinggleDevices(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(new ListJinggleDevicesQuery(user, args));
  }

  @UseGuards(AdminGuard)
  @Get('jinggle-devices/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async getJinggleDevice(@Param('id') id: string, @User() user: ICurrentUser) {
    return await this.queryBus.execute(new GetJinggleDeviceQuery(user, id));
  }

  @UseGuards(AdminGuard)
  @Delete('jinggle-devices/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async deleteJingleDevice(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(new DeleteJingleZoneCommand(user, id));
  }

  @UseGuards(AdminGuard)
  @Patch('jinggle-devices/:id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async updateJingleDevice(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Body() body: CreateJinggleDevicesDto,
  ) {
    return await this.commandBus.execute(
      new UpdateJingleDeviceCommand(body, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Post('jinggle-devices')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async createJinggleDevice(
    @Body() body: CreateJinggleDevicesDto,
    @User() user: ICurrentUser,
  ) {
    return await this.commandBus.execute(
      new CreateJinggleDevicesCommand(user, body),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jingle-summary')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async getJinggleSummary(@User() user: ICurrentUser) {
    return await this.queryBus.execute(new GetJinggleSummaryQuery(user));
  }

  @UseGuards(AdminGuard)
  @Get('jingle-reports')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJinggleReports(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(new ListJinggleReportsQuery(user, args));
  }

  @UseGuards(AdminGuard)
  @Get('jingle-reports/:id/schedule')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJingleSchduleReports(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJingleScheduleReportQuery(args, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jingle-reports/:id/device')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJingleDeviceReports(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJingleZoneReportQuery(args, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jingle-reports/:id/track')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJingleTrackReports(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJingleTrackReportQuery(args, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Get('jingle-reports/:id/playlist')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async listJinglePlaylistReports(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(
      new ListJinglePlaylistReportQuery(args, user, id),
    );
  }

  @UseGuards(AdminGuard)
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  // @UseInterceptors(
  //   FileInterceptor('file', {
  //     storage: diskStorage({
  //       destination: './uploads',
  //       filename: (_, file, cb) => {
  //         // const uniqueName = `${Date.now()}${extname(file.originalname)}`;
  //         cb(null, file.originalname);
  //       },
  //     }),
  //   }),
  // )
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        title: {
          type: 'string',
        },
        artist: {
          type: 'string',
        },
        type: {
          type: 'string',
        },
        category: {
          type: 'string',
        },
        source: {
          type: 'string',
        },
        expiredAt: {
          type: 'string',
          format: 'date-time',
          example: '2025-04-30T23:59:00Z',
        },
      },
    },
  })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async createJinggle(
    // @UploadedFile(
    //   new ParseFilePipe({
    //     validators: [
    //       new MaxFileSizeValidator({ maxSize: 5242880 }),
    //       new FileTypeValidator({ fileType: /(audio\/mpeg|video\/mp4)/ }),
    //     ],
    //   }),
    // )
    @UploadedFile() file: Express.Multer.File,

    // file: Express.Multer.File,
    @Body() request: CreateJinggleDto,
    @User() user: ICurrentUser,
  ) {
    if (!file) {
      throw new BadRequestException('File is required');
    }

    const isAudio = file.mimetype.startsWith('audio/');
    const isVideo = file.mimetype.startsWith('video/');

    if (!isAudio && !isVideo) {
      throw new BadRequestException('Only audio or video files are allowed');
    }

    if (isAudio && file.size > 5 * 1024 * 1024) {
      throw new BadRequestException('Audio file size must be less than 5MB');
    }

    if (isVideo && file.size > 10 * 1024 * 1024) {
      throw new BadRequestException('Video file size must be less than 10MB');
    }

    return await this.commandBus.execute(
      new CreateJinggleCommand(file, request, user),
    );
  }

  @UseGuards(AdminGuard)
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  @Get()
  async listJinggles(
    @User() user: ICurrentUser,
    @Query() args: ListJingglesDto,
  ) {
    return await this.queryBus.execute(new ListJingglesQuery(user, args));
  }

  @Get(':id/url')
  async getUrlJinggle(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Query() args: GetJingleUrlDto,
  ) {
    return await this.queryBus.execute(new GetUrlJinggleQuery(user, id, args));
  }

  @UseGuards(AdminGuard)
  @Get(':id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async getJingle(@User() user: ICurrentUser, @Param('id') id: string) {
    return await this.queryBus.execute(new GetJingleQuery(user, id));
  }

  @UseGuards(AdminGuard)
  @Delete(':id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async deleteJingle(@User() user: ICurrentUser, @Param('id') id: string) {
    return await this.commandBus.execute(new DelteJingleCommand(user, id));
  }

  @UseGuards(AdminGuard)
  @Patch(':id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Jingle)
  async updateJingle(
    @User() user: ICurrentUser,
    @Param('id') id: string,
    @Body() body: CreateJinggleDto,
  ) {
    return await this.commandBus.execute(
      new UpdateJingleCommand(body, user, id),
    );
  }
}
