import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetMusicPlayerOverviewQuery } from '../impl';

@QueryHandler(GrpcGetMusicPlayerOverviewQuery)
export class GrpcGetMusicPlayerOverviewHandler implements IQueryHandler<GrpcGetMusicPlayerOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetMusicPlayerOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                SELECT 
                    COUNT(CASE WHEN type = 'web' THEN 1 END) as web_count,
                    COUNT(CASE WHEN type = 'atv' THEN 1 END) as atv_count,
                    COUNT(CASE WHEN type = 'mob' AND platform = 'android' THEN 1 END) as android_count,
                    COUNT(CASE WHEN type = 'mob' AND platform = 'ios' THEN 1 END) as ios_count
                FROM "Device"
                WHERE "isActive" = true;
            `;

            const counts = result[0];
            
            return {
                webCount: counts.web_count.toString(),
                atvCount: counts.atv_count.toString(),
                androidCount: counts.android_count.toString(),
                iosCount: counts.ios_count.toString()
            };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}