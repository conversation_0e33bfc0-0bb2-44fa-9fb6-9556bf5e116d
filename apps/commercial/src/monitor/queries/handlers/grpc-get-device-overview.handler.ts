import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetDeviceOverviewQuery } from '../impl';

@QueryHandler(GrpcGetDeviceOverviewQuery)
export class GrpcGetDeviceOverviewHandler implements IQueryHandler<GrpcGetDeviceOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetDeviceOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                SELECT 
                    COUNT(CASE WHEN "isActive" = true THEN 1 END) as active_count,
                    COUNT(CASE WHEN "isActive" = false THEN 1 END) as inactive_count,
                    COUNT(*) as total_count
                FROM "Device";
            `;

            const counts = result[0];
            
            return {
                activeCount: counts.active_count.toString(),
                inactiveCount: counts.inactive_count.toString(),
                total: counts.total_count.toString()
            };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}