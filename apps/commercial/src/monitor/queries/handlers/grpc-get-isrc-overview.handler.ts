import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetISRCOverviewQuery } from '../impl';

@QueryHandler(GrpcGetISRCOverviewQuery)
export class GrpcGetISRCOverviewHandler implements IQueryHandler<GrpcGetISRCOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetISRCOverviewQuery) {
        try {
            const { isrc } = query.query;

            if (!isrc || !Array.isArray(isrc) || isrc.length === 0) {
                return { data: [] };
            }

            const result = await this.prisma.$queryRaw<Array<{ isrc: string, count: string }>>`
                WITH isrc_counts AS (
                    SELECT 
                        SUBSTRING(source->>'isrc', 1, 2) as isrc_prefix,
                        COUNT(ph.id) as play_count
                    FROM "Track" t
                    LEFT JOIN "PlayHistory" ph ON ph."trackId" = t.id
                    WHERE source->>'isrc' IS NOT NULL
                    AND SUBSTRING(source->>'isrc', 1, 2) = ANY(${isrc}::text[])
                    GROUP BY SUBSTRING(source->>'isrc', 1, 2)
                )
                SELECT 
                    isrc_prefix as isrc,
                    play_count::text as count
                FROM isrc_counts
                WHERE isrc_prefix != ''
                ORDER BY isrc_prefix;
            `;

            return {
                data: result.map(item => ({
                    isrc: item.isrc,
                    count: item.count
                }))
            };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}