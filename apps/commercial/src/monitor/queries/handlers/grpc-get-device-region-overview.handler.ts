import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetDeviceRegionOverviewQuery } from '../impl';

@QueryHandler(GrpcGetDeviceRegionOverviewQuery)
export class GrpcGetDeviceRegionOverviewHandler implements IQueryHandler<GrpcGetDeviceRegionOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetDeviceRegionOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                WITH device_counts AS (
                    SELECT 
                        p.id as postal_id,
                        pr.name as province,
                        p.city,
                        p.urban,
                        p.district,
                        p.code,
                        COUNT(CASE WHEN d."isActive" = true THEN 1 END) as active_count,
                        COUNT(CASE WHEN d."isActive" = false THEN 1 END) as inactive_count,
                        COUNT(CASE WHEN ad."isActive" = false THEN 1 END) as suspended_count,
                        COUNT(d.id) as total_count
                    FROM "Postal" p
                    LEFT JOIN "Provinces" pr ON pr.id = p."provincesId"
                    LEFT JOIN "Property" prop ON prop."postalId" = p.id
                    LEFT JOIN "Activation" a ON a."propertyId" = prop.id
                    LEFT JOIN "ActivationDevice" ad ON ad."activationId" = a.id
                    LEFT JOIN "Device" d ON d.id = ad."deviceId"
                    GROUP BY p.id, pr.name, p.city, p.urban, p.district, p.code
                    HAVING COUNT(d.id) > 0
                )
                SELECT 
                    province,
                    city,
                    urban,
                    district,
                    code,
                    COALESCE(active_count, 0)::text as "activeCount",
                    COALESCE(inactive_count, 0)::text as "inactiveCount",
                    COALESCE(suspended_count, 0)::text as "suspendedCount",
                    COALESCE(total_count, 0)::text as "totalCount"
                FROM device_counts
                ORDER BY province, city, urban, district;
            `;

            return {
                data: result.map(row => ({
                    province: row.province || '',
                    city: row.city || '',
                    urban: row.urban || '',
                    district: row.district || '',
                    code: row.code || '',
                    activeCount: row.activeCount,
                    inactiveCount: row.inactiveCount,
                    suspendedCount: row.suspendedCount,
                    totalCount: row.totalCount
                }))
            };

        } catch (error) {
            throw new RpcException(error);
        }
    }
}