import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetDetailDeviceQuery } from '../impl';

interface TrackSource {
  title: string;
  artists: Array<{
    name: string;
    type: string;
  }>;
  album: {
    name: string;
  };
  duration: string;
  isrc: string;
}

@QueryHandler(GrpcGetDetailDeviceQuery)
export class GrpcDetailDeviceHandler implements IQueryHandler<GrpcGetDetailDeviceQuery> {
  constructor(
    private prisma: PrismaService,
  ) { }

  async execute(query: GrpcGetDetailDeviceQuery) {
    const { query: q } = query;
    const id = q.id;

    try {
      const device = await this.prisma.device.findFirst({
        where: {
          OR: [
            { id: q.id },
            { serialNumber: id },
            { guid: id }
          ]
        },
        include: {
          activation: {
            include: {
              activation: {
                include: {
                  property: true,
                  user: {
                    include: {
                      profile: true
                    }
                  },
                  currentPlayer: {
                    take: 1,
                    orderBy: {
                      updatedAt: 'desc',
                    },
                    include: {
                      track: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!device) {
        throw new RpcException('Device not found');
      }

      const activation = device.activation[0]?.activation;
      const currentPlayer = activation?.currentPlayer[0];
      const property = activation?.property;
      const user = activation?.user;
      const trackSource = currentPlayer?.track?.source ? JSON.parse(JSON.stringify(currentPlayer.track.source)) as TrackSource : null;

      return {
        id: device.id, 
        cid: property?.cid || '',
        companyName: property?.companyName || '',
        deviceType: device.type || '',
        deviceId: device.serialNumber || device.guid || device.id,
        songTitle: trackSource?.title || '',
        artist: trackSource?.artists?.map(a => a.name).join(', ') || '',
        duration: currentPlayer?.duration?.toString() || '0',
        playedTime: currentPlayer?.createdAt?.toISOString() || '',
        uptime: device.updatedAt?.toISOString() || '',
        userName: user?.username || '',
        password: user?.password || '',
        email: user?.email || '',
        statusAccount: user?.status || ''
      };

    } catch (error) {
      throw new RpcException(error);
    }
  }
}