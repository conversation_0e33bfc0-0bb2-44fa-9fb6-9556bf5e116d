import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetPlayHistoryOverviewQuery } from '../impl';

@QueryHandler(GrpcGetPlayHistoryOverviewQuery)
export class GrpcGetPlayHistoryOverviewHandler implements IQueryHandler<GrpcGetPlayHistoryOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetPlayHistoryOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT CASE WHEN country IS NOT NULL THEN country END) as country_count,
                    COUNT(DISTINCT CASE WHEN "trackId" IS NOT NULL THEN "trackId" END) as song_count
                FROM "PlayHistory";
            `;

            const overview = result[0];
            
            return {
                totalCount: overview.total_count.toString(),
                countryCount: overview.country_count.toString(),
                songCount: overview.song_count.toString()
            };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}