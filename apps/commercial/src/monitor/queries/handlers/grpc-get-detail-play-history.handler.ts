import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetDetailPlayHistoryQuery } from '../impl';

interface TrackSource {
  title: string;
  artists: Array<{
    name: string;
    type: string;
  }>;
  album: {
    name: string;
  };
  duration: string;
  isrc: string;
}

@QueryHandler(GrpcGetDetailPlayHistoryQuery)
export class GrpcGetDetailPlayHistoryHandler implements IQueryHandler<GrpcGetDetailPlayHistoryQuery> {
  constructor(
    private prisma: PrismaService,
  ) { }

  async execute(query: GrpcGetDetailPlayHistoryQuery) {
    const { query: q } = query;
    const id = q.id;

    try {
      const history = await this.prisma.playHistory.findUnique({
        where: { id },
        include: {
          track: true,
          property: true,
          activation: {
            include: {
              activationDevice: {
                include: {
                  device: true
                }
              }
            }
          }
        }
      });

      if (!history) {
        throw new RpcException('Play history not found');
      }

      const device = history.activation?.activationDevice[0]?.device;
      const trackSource = history.track?.source ? JSON.parse(JSON.stringify(history.track.source)) as TrackSource : null;

      return {
        id: history.id,
        playedTime: history.playAt?.toISOString() || '',
        deviceId: device?.serialNumber || device?.guid || device?.id || '',
        songTitle: trackSource?.title || '',
        artist: trackSource?.artists?.map(a => a.name).join(', ') || '',
        duration: history.duration?.toString() || '0',
        album: trackSource?.album?.name || '',
        deviceType: device?.type || '',
        uptime: device?.updatedAt?.toISOString() || '',
        companyName: history.property?.companyName || '',
        brandName: history.property?.brandName || '',
        zoneName: history.activation?.zone || ''
      };

    } catch (error) {
      throw new RpcException(error);
    }
  }
}