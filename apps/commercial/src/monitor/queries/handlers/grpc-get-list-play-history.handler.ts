import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetListPlayHistoryQuery } from '../impl';
import { convertKeyValuePairs } from '@app/common';
import { Prisma } from '@prisma/client';

interface TrackSource {
  title: string;
  artists: Array<{
    name: string;
    type: string;
  }>;
  album: {
    name: string;
  };
  duration: string;
  isrc: string;
}

@QueryHandler(GrpcGetListPlayHistoryQuery)
export class GrpcGetListPlayHistoryHandler implements IQueryHandler<GrpcGetListPlayHistoryQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GrpcGetListPlayHistoryQuery) {
    const { query: args } = query;
    const params = convertKeyValuePairs(args?.filters || []);
    const page = args?.page || 1;
    const limit = args?.limit || 10;
    const offset = (page - 1) * limit;

    try {
      const searchTerm = params.search?.trim();
      
      const sql = searchTerm 
        ? `
          WITH matched_devices AS (
            SELECT DISTINCT d.id
            FROM "Device" d
            WHERE d."serialNumber" = $1
          ),
          matched_properties AS (
            SELECT DISTINCT p.id
            FROM "Property" p
            WHERE $1 IN (p."brandName", p."companyName")
          ),
          matched_tracks AS (
            SELECT DISTINCT t.id
            FROM "Track" t
            WHERE t.source->>'title' = $1
          ),
          filtered_history AS (
            SELECT 
              ph.id as ph_id,
              ph."playAt",
              ph.duration,
              d.id as d_id,
              d."serialNumber",
              d.guid,
              d.type,
              d."updatedAt",
              t.source,
              p."companyName",
              p."brandName",
              a.zone
            FROM "PlayHistory" ph
            INNER JOIN "ActivationDevice" ad ON ph."activationId" = ad."activationId"
            INNER JOIN "Device" d ON ad."deviceId" = d.id
            LEFT JOIN "Track" t ON ph."trackId" = t.id
            LEFT JOIN "Property" p ON ph."propertyId" = p.id
            LEFT JOIN "Activation" a ON ph."activationId" = a.id
            WHERE 
              d.id IN (SELECT id FROM matched_devices)
              OR p.id IN (SELECT id FROM matched_properties)
              OR t.id IN (SELECT id FROM matched_tracks)
            ORDER BY ph."playAt" DESC
            LIMIT ${limit} OFFSET ${offset}
          )
          SELECT 
            (
              SELECT COUNT(*)
              FROM "PlayHistory" ph
              INNER JOIN "ActivationDevice" ad ON ph."activationId" = ad."activationId"
              INNER JOIN "Device" d ON ad."deviceId" = d.id
              LEFT JOIN "Track" t ON ph."trackId" = t.id
              LEFT JOIN "Property" p ON ph."propertyId" = p.id
              WHERE 
                d.id IN (SELECT id FROM matched_devices)
                OR p.id IN (SELECT id FROM matched_properties)
                OR t.id IN (SELECT id FROM matched_tracks)
            )::integer as total_count,
            COALESCE(
              jsonb_agg(
                jsonb_build_object(
                  'id', ph_id,
                  'playedTime', "playAt",
                  'deviceId', COALESCE("serialNumber", guid, d_id),
                  'songTitle', source->>'title',
                  'artist', (
                    SELECT string_agg(artist->>'name', ', ')
                    FROM jsonb_array_elements(source->'artists') as artist
                  ),
                  'duration', duration,
                  'album', source->'album'->>'name',
                  'deviceType', type,
                  'uptime', "updatedAt",
                  'companyName', "companyName",
                  'brandName', "brandName",
                  'zoneName', zone
                )
              ),
              '[]'
            ) as data
          FROM filtered_history
        `
        : `
          WITH filtered_history AS (
            SELECT 
              ph.id as ph_id,
              ph."playAt",
              ph.duration,
              d.id as d_id,
              d."serialNumber",
              d.guid,
              d.type,
              d."updatedAt",
              t.source,
              p."companyName",
              p."brandName",
              a.zone
            FROM "PlayHistory" ph
            INNER JOIN "ActivationDevice" ad ON ph."activationId" = ad."activationId"
            INNER JOIN "Device" d ON ad."deviceId" = d.id
            LEFT JOIN "Track" t ON ph."trackId" = t.id
            LEFT JOIN "Property" p ON ph."propertyId" = p.id
            LEFT JOIN "Activation" a ON ph."activationId" = a.id
            ORDER BY ph."playAt" DESC
            LIMIT ${limit} OFFSET ${offset}
          )
          SELECT 
            (SELECT COUNT(*) FROM "PlayHistory")::integer as total_count,
            COALESCE(
              jsonb_agg(
                jsonb_build_object(
                  'id', ph_id,
                  'playedTime', "playAt",
                  'deviceId', COALESCE("serialNumber", guid, d_id),
                  'songTitle', source->>'title',
                  'artist', (
                    SELECT string_agg(artist->>'name', ', ')
                    FROM jsonb_array_elements(source->'artists') as artist
                  ),
                  'duration', duration,
                  'album', source->'album'->>'name',
                  'deviceType', type,
                  'uptime', "updatedAt",
                  'companyName', "companyName",
                  'brandName', "brandName",
                  'zoneName', zone
                )
              ),
              '[]'
            ) as data
          FROM filtered_history
        `;

      interface QueryResult {
        data: any[];
        total_count: number;
      }

      const result = await this.prisma.$queryRawUnsafe<QueryResult[]>(sql, searchTerm || '');

      const { data, total_count: bigIntTotal } = result[0] || { data: [], total_count: 0 };
      const total_count = Number(bigIntTotal);


      const lastPage = Math.ceil(total_count / limit);

      return {
        data: data || [],
        meta: {
          page,
          limit,
          lastPage,
          total: total_count
        }
      };

    } catch (error) {
      throw new RpcException(error);
    }
  }
}