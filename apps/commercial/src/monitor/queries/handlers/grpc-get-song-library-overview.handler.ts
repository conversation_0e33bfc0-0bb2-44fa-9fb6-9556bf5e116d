import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetSongLibraryOverviewQuery } from '../impl';

@QueryHandler(GrpcGetSongLibraryOverviewQuery)
export class GrpcGetSongLibraryOverviewHandler implements IQueryHandler<GrpcGetSongLibraryOverviewQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetSongLibraryOverviewQuery) {
        try {
            const result = await this.prisma.$queryRaw<any[]>`
                WITH artist_counts AS (
                    SELECT DISTINCT jsonb_array_elements_text(source->'artists') as artist
                    FROM "Track"
                    WHERE source->>'artists' IS NOT NULL
                ),
                country_counts AS (
                    SELECT DISTINCT SUBSTRING(source->>'isrc', 1, 2) as country_code
                    FROM "Track"
                    WHERE source->>'isrc' IS NOT NULL
                )
                SELECT 
                    (SELECT COUNT(*) FROM "Track") as song_count,
                    (SELECT COUNT(*) FROM country_counts WHERE country_code != '') as country_count,
                    (SELECT COUNT(*) FROM artist_counts WHERE artist != '') as artist_count;
            `;

            const overview = result[0];
            
            return {
                songCount: overview.song_count.toString(),
                countryCount: overview.country_count.toString(),
                artistCount: overview.artist_count.toString()
            };
        } catch (error) {
            throw new RpcException(error);
        }
    }
}