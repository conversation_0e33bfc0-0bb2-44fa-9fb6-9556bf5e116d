import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { GrpcGetListDeviceQuery } from '../impl';
import { convertKeyValuePairs } from '@app/common';

@QueryHandler(GrpcGetListDeviceQuery)
export class GrpcListDeviceHandler implements IQueryHandler<GrpcGetListDeviceQuery> {
    constructor(
        private prisma: PrismaService,
    ) { }

    async execute(query: GrpcGetListDeviceQuery) {
        const { query: args } = query;
        const params = convertKeyValuePairs(args?.filters || []);
        const page = args?.page || 1;
        const limit = args?.limit || 10;
        const offset = (page - 1) * limit;

        try {
            const searchTerm = params.search?.trim();
            
            const sql = searchTerm
                ? `
                WITH matched_devices AS (
                    SELECT DISTINCT d.id
                    FROM "Device" d
                    WHERE d."serialNumber" = $1::text OR d.guid = $1::text
                ),
                matched_properties AS (
                    SELECT DISTINCT p.id
                    FROM "Property" p
                    WHERE p.cid = $1::text OR p."companyName" ILIKE '%' || $1::text || '%'
                ),
                matched_users AS (
                    SELECT DISTINCT u.id
                    FROM "User" u
                    WHERE u.email = $1::text
                ),
                device_data AS (
                    SELECT 
                        d.id,
                        d."serialNumber",
                        d.guid,
                        d.type,
                        d."updatedAt",
                        p.cid,
                        p."companyName",
                        u.username,
                        u.password,
                        u.email,
                        u.status,
                        cp."createdAt" as played_time,
                        CAST(CAST(cp.duration AS BIGINT) AS INTEGER) as duration,
                        t.source as track_source
                    FROM "Device" d
                    LEFT JOIN "ActivationDevice" ad ON d.id = ad."deviceId"
                    LEFT JOIN "Activation" a ON ad."activationId" = a.id
                    LEFT JOIN "Property" p ON a."propertyId" = p.id
                    LEFT JOIN "User" u ON u."activationId" = a.id
                    LEFT JOIN (
                        SELECT DISTINCT ON ("activationId") *
                        FROM "CurrentPlayer"
                        ORDER BY "activationId", "updatedAt" DESC
                    ) cp ON a.id = cp."activationId"
                    LEFT JOIN "Track" t ON cp."trackId" = t.id
                    WHERE 
                        d.id IN (SELECT id FROM matched_devices)
                        OR p.id IN (SELECT id FROM matched_properties)
                        OR u.id IN (SELECT id FROM matched_users)
                    ORDER BY d."createdAt" DESC
                    LIMIT ${limit} OFFSET ${offset}
                ),
                total_count AS (
                    SELECT CAST(CAST(COUNT(DISTINCT d.id) AS BIGINT) AS INTEGER) as total
                    FROM "Device" d
                    LEFT JOIN "ActivationDevice" ad ON d.id = ad."deviceId"
                    LEFT JOIN "Activation" a ON ad."activationId" = a.id
                    LEFT JOIN "Property" p ON a."propertyId" = p.id
                    LEFT JOIN "User" u ON u."activationId" = a.id
                    WHERE 
                        d.id IN (SELECT id FROM matched_devices)
                        OR p.id IN (SELECT id FROM matched_properties)
                        OR u.id IN (SELECT id FROM matched_users)
                )`
                : `
                WITH device_data AS (
                    SELECT 
                        d.id,
                        d."serialNumber",
                        d.guid,
                        d.type,
                        d."updatedAt",
                        p.cid,
                        p."companyName",
                        u.username,
                        u.password,
                        u.email,
                        u.status,
                        cp."createdAt" as played_time,
                        CAST(CAST(cp.duration AS BIGINT) AS INTEGER) as duration,
                        t.source as track_source
                    FROM "Device" d
                    LEFT JOIN "ActivationDevice" ad ON d.id = ad."deviceId"
                    LEFT JOIN "Activation" a ON ad."activationId" = a.id
                    LEFT JOIN "Property" p ON a."propertyId" = p.id
                    LEFT JOIN "User" u ON u."activationId" = a.id
                    LEFT JOIN (
                        SELECT DISTINCT ON ("activationId") *
                        FROM "CurrentPlayer"
                        ORDER BY "activationId", "updatedAt" DESC
                    ) cp ON a.id = cp."activationId"
                    LEFT JOIN "Track" t ON cp."trackId" = t.id
                    ${this.buildWhereClause(params)}
                    ORDER BY d."createdAt" DESC
                    LIMIT ${limit} OFFSET ${offset}
                ),
                total_count AS (
                    SELECT CAST(CAST(COUNT(DISTINCT d.id) AS BIGINT) AS INTEGER) as total
                    FROM "Device" d
                    LEFT JOIN "ActivationDevice" ad ON d.id = ad."deviceId"
                    LEFT JOIN "Activation" a ON ad."activationId" = a.id
                    LEFT JOIN "Property" p ON a."propertyId" = p.id
                    LEFT JOIN "User" u ON u."activationId" = a.id
                    ${this.buildWhereClause(params)}
                )`;

            const commonSelect = `
                SELECT 
                    COALESCE(json_agg(
                        json_build_object(
                            'id', d.id,
                            'cid', d.cid,
                            'companyName', d."companyName",
                            'deviceType', d.type,
                            'deviceId', COALESCE(d."serialNumber", d.guid, d.id),
                            'songTitle', (d.track_source->>'title'),
                            'artist', (
                                SELECT string_agg(artist->>'name', ', ')
                                FROM jsonb_array_elements(d.track_source->'artists') as artist
                            ),
                            'duration', d.duration,
                            'playedTime', d.played_time,
                            'uptime', d."updatedAt",
                            'userName', d.username,
                            'password', d.password,
                            'email', d.email,
                            'statusAccount', d.status
                        )
                    ), '[]') as data,
                    (SELECT total FROM total_count) as total_count
                FROM device_data d;
            `;

            const finalSql = sql + commonSelect;

            interface QueryResult {
                data: any[];
                total_count: number;
            }

            const result = searchTerm 
                ? await this.prisma.$queryRawUnsafe<QueryResult[]>(finalSql, searchTerm)
                : await this.prisma.$queryRawUnsafe<QueryResult[]>(finalSql);

            const { data, total_count: bigIntTotal } = result[0] || { data: [], total_count: 0 };
            const total_count = Number(bigIntTotal);

            const lastPage = Math.ceil(total_count / limit);

            return {
                data: data || [],
                meta: {
                    page,
                    limit,
                    lastPage,
                    total: total_count
                }
            };

        } catch (error) {
            throw new RpcException(error);
        }
    }

    private buildWhereClause(params: any) {
        const conditions = [];
        
        if (params.deviceId) {
            conditions.push(`(d.id = '${params.deviceId}' OR d."serialNumber" = '${params.deviceId}' OR d.guid = '${params.deviceId}')`);
        }

        if (params.type) {
            conditions.push(`d.type = '${params.type}'`);
        }

        if (params.cid) {
            conditions.push(`p.cid = '${params.cid}'`);
        }

        if (params.userName) {
            conditions.push(`u.username ILIKE '%${params.userName}%'`);
        }

        return conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    }
}