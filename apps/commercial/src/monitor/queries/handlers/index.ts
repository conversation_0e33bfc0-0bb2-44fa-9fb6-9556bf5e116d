import { GrpcGetDeviceOverviewHandler } from './grpc-get-device-overview.handler';
import { GrpcGetPlayHistoryOverviewHandler } from './grpc-get-play-history-overview.handler';
import { GrpcGetMusicPlayerOverviewHandler } from './grpc-get-music-player-overview.handler';
import { GrpcGetISRCOverviewHandler } from './grpc-get-isrc-overview.handler';
import { GrpcGetSongLibraryOverviewHandler } from './grpc-get-song-library-overview.handler';
import { GrpcListDeviceHandler } from './grpc-get-list-device.handler';
import { GrpcDetailDeviceHandler } from './grpc-get-detail-device.handler';
import { GrpcGetListPlayHistoryHandler } from './grpc-get-list-play-history.handler';
import { GrpcGetDetailPlayHistoryHandler } from './grpc-get-detail-play-history.handler';
import { GrpcGetDeviceRegionOverviewHandler } from './grpc-get-device-region-overview.handler';

export const MonitorQueryHandlers = [
  GrpcGetDeviceRegionOverviewHandler,
  GrpcGetDeviceOverviewHandler,
  GrpcGetPlayHistoryOverviewHandler,
  GrpcGetMusicPlayerOverviewHandler,
  GrpcGetISRCOverviewHandler,
  GrpcGetSongLibraryOverviewHandler,
  GrpcListDeviceHandler,
  GrpcDetailDeviceHandler,
  GrpcGetListPlayHistoryHandler,
  GrpcGetDetailPlayHistoryHandler
];
