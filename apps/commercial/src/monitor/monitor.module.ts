import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { MonitorQueryHandlers } from './queries';
import { GmiModule } from '../gmi/gmi.module';
import { MonitorRpcController } from './monitor.rpc.controller';

@Module({
  imports: [CqrsModule, GmiModule],
  controllers: [MonitorRpcController],
  providers: [...MonitorQueryHandlers],
})
export class MonitorModule {}
