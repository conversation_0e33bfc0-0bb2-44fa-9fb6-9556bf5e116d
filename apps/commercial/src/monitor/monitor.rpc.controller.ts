import { Empty, Id, QueryMap } from '@app/proto-schema/index.common';
import {
  MONITOR_SERVICE_NAME,
  MusicPlayerOverview,
  PlayHistoryOverview,
  DeviceOverview,
  ISRCOverview,
  SongLibraryOverview,
  ISRCOverviewRequest,
  MonitorServiceController,
  DeviceSummary,
  DeviceSummaryListResponse,
  PlayHistorySummary,
  PlayHistorySummaryResponse,
  DeviceRegionOverviewResponse,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { 
  GrpcGetDeviceOverviewQuery, 
  GrpcGetISRCOverviewQuery, 
  GrpcGetMusicPlayerOverviewQuery, 
  GrpcGetPlayHistoryOverviewQuery, 
  GrpcGetSongLibraryOverviewQuery,
  GrpcGetListDeviceQuery,
  GrpcGetDetailDeviceQuery,
  GrpcGetListPlayHistoryQuery,
  GrpcGetDetailPlayHistoryQuery,
  GrpcGetDeviceRegionOverviewQuery,
} from './queries/impl';

@Controller()
export class MonitorRpcController implements MonitorServiceController {
  constructor(private readonly queryBus: QueryBus) { }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getListDevice')
  getListDevice(
    request: QueryMap,
    metadata: Metadata,
  ): Promise<DeviceSummaryListResponse> | Observable<DeviceSummaryListResponse> | DeviceSummaryListResponse {
    return this.queryBus.execute(new GrpcGetListDeviceQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getDetailDevice')
  getDetailDevice(
    request: Id,
    metadata: Metadata,
  ): Promise<DeviceSummary> | Observable<DeviceSummary> | DeviceSummary {
    return this.queryBus.execute(new GrpcGetDetailDeviceQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getListPlayHistory')
  getListPlayHistory(
    request: QueryMap,
    metadata: Metadata,
  ): Promise<PlayHistorySummaryResponse> | Observable<PlayHistorySummaryResponse> | PlayHistorySummaryResponse {
    return this.queryBus.execute(new GrpcGetListPlayHistoryQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getDetailPlayHistory')
  getDetailPlayHistory(
    request: Id,
    metadata: Metadata,
  ): Promise<PlayHistorySummary> | Observable<PlayHistorySummary> | PlayHistorySummary {
    return this.queryBus.execute(new GrpcGetDetailPlayHistoryQuery(request));
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getMusicPlayerOverview')
  getMusicPlayerOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<MusicPlayerOverview> | Observable<MusicPlayerOverview> | MusicPlayerOverview {
    return this.queryBus.execute(new GrpcGetMusicPlayerOverviewQuery())
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getPlayHistoryOverview')
  getPlayHistoryOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<PlayHistoryOverview> | Observable<PlayHistoryOverview> | PlayHistoryOverview {
    return this.queryBus.execute(new GrpcGetPlayHistoryOverviewQuery())
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getDeviceOverview')
  getDeviceOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<DeviceOverview> | Observable<DeviceOverview> | DeviceOverview {
    return this.queryBus.execute(new GrpcGetDeviceOverviewQuery())
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getIsrcOverview')
  getIsrcOverview(
    request: ISRCOverviewRequest,
    metadata: Metadata,
  ): Promise<ISRCOverview> | Observable<ISRCOverview> | ISRCOverview {
    return this.queryBus.execute(new GrpcGetISRCOverviewQuery(request))
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getSongLibraryOverview')
  getSongLibraryOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<SongLibraryOverview> | Observable<SongLibraryOverview> | SongLibraryOverview {
    return this.queryBus.execute(new GrpcGetSongLibraryOverviewQuery())
  }

  @GrpcMethod(MONITOR_SERVICE_NAME, 'getDeviceRegionOverview')
  getDeviceRegionOverview(
    request: Empty,
    metadata: Metadata,
  ): Promise<DeviceRegionOverviewResponse> | Observable<DeviceRegionOverviewResponse> | DeviceRegionOverviewResponse {
    return this.queryBus.execute(new GrpcGetDeviceRegionOverviewQuery(request));
  }
}
