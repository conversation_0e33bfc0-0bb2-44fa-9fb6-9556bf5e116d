import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListZoneTypesQuery } from '../impl';
import { PrismaService } from 'libs/prisma';
import { Pagination } from '@app/common';
import { Prisma, ZoneType } from '@prisma/client';
import { QueryParamsRpc } from 'apps/commercial/src/auth/strategies/types/user.type';
import { ListZoneTypesResponse } from '@app/proto-schema/index.internal';

@QueryHandler(ListZoneTypesQuery)
export class ListZoneTypesHandler implements IQueryHandler<ListZoneTypesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: ListZoneTypesQuery): Promise<ListZoneTypesResponse> {
    const { params }: QueryParamsRpc = query.args as unknown as QueryParamsRpc;

    const search = params.search || '';
    const paginationResult = await <PERSON>gination<
      ZoneType,
      Prisma.ZoneTypeFindManyArgs
    >(
      this.prisma.zoneType,
      {
        where: {
          name: {
            contains: search,
            mode: 'insensitive',
          },
        },
        select: {
          id: true,
          name: true,
        },
      },
      {
        page: params.page || 1,
        limit: params.limit || 10,
      },
    );

    return {
      data: paginationResult.data.map((type) => ({
        id: type.id,
        name: type.name,
      })),
      meta: paginationResult.meta,
    };
  }
}
