import { Query } from '@app/proto-schema/index.common';
import {
  ListZoneTypesResponse,
  ZONE_TYPE_SERVICE_NAME,
  ZoneTypeServiceController,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import { ListZoneTypesQuery } from './queries/impl';

@Controller()
export class ZoneTypeRpcController implements ZoneTypeServiceController {
  constructor(private readonly queryBus: QueryBus) {}

  @GrpcMethod(ZONE_TYPE_SERVICE_NAME, 'listZoneTypes')
  listZoneTypes(
    request: Query,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListZoneTypesResponse>
    | Observable<ListZoneTypesResponse>
    | ListZoneTypesResponse {
    return this.queryBus.execute(new ListZoneTypesQuery(request));
  }
}
