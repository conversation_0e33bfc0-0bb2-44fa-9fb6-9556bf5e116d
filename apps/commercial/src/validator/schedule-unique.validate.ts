import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from '../../../../libs/prisma';

export type itemChecker = {
  id: string;
};

@ValidatorConstraint({ async: true })
export class IsScheduleUniqueConstraint
  implements ValidatorConstraintInterface
{
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const { ignore = false } = args.constraints[0];
    const context = args.object['context'];
    // const times = `${value}:00`;
    const user = context['user'];
    const time = new Date(value).toISOString().substr(11, 8);

    const modeValue = args.object['modeValue'];
    const deviceId = args.object['deviceIds'];
    const item = await this.prisma.$queryRaw<
      itemChecker[]
    >`
      SELECT
        "s"."id"
      FROM "public"."Schedule" AS "s"
      JOIN "public"."ScheduleZone" AS "sz"
        ON s.id = sz."scheduleId"
      WHERE (CAST("s"."startTime" AS timestamp))::time <= (CAST(${time} AS timestamp))::time
        AND (CAST("s"."endTime" AS timestamp))::time > (CAST(${time} AS timestamp))::time
        AND "sz"."activationId" = ANY (${deviceId}::text[])
        AND "s"."modeValue" @> ${modeValue}::text[]
      LIMIT 1 OFFSET 0
    `;
    const firstValue = item[0];

    if (!firstValue && item.length < 1) {
      return true;
    } else {
      if (ignore && firstValue['id'] == context.params['id']) {
        return true;
      } else {
        return false;
      }
    }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} already have a schedule at the same time`;
  }
}

export function IsScheduleUnique(
  property: IsScheduleUniqueProperty,
  validationOptions?: ValidationOptions,
) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsScheduleUnique',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsScheduleUniqueConstraint,
    });
  };
}

export type IsScheduleUniqueProperty = {
  ignore?: boolean;
};
