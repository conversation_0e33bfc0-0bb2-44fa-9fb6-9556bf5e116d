import { BadRequestException } from '@nestjs/common';
import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from '../../../../libs/prisma';

@ValidatorConstraint({ async: true })
export class IsTimeLessThenConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {

    if (
      value === undefined ||
      args.object['startTime'] === undefined ||
      typeof value !== 'string' ||
      typeof args.object['startTime'] !== 'string'
    ) {
      throw new BadRequestException('payload must string or cannot be empty');
    }
    
    const object = args.object as any;
    return new Date(value) < new Date(object.endTime);

    // const inTime = value.split(':');
    // const startTime = new Date().setHours(
    //   Number(inTime[0]),
    //   Number(inTime[1]),
    //   0,
    // );
    // const inEndTime = `${args.object['endTime']}`.split(':');
    // const endTime = new Date().setHours(
    //   Number(inEndTime[0]),
    //   Number(inEndTime[1]),
    //   0,
    // );
    // if (startTime >= endTime) {
    //   return false;
    // } else {
    //   return true;
    // }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} must be less then end time`;
  }
}

export function IsTimeLessThen(validationOptions?: ValidationOptions) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsTimeLessThen',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: IsTimeLessThenConstraint,
    });
  };
}
