import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from '../../../../libs/prisma';

export type itemChecker = {
  id: string;
};

@ValidatorConstraint({ async: true })
export class AddOnChildExistConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}

  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const duration = args.object['parent']['duration'];
    const industry = args.object['parent']['propertyTypeId'];
    if (!duration || !industry) {
      return false;
    }
    const validate = await this.prisma.addOn.findFirst({
      where: {
        AND: [
          { id: value },
          { type: 'custom' },
          { duration: duration },
          { propertyTypeId: industry },
        ],
      },
    });
    if (validate) {
      return true;
    }
    return false;
  }

  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} not exist`;
  }
}

export function AddOnChildExist(validationOptions?: ValidationOptions) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'AddOnChildExist',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: AddOnChildExistConstraint,
    });
  };
}
