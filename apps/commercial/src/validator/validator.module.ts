import { <PERSON>du<PERSON> } from '@nestjs/common';
import { ValidatorProviders } from '@validator/validator';
import { AddOnChildExistConstraint } from './addon-child-exist.validator';
import { AddOnExistConstraint } from './addon-exist.validator';
import { IsScheduleUniqueConstraint } from './schedule-unique.validate';
import { IsRunningTextScheduleUniqueConstraint } from './running-text-schedule-unique.validate';
import { LogValidator } from './logging.validator';

@Module({
  providers: [
    ...ValidatorProviders,
    IsScheduleUniqueConstraint,
    AddOnExistConstraint,
    AddOnChildExistConstraint,
    IsRunningTextScheduleUniqueConstraint,
    LogValidator
  ]
})
export class ValidatorModule {}
