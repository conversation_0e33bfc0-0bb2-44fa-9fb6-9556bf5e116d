import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from '../../../../libs/prisma';

export type itemChecker = {
  id: string;
};

@ValidatorConstraint({ async: true })
export class IsRunningTextScheduleUniqueConstraint
  implements ValidatorConstraintInterface
{
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const { ignore = false } = args.constraints[0];
    const context = args.object['context'];
    // const times = `${value}:00`;
    const user = context['user'];

    const item = await this.prisma.$queryRaw<
      itemChecker[]
    >`SELECT "s"."id" FROM "public"."RunningText" AS "s" WHERE (CAST("s"."startTime" AS timestamp))::time < (CAST(${value} AS timestamp))::time AND (CAST("s"."endTime" AS timestamp))::time > (CAST(${value} AS timestamp))::time AND "s"."propertyId" = ${user['propertyId']} LIMIT 1 OFFSET 0`;
    const firstValue = item[0];

    if (!firstValue && item.length < 1) {
      return true;
    } else {
      if (ignore && firstValue['id'] == context.params['id']) {
        return true;
      } else {
        return false;
      }
    }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} already have a schedule at the same time`;
  }
}

export function IsRunningTextScheduleUnique(
  property: IsRunningTextScheduleUniqueProperty,
  validationOptions?: ValidationOptions,
) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsRunningTextScheduleUnique',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [property],
      options: validationOptions,
      validator: IsRunningTextScheduleUniqueConstraint,
    });
  };
}

export type IsRunningTextScheduleUniqueProperty = {
  ignore?: boolean;
};
