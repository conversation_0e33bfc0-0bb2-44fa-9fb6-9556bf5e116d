import {
  ValidationArguments,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  registerDecorator,
} from 'class-validator';
import { PrismaService } from '../../../../libs/prisma';

@ValidatorConstraint({ async: true })
export class IsTimeMoreThenConstraint implements ValidatorConstraintInterface {
  constructor(private prisma: PrismaService) {}
  async validate(value: any, args?: ValidationArguments): Promise<boolean> {
    const object = args.object as any;
    return new Date(value) > new Date(object.startTime);
    // const inTime = `${args.object['startTime']}`.split(':');
    // const startTime = new Date().setHours(
    //   Number(inTime[0]),
    //   Number(inTime[1]),
    //   0,
    // );
    // const inEndTime = value.split(':');
    // const endTime = new Date().setHours(
    //   Number(inEndTime[0]),
    //   Number(inEndTime[1]),
    //   0,
    // );
    // if (startTime >= endTime) {
    //   return false;
    // } else {
    //   return true;
    // }
  }
  defaultMessage?(args?: ValidationArguments): string {
    return `${args.property} more then start time`;
  }
}

export function IsTimeMoreThen(validationOptions?: ValidationOptions) {
  return (object: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsTimeMoreThen',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [],
      options: validationOptions,
      validator: IsTimeMoreThenConstraint,
    });
  };
}
