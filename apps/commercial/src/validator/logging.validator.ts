import { Call<PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from '@nestjs/common';
import { ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { map, Observable } from 'rxjs';

@ValidatorConstraint({ name: 'logValidator', async: false })
export class LogValidator implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    console.log(`Validating property ${args.property}...`);
    console.log('Value:', value);
    console.log('Data type:', typeof value);
    console.log('Constraints:', args.constraints);
    return true;
  }

  defaultMessage(args: ValidationArguments) {
    return 'An error occurred during validation.';
  }
}

export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    console.log('Request body before:', request.body);

    return next.handle().pipe(
      map(data => {
        console.log('Response data:', data);
        return data;
      }),
    );
  }
}