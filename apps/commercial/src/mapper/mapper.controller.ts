import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { FilterTracksMapperDto } from './dto/filter-tracks-mapper.dto';
import { GetTracksMapperQuery } from './queries';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('mapper')
@ApiTags('Mapper')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class MapperController {
  constructor(private queryBus: QueryBus) {}

  @Get('playlist-track')
  getPlaylistTracks(@Query() filter: FilterTracksMapperDto) {
    return this.queryBus.execute(new GetTracksMapperQuery(filter));
  }
}
