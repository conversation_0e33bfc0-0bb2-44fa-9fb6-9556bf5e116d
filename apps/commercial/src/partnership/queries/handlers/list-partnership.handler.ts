import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListPartnershipQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { Pagination } from '@app/common';
import { Partnership, Prisma } from '@prisma/client';

@QueryHandler(ListPartnershipQuery)
export class ListPartnershipHandler
  implements IQueryHandler<ListPartnershipQuery>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: ListPartnershipQuery) {
    const { args } = query;
    const search = args.query || '';

    const paginatedResult = await Pagination<
      Partnership,
      Prisma.PartnershipFindManyArgs
    >(
      this.prisma.partnership,
      {
        where: {
          AND: [
            {
              OR: [
                { name: { contains: search, mode: 'insensitive' } },
                { popularName: { contains: search, mode: 'insensitive' } },
              ],
            },
          ],
        },
        include: { ReferralCode: true, media: true },
      },
      { page: args.params.page, limit: args.params.limit },
    );

    return paginatedResult;
  }
}
