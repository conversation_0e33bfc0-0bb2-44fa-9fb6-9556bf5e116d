import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPartnershipPropertiesQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { Pagination } from '@app/common';
import { Property, Prisma } from '@prisma/client';
import { ListPartnershipPropertiesResponse } from '@app/proto-schema/index.internal';

@QueryHandler(GetPartnershipPropertiesQuery)
export class GetPartnershipPropertiesHandler
  implements IQueryHandler<GetPartnershipPropertiesQuery>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(
    query: GetPartnershipPropertiesQuery,
  ): Promise<ListPartnershipPropertiesResponse> {
    const { args } = query;
    const search = args.query?.query || '';

    // console.log('INI ARGS ', args.query.params);
    const crmPropertyIds = await this.prisma.propertyPartnership.findMany({
      where: {
        partnershipId: args.partnershipId,
      },
      select: {
        crmPropertyId: true,
      },
    });

    // console.log(crmPropertyIds);

    const crmPropertyIdValues = crmPropertyIds.map(
      (item) => item.crmPropertyId,
    );

    const paginatedResult = await Pagination<
      Property,
      Prisma.PropertyFindManyArgs
    >(
      this.prisma.property,
      {
        where: {
          crmPropertyId: {
            in: crmPropertyIdValues,
          },
          AND: [
            {
              OR: [
                { companyName: { contains: search, mode: 'insensitive' } },
                { brandName: { contains: search, mode: 'insensitive' } },
              ],
            },
          ],
        },
      },
      { page: args.query?.params?.page, limit: args.query?.params?.limit },
    );

    return {
      data: paginatedResult.data.map((property) => ({
        id: property.id,
        cid: property.cid,
        companyName: property.companyName,
        brandName: property.brandName,
        companyEmail: property.companyEmail,
        companyPhoneNumber: property.companyPhoneNumber,
        npwp: property.npwp,
        address: property.address,
        createdAt: property.createdAt.toISOString(),
        updatedAt: property.updatedAt.toISOString(),
        postalId: property.postalId,
        propertyTypeId: property.propertyTypeId,
        configuration: null,
        contactPerson: null,
      })),
      meta: paginatedResult.meta,
    };
  }
}
