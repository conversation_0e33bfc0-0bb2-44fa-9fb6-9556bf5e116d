import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPartnershipQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetPartnershipQuery)
export class GetPartnershipHandler
  implements IQueryHandler<GetPartnershipQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPartnershipQuery) {
    const { id } = query;
    const item = await this.prisma.partnership.findFirst({
      where: { id: id },
    });

    if (!item) {
      throw new NotFoundException();
    }

    return item;
  }
}
