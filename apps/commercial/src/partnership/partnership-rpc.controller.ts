import { Grpc<PERSON>ethod } from '@nestjs/microservices';
import {
  ListPartnershipPropertiesRequest,
  ListPartnershipPropertiesResponse,
  ListPartnershipResponse,
  Partnership,
  PARTNERSHIP_SERVICE_NAME,
} from '@app/proto-schema/internal-proto/partnership';
import { Query as CommonQuery, Id } from '@app/proto-schema/index.common';
import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { QueryBus } from '@nestjs/cqrs';
import {
  GetPartnershipPropertiesQuery,
  GetPartnershipQuery,
  ListPartnershipQuery,
} from './queries';
import { Controller } from '@nestjs/common';
import { Property } from '@app/proto-schema/index.membership';

@Controller('partnership')
export class PartnershipRPCController {
  constructor(private queryBus: QueryBus) {}
  @GrpcMethod(PARTNERSHIP_SERVICE_NAME, 'listPartnership')
  listPartnership(
    request: CommonQuery,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListPartnershipResponse>
    | Observable<ListPartnershipResponse>
    | ListPartnershipResponse {
    return this.queryBus.execute(new ListPartnershipQuery(request));
  }

  @GrpcMethod(PARTNERSHIP_SERVICE_NAME, 'getPartnership')
  getPartnership(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Partnership> | Observable<Partnership> | Partnership {
    return this.queryBus.execute(new GetPartnershipQuery(request.id));
  }

  @GrpcMethod(PARTNERSHIP_SERVICE_NAME, 'listPartnershipProperties')
  listPartnershipProperties(
    request: ListPartnershipPropertiesRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListPartnershipPropertiesResponse>
    | Observable<ListPartnershipPropertiesResponse>
    | ListPartnershipPropertiesResponse {
    return this.queryBus.execute(new GetPartnershipPropertiesQuery(request));
  }
}
