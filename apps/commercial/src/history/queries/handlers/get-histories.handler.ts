import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetHistoriesQuery } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { partitionArray } from 'libs/utils/partition-array.util';

@QueryHandler(GetHistoriesQuery)
export class GetHistoriesHandler implements IQueryHandler<GetHistoriesQuery> {
  constructor(
    private prisma: PrismaService,
    private gmiRestsService: GmiRESTService,
  ) {}

  async execute(query: GetHistoriesQuery) {
    const { user, args } = query;
    const search = args.search || '';
    const limit = Number(args?.take || 50);
    const sortType = args?.sortType || 'DESC';
    const cursor = args?.cursor; // format: ISO string of playAt time or custom
  
    const startDate = new Date();
    startDate.setMonth(0);
    startDate.setDate(1);
    startDate.setHours(0, 0, 0, 0);
    const startDateISO = startDate.toISOString();
  
    let whereQuery = `
      where "ph"."propertyId"='${user.propertyId}'
      AND "ph"."activationId"='${user.deviceId}'
    `;
  
    if (user.isAdmin) {
      whereQuery = `
        where "ph"."propertyId"='${user.propertyId}'
      `;
    }
  
    const activationCondition = !user.isAdmin
      ? `AND "py"."activationId"='${user.deviceId}'`
      : '';
  
    const cursorCondition = cursor
      ? `AND (
           (select "py"."playAt"
            from "PlayHistory" AS "py"
            where "py"."trackId"="sPh"."trackId"
            AND "py"."propertyId"='${user.propertyId}'
            ${activationCondition}
            ORDER BY "py"."playAt" DESC
            limit 1 offset 0
           ) ${sortType === 'DESC' ? '<' : '>'} '${cursor}'
         )`
      : '';
  
    let items = (await this.prisma.$queryRawUnsafe(
      `
      select
        "sPh"."trackId",
        "tr"."key",
        "sPh"."count",
        "tr"."source" as "track",
        (
          select
            "py"."playAt"
          from "PlayHistory" AS "py"
          where "py"."trackId"="sPh"."trackId"
          AND "py"."propertyId"='${user.propertyId}'
          ${activationCondition}
          ORDER BY "py"."playAt" DESC
          limit 1
          offset 0
        ) as jam
      from 
        (
          select
            "ph"."trackId",
            count("ph"."trackId")
            from "PlayHistory" as "ph"
            ${whereQuery}
            AND "ph"."playAt" IS NOT NULL
            group by "ph"."trackId"
        ) as "sPh"
      Inner Join "Track" as "tr"
      ON "tr"."id" = "sPh"."trackId"
      Inner Join "PlayHistory" as "eph"
      ON "eph"."trackId" = "sPh"."trackId"
      WHERE
      (
        "tr"."source"->>'title' ILIKE '%${search}%'
        OR ("tr"."source"->>'album')::json->>'name' ILIKE '%${search}%'
        AND "eph"."propertyId"='${user.propertyId}'
        AND "eph"."playAt" >= '${startDateISO}'
      )
      ${cursorCondition}
      GROUP BY "eph"."trackId", "sPh"."trackId", "tr"."key", "sPh"."count"
      ORDER BY "jam" ${sortType}
      LIMIT ${limit + 1}
    `)) as any[];
  
    const hasNextPage = items.length > limit;
    if (hasNextPage) items.pop(); // remove extra item
  
    const nextCursor = hasNextPage ? items[items.length - 1].jam : null;
  
    // Track Like Mapping
    if (items.length > 0) {
      const trackIds = items.map((tr) => tr.trackId as string);
      const trackLike = await this.prisma.trackLike.findMany({
        where: {
          trackId: { in: trackIds },
          propertyId: user.propertyId,
          activationId: user.deviceId,
        },
      });
  
      items = items.map((trck) => {
        trck.isLike = !!trackLike.find((tl) => tl.trackId === trck.trackId);
        if (!trck.track?.uri) {
          trck.track.uri = `track:${trck.key}`;
        }
        return trck;
      });
    }
  
    return {
      data: items,
      meta: {
        nextCursor,
        hasNextPage,
        limit,
        sortType,
      },
    };
  }
}
