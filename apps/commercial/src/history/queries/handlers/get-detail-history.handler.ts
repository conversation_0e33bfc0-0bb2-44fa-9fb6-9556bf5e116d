import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import { PlayHistoryWithTrack } from '../../types';
import { GetDetailHistoryQuery } from '../impl';

@QueryHandler(GetDetailHistoryQuery)
export class GetDetailHistoryHandler
  implements IQueryHandler<GetDetailHistoryQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetDetailHistoryQuery) {
    const { user, trackId } = query;

    const trackIdsFromKeys = await this.prisma.track.findFirst({
      where: {
        key: trackId,
      },
      select: { id: true },
    });

    const items = await Pagination<any, Prisma.PlayHistoryFindManyArgs>(
      this.prisma.playHistory,
      {
        where: {
          AND: [
            { property: { id: user.propertyId } },
            { trackId: trackIdsFromKeys?.id },
            { playAt: { not: null } },
            user.isAdmin ? {} : { activationId: user.deviceId },
          ],
        },
        include: {
          track: { select: { source: true } },
          activation: {
            include: {
              activationDevice: {
                orderBy: {
                  updatedAt: 'desc',
                },
                include: {
                  device: true,
                },
              },
            },
          },
        },
        orderBy: { playAt: 'desc' },
      },
      { limit: 0, page: 0 },
    );

    const serializeData = items.data.map((item) => {
      return {
        playAt: item.playAt,
        endAt: item.endAt,
        track: item?.track?.source,
        device: {
          id: item.id,
          serialNumber: item.activation.activationDevice[0].device.serialNumber,
          type: item.activation.activationDevice[0].device.type,
          name: item.activation.activationDevice[0].device.name,
          zone: item.activation.zone,
        },
      };
    });

    return Object.assign(items, { data: serializeData });
  }
}
