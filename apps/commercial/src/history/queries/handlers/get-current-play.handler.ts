import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetCurrentPlayQuery } from '../impl';

@QueryHandler(GetCurrentPlayQuery)
export class GetCurrentPlayHandler
  implements IQueryHandler<GetCurrentPlayQuery>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetCurrentPlayQuery) {
    const { user } = query;
    const currentPlay = await this.prisma.playHistory.findFirst({
      where: { AND: [{ property: { id: user.propertyId } }, { endAt: null }, { activation: { id: user.deviceId }}] },
      include: { track: true },
      orderBy: { playAt: 'desc' },
    });

    return currentPlay;
  }
}
