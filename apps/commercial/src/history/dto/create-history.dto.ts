import { SpotifyTrackDto } from '@app/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
  MaxLength,
} from 'class-validator';

export class CreateHistoryDto {
  @ApiProperty()
  @IsNotEmpty()
  @MaxLength(30)
  @IsString()
  key: string;
  @IsObject()
  @ApiProperty()
  source: any;   //dibuat any karena sementara, ambil source dari GMIrest
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  macAddress: string;
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  isp: string;
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  geo: string;
}
