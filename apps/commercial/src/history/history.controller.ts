import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { CreateHistoryCommand, PausedHistoryCommand } from './commands';
import { CreateHistoryDto } from './dto/create-history.dto';
import { FilterHistoryDto } from './dto/filter-history.dto';
import { PausedHistoryDto } from './dto/paused-history.dto';
import {
  GetCurrentPlayQuery,
  GetDetailHistoryQuery,
  GetHistoriesQuery,
} from './queries';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('history')
@ApiTags('History')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class HistoryController {
  constructor(
    private commandBus: CommandBus,
    private queryBus: QueryBus,
  ) {}
  // @Post()
  // @ApiOperation({ summary: 'create history or play count' })
  // create(
  //   @User() user: ICurrentUser,
  //   @Body() createHistoryDto: CreateHistoryDto,
  //   @Req() req: Request,
  // ) {
  //   return this.commandBus.execute(
  //     new CreateHistoryCommand(user, createHistoryDto, req),
  //   );
  // }

  @Get()
  @ApiOperation({ summary: 'get all histories be played' })
  //@UseInterceptors(CacheInterceptor)
  getAll(@User() user: ICurrentUser, @Query() filter: FilterHistoryDto) {
    return this.queryBus.execute(new GetHistoriesQuery(user, filter));
  }

  // @Get('current')
  // @ApiOperation({ summary: 'get current play' })
  // getCurrentPlay(@User() user: ICurrentUser) {
  //   return this.queryBus.execute(new GetCurrentPlayQuery(user));
  // }

  // @Patch('current')
  // @ApiOperation({ summary: 'paused current play track' })
  // pausedCurrent(
  //   @User() user: ICurrentUser,
  //   @Body() pausedHistoryDto: PausedHistoryDto,
  // ) {
  //   return this.commandBus.execute(
  //     new PausedHistoryCommand(user, pausedHistoryDto),
  //   );
  //   // return this.queryBus.execute(new GetCurrentPlayQuery(user));
  // }
  @Get('/detail/:trackId')
  @ApiOperation({ summary: 'get detail history' })
  getDetailHistory(
    @Param('trackId') trackId: string,
    @User() user: ICurrentUser,
    @Query() filter: FilterHistoryDto,
  ) {
    return this.queryBus.execute(
      new GetDetailHistoryQuery(user, trackId, filter),
    );
  }
}
