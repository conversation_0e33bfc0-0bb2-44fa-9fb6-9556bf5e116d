import { <PERSON><PERSON><PERSON>lish<PERSON>, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { CreateTrackHistoryEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { IntegrationModel } from 'apps/commercial/src/integration/model/integration.model';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { ElasticsearchService } from '@nestjs/elasticsearch';

@EventsHandler(CreateTrackHistoryEvent)
export class CreateTrackHistoryHandler implements IEventHandler<CreateTrackHistoryEvent> {
  constructor(
    private prisma: PrismaService,
    private readonly publisher: EventPublisher,
    private readonly gmiService: GmiRESTService,
    private readonly elasticService: ElasticsearchService
  ) {}

  async handle(event: CreateTrackHistoryEvent) {
    const { args } = event;
    try {
      const track = await this.prisma.track.findFirst({
        where: {
          id: args.trackId
        },
        select: {
          id: true,
          count: true,
          key: true
        }
      });

      if (args.flag === 'DEMO' || args.flag === 'PAID' || args.flag === 'TEST' || args.flag  === 'TRIAL') {
        const existing = await this.prisma.playHistory.findFirst({
          where: {
            AND: [
              {
                propertyId: args.propertyId
              },
              {
                activationId: args.deviceId
              },
              {
                trackId: track.id
              },
              {
                endAt: null
              }
            ]
          }
        });

        if (!existing) {
          await this.prisma.playHistory.updateMany({
            where: {
              AND: [{ property: { id: args.propertyId } }, { endAt: null }, { activation: { id: args.deviceId } }],
            },
            data: { endAt: new Date().toISOString() },
          });

          await this.prisma.playHistory.create({
            data: {
              duration: args.duration,
              durationType: args.durationType,
              playAt: new Date().toISOString(),
              property: {
                connect: { id: args.propertyId },
              },
              activation: {
                connect: { id: args.deviceId }
              },
              track: {
                connect: { id: track.id },
              },
              ip: args.ip,
              userAgent: args.userAgent,
              os: args.os,
              macAddress: args.macAddress,
              isp: args.isp,
              geo: args.geo,
              country: args?.country,
              region: args?.region,
              timeZone: args?.timeZone,
              city: args?.city,
              zip: args?.zip,
              playerType: args.playerType,
              flag: args.flag
            },
          });
  
          await this.prisma.track.update({
            where: {
              id: track.id
            },
            data: {
              count: Number(track.count)+1
            }
          });

          if (args.flag === 'PAID' || args.flag === 'TRIAL' || args.flag === 'DEMO' || args.flag === 'TEST') {
            const limiterModel = this.publisher.mergeClassContext(LimiterModel);
            const limiter = new limiterModel();
            limiter.decreaseFeatureQty({
              id: FEATURE.SongQuota,
              propertyId: args.propertyId,
              deviceId: args.deviceId
            });
    
            if (args.flag === 'PAID' || args.flag === 'DEMO' || args.flag === 'TRIAL') {
              const integrationModel = this.publisher.mergeClassContext(IntegrationModel)
              const integration = new integrationModel();
              integration.rmsStoreHistory({
                propertyId: args.propertyId,
                trackId: track.id,
                flag: args.flag,
                deviceId: args.deviceId,
                country: args?.country,
                os: args?.os,
                ip: args?.ip,
                playDuration: args.duration
              });
    
              // ** UPDATE DATA TO ELASTIC **
              // * TODO : Enable it when deploy at production *
    
              // update track playCount
              //await this.elasticService.updateByQuery({
              //  index: 'gmi.tracks',
              //  body: {
              //    script: {
              //      source: `ctx._source['playCount'] = params.value`,
              //      params: { value: Number(track.count)+1 },
              //    },
              //    query: {
              //      match: { ['id']: track.key },
              //    },
              //  },
              //});
              //
              //// update artist popularity
              //if (track['artists']) {
              //  const artistIds: string[] = track['artists'].map((art) => art.id);
              //  if (artistIds.length > 0) {
              //    const response = await this.elasticService.search({
              //      index: 'gmi.artists',
              //      body: {
              //        query: {
              //          terms: {
              //            userId: artistIds, // WHERE userId IN (123, 456, 789)
              //          },
              //        },
              //      },
              //    });
              //
              //    const artists = response.hits.hits;
              //    for (let i of artists) {
              //      // update artist popularity
              //      await this.elasticService.updateByQuery({
              //        index: 'gmi.artists',
              //        body: {
              //          script: {
              //            source: `ctx._source['popularity'] = params.value`,
              //            params: { value: Number(i["popularity"])+1 },
              //          },
              //          query: {
              //            match: { ['id']: i["id"] },
              //          },
              //        },
              //      });
              //    }
              //  }
              //}
              // ** === END OF UPDATE DATA TO ELASTIC === **
            }
          }      
        }
      }
    } catch (error) {
      console.log(error)
      console.log('failed create history');
    }
  }
}
