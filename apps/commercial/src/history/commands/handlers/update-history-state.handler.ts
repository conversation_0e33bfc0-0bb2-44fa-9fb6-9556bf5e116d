import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateHistoryStateCommand } from '../impl';

@CommandHandler(UpdateHistoryStateCommand)
export class UpdateHistoryStateHandler
  implements ICommandHandler<UpdateHistoryStateCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: UpdateHistoryStateCommand) {
    const { id } = command;
    try {
      await this.prisma.playHistory.update({
        where: { id: id },
        data: { endAt: new Date().toDateString() },
      });
      return 'successfully update state history';
    } catch (error) {
      return 'failed update state history';
    }
  }
}
