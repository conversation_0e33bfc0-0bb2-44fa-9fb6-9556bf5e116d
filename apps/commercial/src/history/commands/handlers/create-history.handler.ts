import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import { UAParser } from 'ua-parser-js';
import { CreateHistoryCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';

@CommandHandler(CreateHistoryCommand)
export class CreateHistoryHandler
  implements ICommandHandler<CreateHistoryCommand>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestService: GmiRESTService
  ) {}

  async execute(command: CreateHistoryCommand) {
    const { user, args, req } = command;
    const header = req.headers;
    const ua = UAParser(header['user-agent']);
    try {
      await this.prisma.$transaction(async (tr) => {
        await tr.playHistory.updateMany({
          where: {
            AND: [{ property: { id: user.propertyId } }, { endAt: null }, { activationId: user.deviceId }],
          },
          data: { endAt: new Date().toISOString() },
        });

        let gmiTrack = args.source;
        if (!gmiTrack) { 
          gmiTrack = await this.gmiRestService.melodivaUse(args.key); 
        }

        return await tr.playHistory.create({
          data: {
            activation: {
              connect: {
                id: user.deviceId
              }
            },
            duration: 0,
            playAt: new Date().toISOString(),
            property: {
              connect: { id: user.propertyId },
            },
            track: {
              connectOrCreate: {
                where: { key: args.key },
                create: {
                  key: args.key,
                  source: gmiTrack as unknown as Prisma.JsonValue,
                  count: +1
                },
              },
            },
            ip: header['x-forwarded-for']
              ? (header['x-forwarded-for'] as string)
              : undefined,
            userAgent: header['user-agent']
              ? (header['user-agent'] as string)
              : undefined,
            os: ua.os.name,
            macAddress: args.macAddress,
            isp: args.isp,
            geo: args.geo,
          },
        });
      });

      return 'successfully created history';
    } catch (error) {
      return 'failed created history';
    }
  }
}
