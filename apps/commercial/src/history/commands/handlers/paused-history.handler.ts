import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { PausedHistoryCommand } from '../impl';

@CommandHandler(PausedHistoryCommand)
export class PausedH<PERSON>oryHandler
  implements ICommandHandler<PausedHistoryCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: PausedHistoryCommand) {
    const { user, args } = command;
    const item = await this.prisma.playHistory.findFirst({
      where: {
        AND: [{ property: { id: user.propertyId } }, { endAt: null }, { activation: { id: user.deviceId }}],
      },
      orderBy: { playAt: 'desc' },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      // await this.prisma.playHistory.update({
        // where: { id: item.id },
        // data: { paused: args.paused },
      // });
      return 'successfully paused current play track';
    } catch (error) {
      return 'failed paused current play track';
    }
  }
}
