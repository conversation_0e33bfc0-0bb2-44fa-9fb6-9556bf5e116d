import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { Request } from 'express';
import { CreateHistoryDto } from '../../dto/create-history.dto';

export class CreateHistoryCommand implements ICommand {
  constructor(
    public readonly user: ICurrentUser,
    public readonly args: CreateHistoryDto,
    public readonly req: Request,
  ) {}
}
