import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { HistoryCommandHandlers } from './commands';
import { HistoryController } from './history.controller';
import { HistoryQueryHandlers } from './queries';
import { GmiModule } from '../gmi/gmi.module';
import { HistoryEventHandlers } from './events';
import { LimiterModule } from '../limiter/limiter.module';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Keyv from 'keyv';
import { CacheableMemory } from 'cacheable';
import { createKeyv } from '@keyv/redis';
import { ElasticsearchModule } from '@nestjs/elasticsearch';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    // LimiterModule,
    //CacheModule.registerAsync({
    //  imports: [ConfigModule],
    //  useFactory: (configService: ConfigService) => ({
    //    store: redisStore as unknown as CacheStoreFactory,
    //    host: configService.get<string>('REDIS_HOST'),
    //    port: configService.get<number>('REDIS_PORT'),
    //    password: configService.get<string>('REDIS_PASS'),
    //    ttl: configService.get<number>('REDIS_TTL') || 5,
    //  }),
    //  inject: [ConfigService],
    //}),
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        node: config.get<string>('ELASTIC_NODE'),
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      useFactory: async (config: ConfigService) => {
        return {
          stores: [
            new Keyv({
              store: new CacheableMemory({ ttl: 60000, lruSize: 5000 }),
            }),
            createKeyv({
              url: 'redis://default:Jaguar123@*************:6379'
            }),
          ],
        };
      },
    }),
  ],
  controllers: [HistoryController],
  providers: [
    ...HistoryCommandHandlers,
    ...HistoryQueryHandlers,
    ...HistoryEventHandlers,
  ],
})
export class HistoryModule {}
