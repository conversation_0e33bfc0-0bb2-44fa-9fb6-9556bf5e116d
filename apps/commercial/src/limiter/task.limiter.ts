import { Injectable, Logger } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'libs/prisma';
import { DeactivatePackageCommand } from './commands/impl';

@Injectable()
export class TaskLimiter {
  private readonly logger = new Logger(TaskLimiter.name);
  constructor(
    private prisma: PrismaService,
    private commandBus: CommandBus,
  ) {}

  @Cron(CronExpression.EVERY_MINUTE)
  async handleTask() {
    const pkgs = await this.prisma.package.findMany({
      where: {
        OR: [
          {
            expiredAt: {
              lt: Math.floor(Date.now())
            }
          },
          {
            contractEndAt: {
              lt: Math.floor(Date.now())
            }
          }
        ]
      },
      select: {
        id: true
      }
    });

    if (pkgs.length > 0) {
      await this.commandBus.execute(
        new DeactivatePackageCommand(pkgs.map((pkg) => pkg.id)),
      );
    }
  }
}
