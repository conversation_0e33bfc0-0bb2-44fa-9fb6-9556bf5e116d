import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { IncreaseFeatureQtyEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@EventsHandler(IncreaseFeatureQtyEvent)
export class IncreaseFeatureHandler implements IEventHandler<IncreaseFeatureQtyEvent> {
  constructor(
    private prisma: PrismaService,
  ) {}

  async handle(event: IncreaseFeatureQtyEvent) {
    const { args } = event;
    try {
      const pkg = await this.prisma.packageFeature.findFirst({
        where: {
          AND: [
            {
              feature: {
                AND: [
                  {
                    id: args.id
                  },
                  {
                    valueType: 'number'
                  }
                ]
              }
            },
            { 
              package: {
                AND: [
                  { isActive: true },
                  {
                    expiredAt: { 
                      gt: Date.now()
                    }
                  },
                  {
                    propertyId: args.propertyId
                  }
                ]
              }
            }
          ]
        }
      });
      if (!pkg) {
        console.log('PACKAGE NOT FOUND!!');
        return;
      }

      if (pkg.qouta > 0) {
        if (
          pkg.featureId == FEATURE.SongQuota ||
          pkg.featureId == FEATURE.Schedule ||
          pkg.featureId == FEATURE.Playlist
        ) {
          const pkgFeat = await this.prisma.zoneFeature.findFirst({
            where: {
              AND: [
                {
                  activation: {
                    id: args.deviceId
                  }
                },
                {
                  feature: {
                    AND: [
                      {
                        id: pkg.featureId
                      },
                      {
                        valueType: 'number'
                      }
                    ]
                  }
                },
                {
                  package: {
                    id: pkg.packageId
                  }
                }
              ]
            }
          });
          if (pkgFeat && pkgFeat?.qouta !== -1) {
            await this.prisma.zoneFeature.update({
              where: {
                id: pkgFeat.id
              },
              data: {
                qouta: pkgFeat.qouta+1
              }
            });
          }
        } else {
          await this.prisma.packageFeature.update({
            where: {
              id: pkg.id
            },
            data: {
              qouta: pkg.qouta+1
            }
          });
        }
      }
    } catch (error) {
      console.log('failed decrease qty feature');
    }
  }
}
