import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { DecreaseFeatureQtyEvent } from '../impl';
import { PrismaService } from 'libs/prisma';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@EventsHandler(DecreaseFeatureQtyEvent)
export class DecreaseFeatureHandler implements IEventHandler<DecreaseFeatureQtyEvent> {
  constructor(
    private prisma: PrismaService,
  ) {}

  async handle(event: DecreaseFeatureQtyEvent) {
    const { args } = event;
    try {
      const pkg = await this.prisma.packageFeature.findFirst({
        where: {
          AND: [
            {
              feature: {
                AND: [
                  {
                    valueType: 'number'
                  },
                  {
                    id: args.id
                  }
                ]
              }
            },
            { 
              package: {
                AND: [
                  { isActive: true },
                  {
                    expiredAt: { 
                      gt: Date.now()
                    }
                  },
                  {
                    propertyId: args.propertyId
                  }
                ]
              }
            }
          ]
        }
      });
      if (!pkg) {
        console.log('PACKAGE NOT FOUND!!');
        return;
      }

      if (pkg.qouta > 0) {
        if (
          pkg.featureId == FEATURE.SongQuota ||
          pkg.featureId == FEATURE.Schedule ||
          pkg.featureId == FEATURE.Playlist
        ) {
          const pkgFeat = await this.prisma.zoneFeature.findFirst({
            where: {
              AND: [
                {
                  activation: {
                    id: args.deviceId
                  }
                },
                {
                  feature: {
                    AND: [
                      {
                        id: pkg.featureId
                      },
                      {
                        valueType: 'number'
                      }
                    ]
                  }
                },
                {
                  package: {
                    id: pkg.packageId
                  }
                }
              ]
            }
          });
          if (pkgFeat && pkgFeat?.qouta !== -1) {
            await this.prisma.zoneFeature.update({
              where: {
                id: pkgFeat.id
              },
              data: {
                qouta: pkgFeat.qouta-1
              }
            });
          }
        } else {
          await this.prisma.packageFeature.update({
            where: {
              id: pkg.id
            },
            data: {
              qouta: pkg.qouta-1
            }
          });
        }
      }
    } catch (error) {
      console.log('failed decrease qty feature');
    }
  }
}
