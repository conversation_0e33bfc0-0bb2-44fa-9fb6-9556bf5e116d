import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { DeactivatePackageCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { MembershipModel } from 'apps/commercial/src/membership/models/membership.model';

@CommandHandler(DeactivatePackageCommand)
export class DeactivatePackageHandler
  implements ICommandHandler<DeactivatePackageCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: DeactivatePackageCommand) {
    const { args } = command;

    // First, fetch packages to check their current isActive status
    const packages = await this.prisma.package.findMany({
      where: {
        id: {
          in: args,
        },
      },
      include: {
        property: true,
      },
    });

    // Filter packages that are currently active (isActive: true)
    const activePackages = packages.filter(pkg => pkg.isActive === true);

    // Only update packages that are currently active
    if (activePackages.length > 0) {
      const activePackageIds = activePackages.map(pkg => pkg.id);

      await this.prisma.package.updateMany({
        where: {
          id: {
            in: activePackageIds,
          },
        },
        data: {
          status: 'suspend',
          isActive: false,
        },
      });

      // Get unique property IDs from deactivated packages
      const propertyIds = [...new Set(activePackages.map(pkg => pkg.property.id))];

      // Check each property to see if it still has any active packages
      const propertiesToSuspend = [];

      for (const propertyId of propertyIds) {
        // Check if this property still has any active packages after the deactivation
        const remainingActivePackages = await this.prisma.package.count({
          where: {
            propertyId: propertyId,
            isActive: true,
          },
        });

        // Only suspend property if no active packages remain
        if (remainingActivePackages === 0) {
          propertiesToSuspend.push(propertyId);
        }
      }

      // Only suspend properties that have no remaining active packages
      if (propertiesToSuspend.length > 0) {
        const membershipModel = this.publisher.mergeClassContext(MembershipModel);
        const membership = new membershipModel();
        membership.suspendMembership(propertiesToSuspend);
      }
    }

    return;
  }
}
