import { AggregateRoot } from '@nestjs/cqrs';
import { DecreaseFeatureQtyEvent, IncreaseFeatureQtyEvent } from '../events';

export class LimiterModel extends AggregateRoot {
  constructor() {
    super();
    this.autoCommit = true;
  }

  decreaseFeatureQty(data: any) {
    this.apply(new DecreaseFeatureQtyEvent(data));
  }

  increaseFeatureQty(data: any) {
    this.apply(new IncreaseFeatureQtyEvent(data));
  }
}
