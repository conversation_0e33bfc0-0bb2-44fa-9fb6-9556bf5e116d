import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListProductsQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { ConfigService } from '@nestjs/config';

@QueryHandler(ListProductsQuery)
export class ListProductsHandler implements IQueryHandler<ListProductsQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}
  async execute(query: ListProductsQuery) {
    // console.log(
    //   'ListPlanHandler - Input Query:',
    //   JSON.stringify(query, null, 2),
    // );
    const { industryId, subfolderId, businessType } = query;

    const whereConditions: {
      AND: Array<
        | { propertyType?: { id: string } }
        | { subfolder?: { id: string } }
        | { publish: boolean }
        | { businessType?: string }
        | { deletedAt: null }
      >;
    } = {
      AND: [{ publish: true }, { deletedAt: null }],
    };

    if (subfolderId) {
      whereConditions.AND.push({ subfolder: { id: subfolderId } });
    }

    if (businessType) {
      whereConditions.AND.push({ businessType });
    }

    const products = await this.prisma.product.findMany({
      where: whereConditions,
      include: {
        propertyType: { select: { id: true, name: true, slug: true } },
        subfolder: { select: { id: true, name: true, slug: true } },
        sku: true,
        media: true,
        taxProduct: { include: { tax: true } },
        variants: {
          where: {
            deletedAt: null,
          },
        },
      },
      orderBy: { sequence: 'asc' },
    });

    const productMap = products.map((product) => ({
      id: product.id,
      name: product.name,
      price: Number(product.price.toFixed()),
      description: product.description,
      publish: product.publish,
      propertyTypeId: product.propertyType?.id || '',
      subfolderId: product.subfolder?.id || '',
      variants: product.variants,
      taxes: product.taxProduct.map((tax) => ({
        id: tax.tax.id,
        name: tax.tax.name,
        nominal: Number(tax.tax.nominal),
        description: tax.tax.description,
        type: tax.tax.type,
        startDate: String(tax.tax.startDate),
        endDate: String(tax.tax.endDate),
      })),
      sku: product.sku
        ? {
            id: product.sku.id,
            code: product.sku.code,
            productType: product.sku.productType,
          }
        : null,
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
      isMultiple: product.isMultiple,
      image: product.media
        ? `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${product.media.path}/${product.media.fileName}`
        : null,
    }));

    // console.log(
    //   'ListPlanHandler - Response:',
    //   JSON.stringify(
    //     {
    //       totalPlans: productMap.length,
    //       businessType,
    //       subfolderId,
    //     },
    //     null,
    //     2,
    //   ),
    // );

    return {
      status: { code: 200, message: 'Success' },
      data: productMap,
    };
  }
}
