import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GrpcGetProductQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GrpcGetProductQuery)
export class GrpcGetProductHandler
  implements IQueryHandler<GrpcGetProductQuery>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GrpcGetProductQuery) {
    const { id } = query;

    const product = await this.prisma.product.findFirst({
      where: { id, deletedAt: null },
      include: {
        propertyType: { select: { id: true, name: true } },
        subfolder: { select: { id: true, name: true } },
        taxProduct: {
          include: {
            tax: true,
          },
        },
        media: true,
        sku: true,
        variants: {
          where: {
            deletedAt: null,
          },
        },
      },
    });

    if (!product) {
      throw new NotFoundException('Product not found');
    }

    return {
      id: product.id,
      name: product.name,
      price: Number(product.price.toFixed()),
      description: product.description,
      publish: product.publish,
      propertyTypeId: product.propertyType?.id || '',
      subfolderId: product.subfolder?.id || '',
      taxes: product.taxProduct.map((tax) => ({
        id: tax.tax.id,
        name: tax.tax.name,
        nominal: Number(tax.tax.nominal),
        description: tax.tax.description,
        type: tax.tax.type,
        startDate: String(tax.tax.startDate),
        endDate: String(tax.tax.endDate),
      })),
      sku: product.sku
        ? {
            id: product.sku.id,
            code: product.sku.code,
            productType: product.sku.productType,
          }
        : null,
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
      isMultiple: product.isMultiple,
      image: product.media
        ? `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${product.media.path}/${product.media.fileName}`
        : null,
    };
  }
}
