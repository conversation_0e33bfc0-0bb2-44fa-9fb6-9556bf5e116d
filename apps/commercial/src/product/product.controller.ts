import { ApiTags } from '@nestjs/swagger';
import {
  Controller,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import {
  ListProductResponse,
  Product,
  PRODUCT_SERVICE_NAME,
  ProductRequest,
} from '@app/proto-schema/internal-proto/product';
import { Id } from '@app/proto-schema/common-proto/common';
import { Metadata, status } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { GrpcGetProductQuery, ListProductsQuery } from './queries';

@ApiTags('Product')
@Controller('product')
export class ProductController {
  constructor(private queryBus: QueryBus) {}

  @GrpcMethod(PRODUCT_SERVICE_NAME, 'listProduct')
  listProduct(
    request: ProductRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListProductResponse>
    | Observable<ListProductResponse>
    | ListProductResponse {
    // console.log('ListProduct');
    return this.queryBus.execute(
      new ListProductsQuery(
        request.industryId,
        request.subfolderId,
        request.businessType,
      ),
    );
  }

  @GrpcMethod(PRODUCT_SERVICE_NAME, 'detailProduct')
  detailProduct(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Product> | Observable<Product> | Product {
    try {
      return this.queryBus.execute(new GrpcGetProductQuery(request.id));
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('product not found');
      } else {
        throw new InternalServerErrorException('internal server error');
      }
    }
  }
}
