import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateMembershipCommand } from '../impl';

@CommandHandler(UpdateMembershipCommand)
export class UpdateMembershipHandler
  implements ICommandHandler<UpdateMembershipCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateMembershipCommand) {
    const { id } = command;
    return id;
  }
}
