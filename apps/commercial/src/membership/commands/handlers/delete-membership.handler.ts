import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeleteMembershipCommand } from '../impl';

@CommandHandler(DeleteMembershipCommand)
export class DeleteMembershipHandler
  implements ICommandHandler<DeleteMembershipCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteMembershipCommand) {
    const { id } = command;
    return id;
  }
}
