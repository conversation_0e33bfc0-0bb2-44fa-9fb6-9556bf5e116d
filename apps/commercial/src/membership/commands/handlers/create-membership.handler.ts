import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CreateMembershipCommand } from '../impl';

@CommandHandler(CreateMembershipCommand)
export class CreateMembershipHandler
  implements ICommandHandler<CreateMembershipCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateMembershipCommand) {
    const { args } = command;
    return args;
  }
}
