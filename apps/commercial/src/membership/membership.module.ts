import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { MembershipController } from './membership.controller';
import { MembershipQueryHandlers } from './queries';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { MembershipEventHandlers } from './events/handlers';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    CqrsModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  providers: [
    ...MembershipQueryHandlers,
    ...MembershipEventHandlers
  ],
})
export class MembershipModule {}
