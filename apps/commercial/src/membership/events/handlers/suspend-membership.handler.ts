import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { SuspendMembershipEvent } from '../impl';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@EventsHandler(SuspendMembershipEvent)
export class SuspendMembershipHandler
  implements IEventHandler<SuspendMembershipEvent>
{
  constructor(
    private prisma: PrismaService,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async handle(event: SuspendMembershipEvent) {
    const { args } = event;
    try {
      await this.prisma.property.updateMany({
        where: {
          id: {
            in: args,
          },
        },
        data: {
          status: 'suspend',
        },
      });

      this.client.emit(
        'device-suspend',
        args.map((prop) => {
          return {
            propertyId: prop,
          };
        }),
      );

      //const integrationModel = this.publisher.mergeClassContext(IntegrationModel)
      //const integration = new integrationModel();
      //integration.rmsSuspendCustomer(
      //  args.map((prop) => {
      //    return {
      //      id: prop
      //    }
      //  })
      //);

      // TODO : send suspend notification to the web
      // const notificationModel = this.publisher.mergeClassContext(NotificationModel);
      // const hookModel = new notificationModel();
      // hookModel.sendWebNotification({
      //   to: property.userId,
      //   event: 'notification',
      //   data: {
      //     message: `order successfully paid`,
      //     type: 'order_success',
      //     data: {
      //       orderId: args.id
      //     }
      //   }
      // });
    } catch (error) {
      console.log('failed suspend property');
    }
  }
}
