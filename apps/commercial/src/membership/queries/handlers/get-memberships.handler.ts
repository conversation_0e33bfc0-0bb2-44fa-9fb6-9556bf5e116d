import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetMembershipsQuery } from '../impl';

@QueryHandler(GetMembershipsQuery)
export class GetMembershipsHandler
  implements IQueryHandler<GetMembershipsQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetMembershipsQuery) {
    const { user, args } = query;

    //const orders = await this.prisma.order.findMany({
    //  where: {
    //    AND: [{ propertyId: user.propertyId }],
    //  },
    //  include: {
    //    plan: {
    //      include: {
    //        propertyType: true,
    //        addons: {
    //          include: {
    //            feature: true,
    //          },
    //        },
    //      },
    //    },
    //    details: {
    //      include: {
    //        feature: true,
    //      },
    //    },
    //    payment: true,
    //    property: {
    //      include: {
    //        propertyType: true,
    //      },
    //    },
    //  },
    //  orderBy: {
    //    createdAt: 'desc',
    //  },
    //});

    return '';
  }
}
