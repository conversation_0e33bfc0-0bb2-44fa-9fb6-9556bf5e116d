import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetMembershipQuery } from '../impl';

@QueryHandler(GetMembershipQuery)
export class GetMembershipHandler implements IQueryHandler<GetMembershipQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetMembershipQuery) {
    const { user, id } = query;

    //const item = await this.prisma.order.findFirst({
    //  where: {
    //    AND: [
    //      { id },
    //      { propertyId: user.propertyId }
    //    ],
    //  },
    //  include: {
    //    plan: {
    //      include: {
    //        propertyType: true,
    //        addons: {
    //          include: {
    //            feature: true
    //          }
    //        }
    //      }
    //    },
    //    details: {
    //      include: {
    //        feature: true
    //      }
    //    },
    //    payment: true,
    //    property: {
    //      include: {
    //        propertyType: true
    //      }
    //    }
    //  }
    //});

    return '';
  }
}