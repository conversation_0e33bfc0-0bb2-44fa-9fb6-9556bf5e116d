import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { FilterMembershipDto } from './dto/filter-membership.dto';
import { GetMembershipQuery, GetMembershipsQuery } from './queries';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@ApiTags('Membership')
@Controller('membership')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class MembershipController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterMembershipDto) {
    return this.queryBus.execute(new GetMembershipsQuery(user, filter));
  }

  @Get(':id')
  findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.queryBus.execute(new GetMembershipQuery(user, id));
  }
}
