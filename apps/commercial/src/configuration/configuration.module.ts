import { Module } from '@nestjs/common';
import { ConfigurationController } from './configuration.controller';
import { ConfigurationQueryHandlers } from './queries';
import { ConfigurationCommandHandlers } from './commands';
import { CqrsModule } from '@nestjs/cqrs';
import { ConfigurationRpcController } from './configuration.rpc.controller';
import { MembershipClientModule } from '../membership-client/membership-client.module';

@Module({
  imports: [CqrsModule],
  controllers: [ConfigurationController, ConfigurationRpcController],
  providers: [...ConfigurationQueryHandlers, ...ConfigurationCommandHandlers],
})
export class ConfigurationModule {}
