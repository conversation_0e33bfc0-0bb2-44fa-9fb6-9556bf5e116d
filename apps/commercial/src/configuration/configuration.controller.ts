import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CreateConfigurationDto } from './dto/create-configuration.dto';
import { UpdateConfigurationDto } from './dto/update-configuration.dto';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  CreateConfigurationCommand,
  DeleteConfigurationCommand,
  UpdateConfigurationCommand,
} from './commands';
import { FilterConfigurationDto } from './dto/filter-configuration.dto';
import { GetConfigurationsQuery } from './queries';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { User } from '../auth/decorator/user.decorator';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@ApiTags('Configuration')
@Controller('configuration')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class ConfigurationController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @Post()
  create(
    @Body() createConfigurationDto: CreateConfigurationDto,
    @User() user: any,
  ) {
    const { sub } = user;
    return this.commandBus.execute(
      new CreateConfigurationCommand(createConfigurationDto, sub),
    );
  }

  @Get()
  findAll(@Query() filter: FilterConfigurationDto) {
    return this.queryBus.execute(new GetConfigurationsQuery(filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetConfigurationsQuery(id));
  }

  @Throttle({ default: { limit: 10, ttl: 60000 } })
  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateConfigurationDto: UpdateConfigurationDto,
  ) {
    return this.commandBus.execute(
      new UpdateConfigurationCommand(id, updateConfigurationDto),
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.commandBus.execute(new DeleteConfigurationCommand(id));
  }
}
