import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetConfigurationQuery } from '../impl';

@QueryHandler(GetConfigurationQuery)
export class GetConfigurationHandler
  implements IQueryHandler<GetConfigurationQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetConfigurationQuery) {
    const { id } = query;
    const item = await this.prisma.configuration.findFirst({
      where: { id: id },
    });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
