import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { NotFoundException } from '@nestjs/common';
import { GetConfigAppVersionQuery } from '../impl';

@QueryHandler(GetConfigAppVersionQuery)
export class GetConfigAppVersionHandler implements IQueryHandler<GetConfigAppVersionQuery> {
  constructor(private prisma: PrismaService) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async execute(query: GetConfigAppVersionQuery) {
    const item = await this.prisma.setting.findFirst({
      where: { type: 'app-version' },
    });

    if (!item) {
      throw new NotFoundException();
    }

    return item.option;
  }
}
