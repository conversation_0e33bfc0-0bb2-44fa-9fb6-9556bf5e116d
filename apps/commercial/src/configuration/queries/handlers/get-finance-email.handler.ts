import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { NotFoundException } from '@nestjs/common';
import { GetFinanceEmailQuery } from '../impl';
import { status } from '@grpc/grpc-js';
import { GetBccEmailsResponse } from '@app/proto-schema/index.internal';

@QueryHandler(GetFinanceEmailQuery)
export class GetFinanceEmailHandler
  implements IQueryHandler<GetFinanceEmailQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetFinanceEmailQuery) {
    const item = await this.prisma.setting.findFirst({
      where: { type: 'finance-email' },
    });

    if (!item) {
      throw new NotFoundException();
    }
    const emails = (item.option as { bcc: string[] })?.bcc || [];

    const response: GetBccEmailsResponse = {
      emails,
    };

    return response;
  }
}
