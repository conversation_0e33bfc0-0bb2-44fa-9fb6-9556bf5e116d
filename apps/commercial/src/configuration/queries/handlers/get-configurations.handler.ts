import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON>ueryH<PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetConfigurationsQuery } from '../impl';

@QueryHandler(GetConfigurationsQuery)
export class GetConfigurationsHandler
  implements IQueryHandler<GetConfigurationsQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetConfigurationsQuery) {
    const items = await this.prisma.configuration.findMany();
    return items;
  }
}
