import {
  CONFIGURATION_SERVICE_NAME,
  ConfigurationServiceController,
  GetAppVersionResponse,
  GetBccEmailsResponse,
} from '@app/proto-schema/index.internal';
import { Controller } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { GetConfigAppVersionQuery, GetFinanceEmailQuery } from './queries';
import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { Empty } from '@app/proto-schema/index.common';

@Controller()
export class ConfigurationRpcController
  implements ConfigurationServiceController
{
  constructor(
    private readonly queryBus: QueryBus,
  ) {}
  @GrpcMethod(CONFIGURATION_SERVICE_NAME, 'getAppVersion')
  getAppVersion(
    request: Empty,
    metadata: Metadata,
    ...rest: any  ): Promise<GetAppVersionResponse>
    | Observable<GetAppVersionResponse>
    | GetAppVersionResponse 
  {
    return this.queryBus.execute(new GetConfigAppVersionQuery());
  }

  @GrpcMethod(CONFIGURATION_SERVICE_NAME, 'getBccEmails')
  getBccEmails(
    request: Empty,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetBccEmailsResponse>
    | Observable<GetBccEmailsResponse>
    | GetBccEmailsResponse {
    return this.queryBus.execute(new GetFinanceEmailQuery(request));
  }
}
