import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdateConfigurationCommand } from '../impl';

@CommandHandler(UpdateConfigurationCommand)
export class UpdateConfigurationHandler
  implements ICommandHandler<UpdateConfigurationCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdateConfigurationCommand) {
    const { id, args } = command;
    return await this.prisma.configuration.update({
      where: { id: id },
      data: {
        options: args.options,
      },
    });
  }
}
