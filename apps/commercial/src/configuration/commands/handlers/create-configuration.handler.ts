import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CreateConfigurationCommand } from '../impl';

@CommandHandler(CreateConfigurationCommand)
export class CreateConfigurationHandler
  implements ICommandHandler<CreateConfigurationCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: CreateConfigurationCommand) {
    const { args, sub } = command;

    // const userOnBussines = await this.prisma.userOnProperty.findFirst({
    //   where: { userId: sub },
    // });

    // return await this.prisma.configuration.create({
    //   data: {
    //     options: args.options,
    //     propertyId: userOnBussines.propertyId,
    //   },
    // });
  }
}
