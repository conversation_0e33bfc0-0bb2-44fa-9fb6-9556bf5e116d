import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeleteConfigurationCommand } from '../impl';

@CommandHandler(DeleteConfigurationCommand)
export class DeleteConfigurationHandler
  implements ICommandHandler<DeleteConfigurationCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: DeleteConfigurationCommand) {
    const { id } = command;

    return this.prisma.configuration.delete({ where: { id: id } });
  }
}
