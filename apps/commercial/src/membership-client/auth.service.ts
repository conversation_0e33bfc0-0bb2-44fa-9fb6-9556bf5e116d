import { MEMBERSHIP_PACKAGE } from '@app/common';
import { AuthServiceClient, PROPERTY_SERVICE_NAME } from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';

@Injectable()
export class AuthService implements OnModuleInit {
  private authClient: AuthServiceClient;

  constructor(@Inject(MEMBERSHIP_PACKAGE) private authClientRpc: ClientGrpc) {}

  activateProperty(userId: string) {
    const meta = new Metadata();
    return this.authClient.getUserById({ userId }, meta);
  }

  onModuleInit() {
    this.authClient =
      this.authClientRpc.getService<AuthServiceClient>(PROPERTY_SERVICE_NAME);
  }
}
