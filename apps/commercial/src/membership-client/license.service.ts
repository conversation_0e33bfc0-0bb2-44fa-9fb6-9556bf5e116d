import { MEMBERSHIP_PACKAGE } from '@app/common';
import { LICENSE_SERVICE_NAME, LicenseServiceClient } from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';

@Injectable()
export class LicenseService implements OnModuleInit {
  private licenseClient: LicenseServiceClient;

  constructor(@Inject(MEMBERSHIP_PACKAGE) private licenseClientRpc: ClientGrpc) {}

  setPerformingLicense(propertyId: string, key: string, type: string, requestId: string) {
    const meta = new Metadata();
    return this.licenseClient.setPerformingLicense({ id: propertyId, key, type, requestId }, meta);
  }

  onModuleInit() {
    this.licenseClient =
      this.licenseClientRpc.getService<LicenseServiceClient>(LICENSE_SERVICE_NAME);
  }
}
