import { MEMBERSHIP_PACKAGE } from '@app/common';
import { UserServiceClient, USER_SERVICE_NAME, UserResponse } from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { tap } from 'rxjs/operators';
import { Observable } from 'rxjs';

@Injectable()
export class UserService implements OnModuleInit {
    private userClient: UserServiceClient;

    constructor(@Inject(MEMBERSHIP_PACKAGE) private userClientRpc: ClientGrpc) { }

    getUserById(id: string): Observable<UserResponse> {
        const meta = new Metadata();
        try {
            const response = this.userClient.getUserById({
                id
            }, meta);
            
            return response.pipe(
                tap((data: UserResponse) => {
                    if (!data?.user?.id) {
                        throw new Error('Invalid response format');
                    }
                })
            );
        } catch (error) {
            console.error('\x1b[31m%s\x1b[0m', '[gRPC Error]', error);
            throw error;
        }
    }

    getUserAndPropertyById(userId: string, propertyId: string): Observable<UserResponse> {
        const meta = new Metadata();
        try {
            const response = this.userClient.getUserByIdPropertyId({
                userId,
                propertyId
            }, meta);
            
            return response.pipe(
                tap((data: UserResponse) => {
                    if (!data?.user?.id) {
                        throw new Error('Invalid response format');
                    }
                })
            );
        } catch (error) {
            console.error('\x1b[31m%s\x1b[0m', '[gRPC Error]', error);
            throw error;
        }
    }

    onModuleInit() {
        this.userClient = this.userClientRpc.getService<UserServiceClient>(USER_SERVICE_NAME);
    }
}
