import { MEMBERSHIP_PACKAGE } from '@app/common';
import {
  GetAllOrderResponse,
  GetListAllOrderResponse,
  ORDER_SERVICE_NAME,
  OrderServiceClient,
  RenewalType,
} from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { Observable } from 'rxjs';

@Injectable()
export class OrderService implements OnModuleInit {
  private orderClient: OrderServiceClient;

  constructor(@Inject(MEMBERSHIP_PACKAGE) private orderClientRpc: ClientGrpc) {}

  checkPayment(paymentRequestId: string) {
    const meta = new Metadata();
    return this.orderClient.checkPayment({ paymentRequestId }, meta);
  }

  renewOrder(
    propertyId: string,
    itemType: string,
    itemId: string,
    renewalType: RenewalType,
  ) {
    const meta = new Metadata();
    return this.orderClient.renewOrder(
      { propertyId, itemType, itemId, renewalType },
      meta,
    );
  }

  getOrderDetail(orderId: string) {
    const meta = new Metadata();
    return this.orderClient.getOrderDetail({ id: orderId }, meta);
  }

  paidOrder(
    orderId: string,
    paymentMethod: string,
    paymentRequestId: string,
    paidAt: string,
    paymentId: string,
  ) {
    const meta = new Metadata();
    return this.orderClient.paidOrder(
      { orderId, paymentMethod, paymentRequestId, paidAt, paymentId },
      meta,
    );
  }

  updateWaitingOrder(
    id: string,
    isPaid: boolean,
    paymentRequestId: string,
    url: string,
    expired: string,
  ) {
    const meta = new Metadata();
    return this.orderClient.updateWaitingOrder(
      { id, isPaid, paymentRequestId, url, expired },
      meta,
    );
  }

  getDetailAllOrder(id: string): Observable<GetAllOrderResponse> {
    const meta = new Metadata();
    return this.orderClient.getDetailAllOrder({ id }, meta);
  }

  trialOrder(propertyId: string, itemType: string, itemId: string) {
    const meta = new Metadata();
    return this.orderClient.trialOrder(
      {
        propertyId: propertyId,
        itemType: itemType,
        itemId: itemId,
      },

      meta,
    );
  }
  onModuleInit() {
    this.orderClient =
      this.orderClientRpc.getService<OrderServiceClient>(ORDER_SERVICE_NAME);
  }
}
