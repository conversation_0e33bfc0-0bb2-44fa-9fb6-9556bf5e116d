import { Modu<PERSON> } from '@nestjs/common';
import { ClientsModule } from '@nestjs/microservices';
import { MEMBERSHIP_PACKAGE, MembershipClient } from '@app/common';
import { OrderService } from './order.service';
import { AuthService } from './auth.service';
import { PropertyService } from './property.service';
import { UserService } from './user.service';
import { ConfigService } from '@nestjs/config';
import { LicenseService } from './license.service';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => MembershipClient(configService),
        name: MEMBERSHIP_PACKAGE
      }
    ])
  ],
  providers: [
    UserService,
    AuthService,
    OrderService,
    PropertyService,
    LicenseService
  ],
  exports: [
    UserService,
    AuthService,
    OrderService,
    PropertyService,
    LicenseService
  ]
})
export class MembershipClientModule { }
