import { MEMBERSHIP_PACKAGE } from '@app/common';
import { PROPERTY_SERVICE_NAME, PropertyServiceClient } from '@app/proto-schema/index.membership';
import { Metadata } from '@grpc/grpc-js';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';

@Injectable()
export class PropertyService implements OnModuleInit {
  private propertyClient: PropertyServiceClient;

  constructor(@Inject(MEMBERSHIP_PACKAGE) private propertyClientRpc: ClientGrpc) {}

  activateProperty(propertyId: string) {
    const meta = new Metadata();
    return this.propertyClient.activateProperty({ propertyId }, meta);
  }

  getPropertyUser(propertyId: string) {
    const meta = new Metadata();
    return this.propertyClient.getPropertyUser({ propertyId }, meta);
  }

  suspendProperty(propertyId: string) {
    const meta = new Metadata();
    return this.propertyClient.suspendProperty({ propertyId }, meta);
  }

  setPropertyFlag(propertyId: string, flag: string) {
    const meta = new Metadata();
    return this.propertyClient.setPropertyFlag({ id: propertyId, flag }, meta);
  }

  onModuleInit() {
    this.propertyClient =
      this.propertyClientRpc.getService<PropertyServiceClient>(PROPERTY_SERVICE_NAME);
  }
}
