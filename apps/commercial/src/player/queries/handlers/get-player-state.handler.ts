import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPlayerStateQuery } from '../impl';

@QueryHandler(GetPlayerStateQuery)
export class GetPlayerStateHandler implements IQueryHandler<GetPlayerStateQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPlayerStateQuery) {
    const { user } = query;

    if (!user.deviceId) {
      return {}
    }

    const activation = await this.prisma.activation.findFirst({
      where: {
        id: user.deviceId
      },
      include: {
        config: true,
        activationDevice: {
          include: {
            device: true
          },
          orderBy: {
            updatedAt: 'desc'
          }
        },
      }
    });

    const item = await this.prisma.currentPlayer.findFirst({
      where: {
        AND: [
          {
            activation: {
              id: user.deviceId
            }
          },
          {
            property: {
              id: user.propertyId
            }
          }
        ]
      },
      orderBy: {
        updatedAt: 'desc'
      },
      include: {
        track: true,
      }
    });

    let trackUri = null;
    if (item) {
      if (item?.track) {
        if (typeof item.track.source === 'object' && item.track.source !== null) { 
          trackUri = { 
            ...item.track.source, 
            uri: `queue:${(item.track.source as { id: string }).id}`
          }
        }
      }
    }
    activation.activationDevice[0].device.id = user.deviceId;
    return {
      device: activation.activationDevice[0].device,
      current: {
        loop: activation.config?.player['loop'] !== undefined ? activation.config?.player['loop'] : 0,
        shuffle: activation.config?.player['shuffle'] !== undefined ? activation.config?.player['shuffle'] : false,
        duration: item?.duration,
        volume: activation.config?.player['volume'] !== undefined ? activation.config?.player['volume'] : 0.3,
        durationType: item?.durationType,
        paused: item?.paused ? item.paused : true,
        track: trackUri
      }
    }
  }
}
