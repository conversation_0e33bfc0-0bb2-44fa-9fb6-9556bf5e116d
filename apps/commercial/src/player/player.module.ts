import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { PlayerController } from './player.controller';
import { PlayerCommandHandlers } from './commands';
import { GmiModule } from '../gmi/gmi.module';
import { PlayerQueryHandlers } from './queries';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventsModule } from '../events/events.module';
import { PlayerEventHandlers } from './events/handlers';
import { JinggleModule } from '../jinggle/jinggle.module';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    JinggleModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
    EventsModule,
  ],
  controllers: [PlayerController],
  providers: [
    ...PlayerCommandHandlers,
    ...PlayerQueryHandlers,
    ...PlayerEventHandlers,
  ],
})
export class PlayerModule {}
