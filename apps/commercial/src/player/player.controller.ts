import {
  Body,
  Controller,
  Get,
  Ip,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  LoopCommand,
  MutedCommand,
  NextQueueCommand,
  PauseQueueCommand,
  PlayQueueCommand,
  PrevQueueCommand,
  SeekQueueCommand,
  ShuffleQueueCommand,
  VolumeCommand,
} from './commands';
import { SeekQueueDto } from './dto/seek-queue.dto';
import { ShuffleQueueDto } from './dto/shuffle-queue.dto';
import { MutedDto } from './dto/muted.dto';
import { LoopDto } from './dto/loop.dto';
import { GetPlayerStateQuery } from './queries';
import { PlayDto } from './dto/play.dto';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('player')
@ApiTags('Player')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class PlayerController {
  constructor(
    private readonly commandBus: CommandBus,
    private queryBus: QueryBus,
  ) {}

  @Post('next')
  async nextQueue(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() payload: PlayDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new NextQueueCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        payload,
      ),
    );
  }

  @Post('shuffle')
  async shuffle(
    @User() user: ICurrentUser,
    @Body() body: ShuffleQueueDto,
    @Req() req: Request,
    @Ip() ip: string,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new ShuffleQueueCommand(
        user,
        body,
        clientIp,
        req.headers['user-agent'],
      ),
    );
  }

  @Post('prev')
  async prev(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() payload: PlayDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new PrevQueueCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        payload,
      ),
    );
  }

  @Post('seek')
  async seek(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() paylod: SeekQueueDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new SeekQueueCommand(
        user,
        paylod,
        clientIp,
        req.headers['user-agent'],
      ),
    );
  }

  @Post('muted')
  async muted(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() paylod: MutedDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new MutedCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        paylod,
      ),
    );
  }

  @Post('loop')
  async loop(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() paylod: LoopDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new LoopCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        paylod,
      ),
    );
  }

  @Post('volume')
  async volume(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() paylod: LoopDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new VolumeCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        paylod,
      ),
    );
  }

  @Post('pause')
  async pause(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() payload: PlayDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new PauseQueueCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        payload,
      ),
    );
  }

  @Post('play')
  async play(
    @User() user: ICurrentUser,
    @Req() req: Request,
    @Ip() ip: string,
    @Body() payload: PlayDto,
  ) {
    const clientIp = req.headers['x-forwarded-for']
    ? (req.headers['x-forwarded-for'] as string)
    : ip.replace('::ffff:', '');

    return this.commandBus.execute(
      new PlayQueueCommand(
        user,
        clientIp,
        req.headers['user-agent'],
        payload,
      ),
    );
  }

  @Get('state')
  @ApiOperation({ summary: 'get player state' })
  getCurrentPlay(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetPlayerStateQuery(user));
  }
}
