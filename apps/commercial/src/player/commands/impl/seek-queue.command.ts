import { ICommand } from "@nestjs/cqrs";
import { ICurrentUser } from "apps/commercial/src/auth/strategies/types/user.type";
import { SeekQueueDto } from "../../dto/seek-queue.dto";

export class SeekQueueCommand implements ICommand {
  constructor(
    public readonly user: ICurrentUser,
    public readonly args: SeekQueueDto,
    public readonly clientIp: string,
    public readonly agent: any
  ) {
  }
}