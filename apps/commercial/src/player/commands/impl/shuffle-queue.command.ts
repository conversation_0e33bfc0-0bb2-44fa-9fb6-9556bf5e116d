import { ICommand } from "@nestjs/cqrs";
import { ICurrentUser } from "apps/commercial/src/auth/strategies/types/user.type";
import { ShuffleQueueDto } from "../../dto/shuffle-queue.dto";

export class ShuffleQueueCommand implements ICommand {
  constructor(
    public readonly user: <PERSON><PERSON><PERSON>rent<PERSON><PERSON>,
    public readonly body: ShuffleQueueDto,
    public readonly clientIp: string,
    public readonly agent: any,
  ) {

  }
}