import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { NextQueueCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { QueueModel } from 'apps/commercial/src/queue/model/queue.model';
import { UAParser } from 'ua-parser-js';
import { CurrentModel } from 'apps/commercial/src/current/model/current.model';
import {
  ForbiddenException,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerModel } from '../../models/player.module';
import { firstValueFrom } from 'rxjs';
import { JinggleService } from 'apps/commercial/src/jinggle/jinggle.service';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { FEATURE } from 'apps/admin/src/auth/enum/FEATURE.enum';

@CommandHandler(NextQueueCommand)
export class NextQueueHandler implements ICommandHandler<NextQueueCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly gmiProvider: GmiRESTService,
    private readonly publisher: EventPublisher,
    private readonly player: PlayerGateway,
    @Inject('mobile') private client: ClientProxy,
    private readonly jingleService: JinggleService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async execute(command: NextQueueCommand) {
    const { user, clientIp, agent, args } = command;
    const { propertyId } = user;

    const songPackage = await this.prisma.zoneFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.SongQuota,
          },
          {
            activationId: user.deviceId,
          },
        ],
      },
    });

    if (!songPackage) {
      throw new ForbiddenException('Insufficient song quota');
    }

    if (songPackage.qouta < 1 && songPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient song quota');
    }

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
          {
            isUsed: true,
          },
        ],
      },
      include: {
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      if (device?.config) {
        let nextTrackIndex: number;
        let nextTrack;

        const queue = await this.prisma.queue.findFirst({
          where: { activationId: device.id, propertyId },
          include: {
            tracks: { orderBy: { order: 'asc' } },
            activation: true,
          },
        });
        if (!queue) {
          return { message: 'Queue not found' };
        }

        let currentTrackIndex = queue.tracks.findIndex(
          (track) => track.current,
        );
        if (currentTrackIndex === -1) {
          currentTrackIndex = 0;
        }

        if (device.config?.player) {
          const playerSettings = device.config?.player as { loop?: number };

          if (playerSettings.loop === 1) {
            nextTrackIndex = currentTrackIndex + 1;
            if (nextTrackIndex >= queue.tracks.length) {
              nextTrackIndex = 0;
            }
          } else {
            nextTrackIndex = currentTrackIndex + 1;
          }
        }

        if (nextTrackIndex >= queue.tracks.length) {
          return {
            status: 200,
            message: 'No more tracks in queue',
            url: null,
            track: null,
          };
        }
        nextTrack = queue.tracks[nextTrackIndex];

        const itemId = nextTrack.uri;
        const parts = itemId.split(':');

        const [urlData, track] = await Promise.all([
          this.gmiProvider.findTrackUrl(parts[1], clientIp),
          this.gmiProvider.optimize(parts[1]),
        ]);

        const currentQueueModel = this.publisher.mergeClassContext(QueueModel);
        const eventCurrent = new currentQueueModel();

        const history = await this.prisma.playHistory.findFirst({
          where: {
            activationId: user.deviceId,
          },
          orderBy: { createdAt: 'desc' },
        });

        const currentTrackData = await this.prisma.currentPlayer.findFirst({
          where: {
            activationId: user.deviceId,
          },
          orderBy: { createdAt: 'desc' },
        });

        const jinggleStatus = await firstValueFrom(
          this.jingleService.isScheduleAvailable({
            id: user.deviceId,
            createdAtHistorySong: history?.createdAt?.getTime() ?? 0,
            timezone: device.timezone,
            createdAtCurrentPlay: currentTrackData?.createdAt.getTime() ?? 0,
          }),
        );

        // console.log('INI JINGLE STATUS ', jinggleStatus);

        if (jinggleStatus.isAvailable) {
          const historyPlaylistJingleDate = new Date(
            Number(jinggleStatus.lastPlaylistHistoryCreatedAt),
          );
          const scheduleJingleDate = new Date(
            Number(jinggleStatus.scheduleUpdatedAt),
          );

          const historySongData = await this.prisma.playHistory.findMany({
            where: {
              AND: [
                {
                  activationId: user.deviceId,
                },
                {
                  createdAt: { gte: historyPlaylistJingleDate },
                },
              ],
            },
          });

          const historySongFromScheduleJingle =
            await this.prisma.playHistory.findMany({
              where: {
                AND: [
                  {
                    activationId: user.deviceId,
                  },
                  {
                    createdAt: { gte: scheduleJingleDate },
                  },
                ],
              },
            });

          // console.log(
          //   'APAKAH BENAR ',
          //   jinggleStatus.ratio - historySongData.length <= 0,
          //   jinggleStatus.ratio,
          //   historySongData.length,
          // );

          const isUserOnPlaying = await this.redis.get(
            `schedule_jingle_isCurrentPLaying${user.deviceId}`,
          );

          let historySongLenght = historySongData.length;
          let jinggleStatusRemain = jinggleStatus.remain;
          let jinggleStatusRatio = jinggleStatus.ratio;
          if (jinggleStatus.isPlaynow && !jinggleStatus.isPlayed) {
            jinggleStatusRatio++;
            jinggleStatusRemain--;
          }
          // console.log(
          //   'CEKKKK historySongLenght',
          //   historySongLenght,
          //   jinggleStatusRemain,
          //   jinggleStatusRatio,
          // );
          if (
            jinggleStatusRemain > 0 ||
            jinggleStatusRatio - historySongData.length <= 0 ||
            (!jinggleStatus.isPlayed &&
              jinggleStatus.isPlayed != undefined &&
              historySongFromScheduleJingle.length > 0 &&
              !jinggleStatus.isPlaynow)
          ) {
            const jingleUrl = await firstValueFrom(
              this.jingleService.getJingleUrlTrackRatio({
                playlistId: jinggleStatus.playlistId,
                scheduleId: jinggleStatus.scheduleId,
                zoneId: user.deviceId,
                isPlayed: jinggleStatus.isPlayed,
              }),
            );

            eventCurrent.QueueCurrent(nextTrack.trackId, user, true);

            this.jingleService
              .updateCurrentTrack({
                duration: 0,
                trackId: jingleUrl.trackId,
                propertyId: user.propertyId,
                zoneId: user.deviceId,
              })
              .subscribe({
                error: () => {},
              });

            return {
              type: 'jingle',
              url: jingleUrl.url,
              track: {
                id: jingleUrl.trackId,
                artists: [
                  {
                    name: jingleUrl.artistName,
                  },
                ],
                title: jingleUrl.trackName,
                duration: jingleUrl.duration.toString(),
              },
              isCrossFade: jingleUrl.isCrossFade,
              isLockMode: jingleUrl.isLockMode,
              startAt: jingleUrl.startAt,
              category: jingleUrl.category,
            };
          }
        }
        this.jingleService
          .updateCurrentTrack({
            duration: 0,
            trackId: '',
            propertyId: user.propertyId,
            zoneId: user.deviceId,
          })
          .subscribe({
            error: () => {},
          });

        eventCurrent.QueueCurrent(nextTrack.trackId, user);

        const url = urlData.url;
        track.uri = itemId;

        const ua = UAParser(agent);
        const currentModel = this.publisher.mergeClassContext(CurrentModel);
        const eventCurrentTrack = new currentModel();
        eventCurrentTrack.createCurrentTrack({
          itemId: track?.id,
          deviceId: device.id,
          info: {
            ip: clientIp ? clientIp : undefined,
            os: ua.os.name,
            userAgent: agent ? agent : undefined,
            isp: null,
            geo: null,
            macAddress: null,
          },
          propertyId: user.propertyId,
        });

        this.player.server.to(device.id).emit('command-remote', 'next');

        const playerModel = this.publisher.mergeClassContext(PlayerModel);
        const player = new playerModel();
        player.updatePlayerState({
          propertyId: device.propertyId,
          id: device.id,
          value: {
            paused: device.config.player['paused'],
            muted: device.config.player['muted'],
            volume: device.config.player['volume'],
            loopMode: device.config.player['loop'],
            shuffle: device.config.player['shuffle'],
            trackWindow: {
              currentTrack: {
                title: track ? track['title'] : null,
              },
            },
          },
        });
        return { type: 'song', url, track, skip: true };
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'command',
          to: device.activationDevice[0].device.serialNumber,
          value: 'next',
          deviceType: device.activationDevice[0].device.type,
        });
        return { url: null, track: null };
      }
    } catch (e) {
      console.error(e);
      if (e instanceof HttpException) {
        throw e;
      }

      throw new InternalServerErrorException();
    }
  }
}
