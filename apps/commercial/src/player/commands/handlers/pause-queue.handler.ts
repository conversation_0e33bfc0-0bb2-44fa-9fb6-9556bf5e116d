import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PauseQueueCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import {
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerModel } from '../../models/player.module';

@CommandHandler(PauseQueueCommand)
export class PauseQueueHandler implements ICommandHandler<PauseQueueCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly player: PlayerGateway,
    private readonly publisher: EventPublisher,
    @Inject('mobile') private client: ClientProxy,
  ) {}
  async execute(command: PauseQueueCommand) {
    const { user, args } = command;

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        currentPlayer: {
          include: {
            track: true
          }
        },
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      if (device?.config) {
        device.config.player['paused'] = true;
        const config = await this.prisma.config.update({
          where: {
            id: device.config.id,
          },
          data: {
            player: device.config.player,
          },
        });

        const playerModel = this.publisher.mergeClassContext(PlayerModel);
        const player = new playerModel();
        player.updatePlayerState({
          propertyId: device.propertyId,
          id: device.id,
          value: {
            paused: config.player['paused'],
            muted: config.player['muted'],
            volume: config.player['volume'],
            loopMode: config.player['loop'],
            shuffle: config.player['shuffle'],
            trackWindow:
              {
                currentTrack: {
                  title: device?.currentPlayer[0].track.key['title']
                }
              }
            }
          }
        );
      }

      const current = await this.prisma.currentPlayer.findFirst({
        where: {
          AND: [
            { activationId: device.id },
            { propertyId: user.propertyId },
            {
              paused: false,
            },
          ],
        },
        orderBy: {
          updatedAt: 'desc',
        },
      });

      if (!current) {
        if (device.activationDevice[0].device.type === 'web') {
          this.player.server.to(device.id).emit('command-remote', 'pause');
        } else {
          this.client.emit('REMOTE-PLAYER', {
            event: 'command',
            to: device.activationDevice[0].device.serialNumber,
            value: 'pause',
            deviceType: device.activationDevice[0].device.type,
          });
        }
        return {};
      }

      await this.prisma.currentPlayer.update({
        where: {
          id: current.id,
        },
        data: {
          paused: true,
        },
      });

      if (device.activationDevice[0].device.type === 'web') {
        this.player.server.to(device.id).emit('command-rommand', 'pause');
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'command',
          to: device.activationDevice[0].device.serialNumber,
          value: 'pause',
          deviceType: device.activationDevice[0].device.type,
        });
      }

      return {
        status: HttpStatus.CREATED,
        message: 'success pause track',
      };
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }
}
