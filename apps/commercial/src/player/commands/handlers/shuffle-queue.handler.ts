import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ShuffleQueueCommand } from '../impl';
import {
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { QueueModel } from 'apps/commercial/src/queue/model/queue.model';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerModel } from '../../models/player.module';

@CommandHandler(ShuffleQueueCommand)
export class ShuffleQueue<PERSON>andler
  implements ICommandHandler<ShuffleQueueCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
    private readonly player: PlayerGateway,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: ShuffleQueueCommand) {
    const { user, body } = command;
    const { propertyId } = user;

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        currentPlayer: {
          include: {
            track: true
          }
        },
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      if (device?.config) {
        device.config.player['shuffle'] = body.state;
        await this.prisma.config.update({
          where: {
            id: device.config.id,
          },
          data: {
            player: device.config.player,
          },
        });
      }

      if (device.activationDevice[0].device.type === 'web') {
        let originalTracksOrder: any[] = [];

        let queue = await this.prisma.queue.findFirst({
          where: { activationId: user.deviceId, propertyId },
          include: {
            tracks: true,
          },
        });

        if (!queue) {
          return {
            status: HttpStatus.NOT_FOUND,
            message: 'Queue not found',
          };
        }

        await this.prisma.$transaction(
          async (tx) => {
            if (queue.tracks) {
              originalTracksOrder = [...queue.tracks];
            }

            if (body.state) {
              queue.tracks = this.shuffleArray(queue.tracks);
              await Promise.all(
                queue.tracks.map(async (track, index) => {
                  await tx.queueTrack.update({
                    where: { id: track.id },
                    data: { order: index },
                  });
                }),
              );
            } else {
              await Promise.all(
                originalTracksOrder.map(async (track, index) => {
                  await tx.queueTrack.update({
                    where: { id: track.id },
                    data: { order: index },
                  });
                }),
              );
            }

            const config = await tx.config.findFirst({
              where: { activationId: user.deviceId },
            });

            if (!config) {
              return {
                status: HttpStatus.NOT_FOUND,
                message: 'Config not found',
              };
            }

            config.player['shuffle'] = body.state;

            const conf = await tx.config.update({
              where: { id: config.id },
              data: {
                player: config.player,
              },
            });

            const playerModel = this.publisher.mergeClassContext(PlayerModel);
            const player = new playerModel();
            player.updatePlayerState({
              propertyId: device.propertyId,
              id: device.id,
              value: {
                paused: conf.player['paused'],
                muted: conf.player['muted'],
                volume: conf.player['volume'],
                loopMode: conf.player['loop'],
                shuffle: conf.player['shuffle'],
                trackWindow:
                  {
                    currentTrack: {
                      title: device?.currentPlayer[0].track.key['title']
                    }
                  }
                }
              }
            );
          },
          {
            maxWait: 5000, // 5 seconds max wait to connect
            timeout: 20000, // 20 seconds timeout
            retry: 3,
          },
        );

        const current = await this.prisma.queueTrack.findFirst({
          where: { current: true, queueId: queue.id },
        });

        const currentQueueModel = this.publisher.mergeClassContext(QueueModel);
        const eventCurrent = new currentQueueModel();
        eventCurrent.QueueCurrent(current.trackId, user);

        this.player.server.to(device.id).emit('command-remote', 'shuffle');

        return {
          message: body.state ? 'Shuffle enabled' : 'Shuffle disabled',
        };
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'command',
          to: device.activationDevice[0].device.serialNumber,
          value: 'shuffle',
          deviceType: device.activationDevice[0].device.type,
        });
      }
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }

  shuffleArray(array: any[]): any[] {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
  }
}
