import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { MutedCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import {
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerModel } from '../../models/player.module';

@CommandHandler(MutedCommand)
export class MutedHandler implements ICommandHandler<MutedCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly player: PlayerGateway,
    private readonly publisher: EventPublisher,
    @Inject('mobile') private client: ClientProxy,
  ) {}
  async execute(command: MutedCommand) {
    const { user, args } = command;

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        config: true,
        currentPlayer: {
          include: {
            track: true
          }
        },
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      if (device?.config) {
        device.config.player['muted'] = args.enable;
        const config = await this.prisma.config.update({
          where: {
            id: device.config.id,
          },
          data: {
            player: device.config.player,
          },
        });
        const playerModel = this.publisher.mergeClassContext(PlayerModel);
        const player = new playerModel();
        player.updatePlayerState({
          propertyId: device.propertyId,
          id: device.id,
          value: {
            paused: config.player['paused'],
            muted: config.player['muted'],
            volume: config.player['volume'],
            loopMode: config.player['loop'],
            shuffle: config.player['shuffle'],
            trackWindow:
              {
                currentTrack: {
                  title: device?.currentPlayer[0].track.key['title']
                }
              }
            }
          }
        );
      }

      if (device.activationDevice[0].device.type === 'web') {
        this.player.server.to(device.id).emit('command-remote', 'mute');
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'command',
          to: device.activationDevice[0].device.serialNumber,
          value: 'mute',
          deviceType: device.activationDevice[0].device.type,
        });
      }
      return {
        status: HttpStatus.CREATED,
        message: 'success muted player',
      };
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }
}
