import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { SeekQueueCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { HistoryModel } from 'apps/commercial/src/history/models/history.model';
import {
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerModel } from '../../models/player.module';

@CommandHandler(SeekQueueCommand)
export class SeekQueueHandler implements ICommandHandler<SeekQueueCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
    private readonly player: PlayerGateway,
    @Inject('mobile') private client: ClientProxy,
  ) {}
  async execute(command: SeekQueueCommand) {
    const { user, args } = command;
    const { propertyId } = user;
    const position = Math.floor(args.value / 1000);

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        currentPlayer: {
          include: {
            track: true
          }
        },
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      const current = await this.prisma.currentPlayer.findFirst({
        where: {
          AND: [
            { activationId: device.id },
            {
              propertyId: propertyId,
            },
          ],
        },
      });
      if (current) {
        await this.prisma.$transaction(
          async (tr) => {
            const currentTrack = await tr.currentPlayer.update({
              where: {
                id: current.id,
              },
              data: {
                counter: current.counter + 1,
                duration: position,
                paused: false,
              },
            });

            const item = await tr.setting.findFirst({
              where: { type: 'history' },
            });

            if (!item) {
              return;
            }
            const option = item.option as any;
            const updatedAt = new Date(currentTrack.updatedAt);
            const createdAt = new Date(currentTrack.createdAt);
            const difMil = Number(updatedAt) - Number(createdAt);
            const difSec = Math.floor(difMil / 1000);
            if (difSec >= Number(option.interval)) {
              const history = await this.prisma.playHistory.findFirst({
                where: {
                  AND: [
                    {
                      track: {
                        id: currentTrack.trackId,
                      },
                    },
                    {
                      activation: {
                        id: currentTrack.activationId,
                      },
                    },
                    {
                      property: {
                        id: currentTrack.propertyId,
                      },
                    },
                    {
                      endAt: null,
                    },
                  ],
                },
              });
              if (!history) {
                const eventModel =
                  this.publisher.mergeClassContext(HistoryModel);
                const event = new eventModel();
                event.createTrackHistory({
                  key: currentTrack.trackId,
                  trackId: currentTrack.trackId,
                  propertyId: currentTrack.propertyId,
                  deviceId: device.id,
                  playerType: device.activationDevice[0].device.type,
                  userAgent: currentTrack.userAgent,
                  ip: currentTrack.ip,
                  os: currentTrack.os,
                  macAddress: currentTrack.macAddress,
                  isp: currentTrack.isp,
                  geo: currentTrack.geo,
                  duration: currentTrack.duration,
                  durationType: currentTrack.durationType,
                });
              }
            }

            if (device?.config) {
              device.config.player['position'] = position;
              const config = await tr.config.update({
                where: {
                  id: device.config.id,
                },
                data: {
                  player: device.config.player,
                },
              });
              const playerModel = this.publisher.mergeClassContext(PlayerModel);
              const player = new playerModel();
              player.updatePlayerState({
                propertyId: device.propertyId,
                id: device.id,
                value: {
                  paused: config.player['paused'],
                  muted: config.player['muted'],
                  volume: config.player['volume'],
                  loopMode: config.player['loop'],
                  shuffle: config.player['shuffle'],
                  trackWindow:
                    {
                      currentTrack: {
                        title: device?.currentPlayer[0].track.key['title']
                      }
                    }
                  }
                }
              );
            }
          },
          {
            maxWait: 5000, // 5 seconds max wait to connect
            timeout: 20000, // 20 seconds timeout
            retry: 3,
          },
        );
      }

      if (device.activationDevice[0].device.type === 'web') {
        this.player.server.to(device.id).emit('seek', args.value);
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'seek',
          to: device.activationDevice[0].device.serialNumber,
          value: args.value,
          deviceType: device.activationDevice[0].device.type,
        });
      }
      return {
        status: HttpStatus.CREATED,
        message: 'success seek track',
      };
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }
}
