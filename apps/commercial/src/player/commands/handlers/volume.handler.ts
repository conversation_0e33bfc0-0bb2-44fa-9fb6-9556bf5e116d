import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { VolumeCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import {
  HttpStatus,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { PlayerModel } from '../../models/player.module';

@CommandHandler(VolumeCommand)
export class VolumeHandler implements ICommandHandler<VolumeCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly player: PlayerGateway,
    private readonly publisher: EventPublisher,
    @Inject('mobile') private client: ClientProxy,
  ) {}
  async execute(command: VolumeCommand) {
    const { user, args } = command;

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        currentPlayer: {
          include: {
            track: true
          }
        },
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    const config = await this.prisma.config.findFirst({
      where: {
        activationId: device.id,
      },
    });

    try {
      if (config) {
        config.player['volume'] = args.value;
        const conf = await this.prisma.config.update({
          where: {
            id: config.id,
          },
          data: {
            player: config.player,
          },
        });
        const playerModel = this.publisher.mergeClassContext(PlayerModel);
        const player = new playerModel();
        player.updatePlayerState({
          propertyId: device.propertyId,
          id: device.id,
          value: {
            paused: conf.player['paused'],
            muted: conf.player['muted'],
            volume: conf.player['volume'],
            loopMode: conf.player['loop'],
            shuffle: conf.player['shuffle'],
            trackWindow:
              {
                currentTrack: {
                  title: device?.currentPlayer[0].track.key['title']
                }
              }
            }
          }
        );
      }

      if (device.activationDevice[0].device.type === 'web') {
        this.player.server.to(device.id).emit('volume', args.value);
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'volume',
          to: device.activationDevice[0].device.serialNumber,
          value: args.value,
          deviceType: device.activationDevice[0].device.type,
        });
      }
      return {
        status: HttpStatus.CREATED,
        message: 'success adjust player volume',
      };
    } catch (error) {
      console.error(error);
      throw new InternalServerErrorException();
    }
  }
}
