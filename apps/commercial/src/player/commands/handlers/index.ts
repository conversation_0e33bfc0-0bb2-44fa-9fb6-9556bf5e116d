import { <PERSON><PERSON><PERSON><PERSON> } from "./loop.handler";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "./muted.handler";
import { NextQueueHandler } from "./next-queue.handler";
import { PauseQueueHandler } from "./pause-queue.handler";
import { PlayQueueHandler } from "./play-queue.handler";
import { PrevQueueHandler } from "./prev-queue.handler";
import { SeekQueueHandler } from "./seek-queue.handler";
import { ShuffleQueueHandler } from "./shuffle-queue.handler";
import { VolumeHandler } from "./volume.handler";

export const PlayerCommandHandlers = [
  <PERSON>Q<PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  VolumeHandler,
]