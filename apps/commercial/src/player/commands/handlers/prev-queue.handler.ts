import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrevQueueCommand } from '../impl';
import { PrismaService } from 'libs/prisma';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { QueueModel } from 'apps/commercial/src/queue/model/queue.model';
import { UAParser } from 'ua-parser-js';
import { CurrentModel } from 'apps/commercial/src/current/model/current.model';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { ClientProxy } from '@nestjs/microservices';
import {
  ForbiddenException,
  HttpException,
  Inject,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PlayerModel } from '../../models/player.module';
import { JinggleService } from 'apps/commercial/src/jinggle/jinggle.service';
import { firstValueFrom } from 'rxjs';
import Redis from 'ioredis';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(PrevQueueCommand)
export class PrevQueueHandler implements ICommandHandler<PrevQueueCommand> {
  constructor(
    private readonly prisma: PrismaService,
    private readonly gmiProvider: GmiRESTService,
    private readonly publisher: EventPublisher,
    private readonly player: PlayerGateway,
    @Inject('mobile') private client: ClientProxy,
    private readonly jingleService: JinggleService,
    @InjectRedis() private readonly redis: Redis,
  ) {}
  async execute(command: PrevQueueCommand) {
    const { user, clientIp, agent, args } = command;
    const { deviceId, propertyId } = user;

    const songPackage = await this.prisma.zoneFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.SongQuota,
          },
          {
            activationId: user.deviceId,
          },
        ],
      },
    });

    if (!songPackage) {
      throw new ForbiddenException('Insufficient song quota');
    }

    if (songPackage.qouta < 1 && songPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient song quota');
    }

    const device = await this.prisma.activation.findFirst({
      where: {
        AND: [
          {
            id: user.deviceId,
          },
          {
            property: {
              id: user.propertyId,
            },
          },
        ],
      },
      include: {
        config: true,
        activationDevice: {
          include: {
            device: true,
          },
          orderBy: {
            updatedAt: 'desc',
          },
          take: 1,
        },
        property: true,
      },
    });
    if (!device) {
      throw new NotFoundException('device not found');
    }

    try {
      if (device.activationDevice[0].device.type === 'web') {
        const queue = await this.prisma.queue.findFirst({
          where: { activationId: deviceId, propertyId },
          include: { tracks: { orderBy: { order: 'asc' } } },
        });

        if (!queue) {
          return { message: 'Queue not found' };
        }

        let currentTrackIndex = queue.tracks.findIndex(
          (track) => track.current,
        );
        if (currentTrackIndex === -1) {
          currentTrackIndex = 0;
        }

        const prevTrackIndex = currentTrackIndex - 1;
        if (prevTrackIndex < 0) {
          return {
            status: 200,
            message: 'No previous tracks in queue',
            url: null,
            track: null,
          };
        }

        const prevTrack = queue.tracks[prevTrackIndex];
        const itemId = prevTrack.uri;
        const parts = itemId.split(':');

        const [urlData, track] = await Promise.all([
          this.gmiProvider.findTrackUrl(parts[1], clientIp),
          this.gmiProvider.optimize(parts[1]),
        ]);

        const currentQueueModel = this.publisher.mergeClassContext(QueueModel);
        const eventCurrent = new currentQueueModel();
        eventCurrent.QueueCurrent(prevTrack.trackId, user);

        const history = await this.prisma.playHistory.findFirst({
          where: {
            activationId: user.deviceId,
          },
          orderBy: { createdAt: 'desc' },
        });

        const currentTrackData = await this.prisma.currentPlayer.findFirst({
          where: {
            activationId: user.deviceId,
          },
          orderBy: { createdAt: 'desc' },
        });

        const jinggleStatus = await firstValueFrom(
          this.jingleService.isScheduleAvailable({
            id: user.deviceId,
            createdAtHistorySong: history?.createdAt?.getTime() ?? 0,
            timezone: device.timezone,
            createdAtCurrentPlay: currentTrackData?.createdAt.getTime() ?? 0,
          }),
        );

        // console.log('first  ', jinggleStatus);
        if (jinggleStatus.isAvailable) {
          const historyPlaylistJingleDate = new Date(
            Number(jinggleStatus.lastPlaylistHistoryCreatedAt),
          );
          const scheduleJingleDate = new Date(
            Number(jinggleStatus.scheduleUpdatedAt),
          );

          const historySongData = await this.prisma.playHistory.findMany({
            where: {
              AND: [
                {
                  activationId: user.deviceId,
                },
                {
                  createdAt: { gte: historyPlaylistJingleDate },
                },
              ],
            },
          });

          const historySongFromScheduleJingle =
            await this.prisma.playHistory.findMany({
              where: {
                AND: [
                  {
                    activationId: user.deviceId,
                  },
                  {
                    createdAt: { gte: scheduleJingleDate },
                  },
                ],
              },
            });

          // console.log(
          //   'APAKAH BENAR ',
          //   jinggleStatus.ratio - historySongData.length <= 0,
          //   jinggleStatus.ratio,
          //   historySongData.length,
          // );

          const isUserOnPlaying = await this.redis.get(
            `schedule_jingle_isCurrentPLaying${user.deviceId}`,
          );
          let historySongLenght = historySongData.length;
          let jinggleStatusRemain = jinggleStatus.remain;
          let jinggleStatusRatio = jinggleStatus.ratio;
          if (jinggleStatus.isPlaynow && !jinggleStatus.isPlayed) {
            jinggleStatusRatio++;
            jinggleStatusRemain--;
          }
          //console.log(
          //  'CEKKKK historySongLenght',
          //  historySongLenght,
          //  jinggleStatusRemain,
          //  jinggleStatusRatio,
          //);
          if (
            jinggleStatusRemain > 0 ||
            jinggleStatusRatio - historySongData.length <= 0 ||
            (!jinggleStatus.isPlayed &&
              jinggleStatus.isPlayed != undefined &&
              historySongFromScheduleJingle.length > 0 &&
              !jinggleStatus.isPlaynow)
          ) {
            const jingleUrl = await firstValueFrom(
              this.jingleService.getJingleUrlTrackRatio({
                playlistId: jinggleStatus.playlistId,
                scheduleId: jinggleStatus.scheduleId,
                zoneId: user.deviceId,
                isPlayed: jinggleStatus.isPlayed,
              }),
            );

            return {
              type: 'jingle',
              url: jingleUrl.url,
              track: {
                id: jingleUrl.trackId,
                artists: [
                  {
                    name: jingleUrl.artistName,
                  },
                ],
                title: jingleUrl.trackName,
                duration: jingleUrl.duration.toString(),
              },
              isCrossFade: jingleUrl.isCrossFade,
              isLockMode: jingleUrl.isLockMode,
              startAt: jingleUrl.startAt,
              category: jingleUrl.category,
            };
          }
        }

        const url = urlData.url;
        track.uri = itemId;

        const ua = UAParser(agent);
        const currentModel = this.publisher.mergeClassContext(CurrentModel);
        const eventCurrentTrack = new currentModel();
        eventCurrentTrack.createCurrentTrack({
          itemId: track?.id,
          deviceId: user.deviceId,
          info: {
            ip: clientIp ? clientIp : undefined,
            os: ua.os.name,
            userAgent: agent ? agent : undefined,
            isp: null,
            geo: null,
            macAddress: null,
          },
          propertyId: user.propertyId,
        });

        this.player.server.to(device.id).emit('command-remote', 'prev');
        const playerModel = this.publisher.mergeClassContext(PlayerModel);
        const player = new playerModel();
        player.updatePlayerState({
          propertyId: device.propertyId,
          id: device.id,
          value: {
            paused: device.config.player['paused'],
            muted: device.config.player['muted'],
            volume: device.config.player['volume'],
            loopMode: device.config.player['loop'],
            shuffle: device.config.player['shuffle'],
            trackWindow: {
              currentTrack: {
                title: track ? track['title'] : null,
              },
            },
          },
        });
        return { type: 'song', url, track };
      } else {
        this.client.emit('REMOTE-PLAYER', {
          event: 'command',
          to: device.activationDevice[0].device.serialNumber,
          value: 'prev',
          deviceType: device.activationDevice[0].device.type,
        });
        return { url: null, track: null };
      }
    } catch (error) {
      console.error(error);
      if (error instanceof HttpException) {
        throw error;
      }

      throw new InternalServerErrorException();
    }
  }
}
