import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdatePlayerStateEvent } from '../impl';
import { WebGateway } from 'apps/commercial/src/events/web.gateway';

@EventsHandler(UpdatePlayerStateEvent)
export class UpdatePlayerStateHandler implements IEventHandler<UpdatePlayerStateEvent> {
  constructor(
    private prisma: PrismaService,
    private webGateway: WebGateway
  ) {}

  async handle(event: UpdatePlayerStateEvent) {
    const { args } = event;
    try {
      const property = await this.prisma.property.findFirst({
        where: {
          id: args.propertyId
        },
        include: {
          users: {
            where: {
              user: {
                isAdmin: true
              }
            },
            include: {
              user: true
            }
          }
        }
      });
  
      if (property && property.users[0]?.user) {
        this.webGateway.server.to(property.users[0].userId).emit('device', {
          id: args.id as string,
          state: 'PLAYER',
          value: args.value
        });
      }

    } catch (error) {
      console.log('failed decrease qty feature');
    }
  }
}
