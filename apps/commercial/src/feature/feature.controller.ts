import { Controller, Get, Param, Query } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiExcludeController, ApiTags } from '@nestjs/swagger';
import { FilterFeatureDto } from './dto/filter-feature.dto';
import { GetFeatureQuery, GetFeaturesQuery } from './queries';

@ApiTags('Feature')
@Controller('feature')
@ApiExcludeController()
export class FeatureController {
  constructor(private queryBus: QueryBus) {}

  @Get()
  findAll(@Query() filter: FilterFeatureDto) {
    return this.queryBus.execute(new GetFeaturesQuery(filter));
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.queryBus.execute(new GetFeatureQuery(id));
  }
}
