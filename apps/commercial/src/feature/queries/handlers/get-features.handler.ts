import { Query<PERSON><PERSON><PERSON>, <PERSON><PERSON>ueryH<PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetFeaturesQuery } from '../impl';

@QueryHandler(GetFeaturesQuery)
export class GetFeaturesHandler implements IQueryHandler<GetFeaturesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetFeaturesQuery) {
    const items = await this.prisma.feature.findMany();
    return items;
  }
}
