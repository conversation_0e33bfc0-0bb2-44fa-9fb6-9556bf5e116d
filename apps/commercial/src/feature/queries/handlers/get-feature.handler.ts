import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetFeatureQuery } from '../impl';

@QueryHandler(GetFeatureQuery)
export class GetFeatureHandler implements IQueryHandler<GetFeatureQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetFeatureQuery) {
    const { id } = query;
    const item = await this.prisma.feature.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
