import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListDemoCodePropertiesQuery } from '../impl/list-demo-code-properties.query';
import { PrismaService } from 'libs/prisma';
import { ListDemoCodePropertiesResponse } from '@app/proto-schema/index.internal';
import { Pagination } from '@app/common';
import { DemoCodeDetail, Property, Prisma } from '@prisma/client';

@QueryHandler(ListDemoCodePropertiesQuery)
export class ListDemoCodePropertiesHandler
  implements IQueryHandler<ListDemoCodePropertiesQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(
    query: ListDemoCodePropertiesQuery,
  ): Promise<ListDemoCodePropertiesResponse> {
    const { args } = query;
    const search = args.query?.query || '';

    const paginatedResult = await Pagination<
      DemoCodeDetail & { property?: Property },
      Prisma.DemoCodeDetailFindManyArgs
    >(
      this.prisma.demoCodeDetail,
      {
        where: {
          propertyId: { not: null },
          ...(args.demoCodeId && { demoCodeId: args.demoCodeId }),
          OR: [{ code: { contains: search, mode: 'insensitive' } }],
        },
        include: {
          property: true,
        },
      },
      { page: args.query?.params?.page, limit: args.query?.params?.limit },
    );
    return {
      data: paginatedResult.data.map((item) => ({
        code: item.code,
        properties: item.property
          ? [
              {
                id: item.property.id,
                cid: item.property.cid,
                companyName: item.property.companyName,
                brandName: item.property.brandName,
                companyEmail: item.property.companyEmail,
                companyPhoneNumber: item.property.companyPhoneNumber,
                npwp: item.property.npwp,
                address: item.property.address,
                createdAt: item.property.createdAt.toISOString(),
                updatedAt: item.property.updatedAt.toISOString(),
                postalId: item.property.postalId,
                propertyTypeId: item.property.propertyTypeId,
                configuration: null,
                contactPerson: null,
              },
            ]
          : [],
      })),
      meta: paginatedResult.meta,
    };
  }
}
