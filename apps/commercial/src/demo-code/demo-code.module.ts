import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { DemoCodeRpcController } from './demo-code.rpc.controller';
import { DemoCOdeCommandHandlers } from './commands';
import { MembershipClientModule } from '../membership-client/membership-client.module';
import { DemoCodeQueryHandlers } from './queries';

@Module({
  imports: [CqrsModule, MembershipClientModule],
  controllers: [DemoCodeRpcController],
  providers: [...DemoCOdeCommandHandlers, ...DemoCodeQueryHandlers],
})
export class DemoCodeModule {}
