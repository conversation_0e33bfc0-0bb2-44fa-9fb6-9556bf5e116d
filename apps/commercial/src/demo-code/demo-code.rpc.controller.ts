import { Controller } from '@nestjs/common';
import {
  DEMO_CODE_SERVICE_NAME,
  DemoCodeRequest,
  DemoCodeServiceController,
  ListDemoCodePropertiesRequest,
  ListDemoCodePropertiesResponse,
} from '@app/proto-schema/internal-proto/demo-code';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Metadata } from '@grpc/grpc-js';
import { Status } from '@app/proto-schema/common-proto/common';
import { Observable } from 'rxjs';
import { UseDemoCodeCommand, ValidateDemoCodeCommand } from './commands';
import { ListDemoCodePropertiesQuery } from './queries/impl/list-demo-code-properties.query';

@Controller()
export class DemoCodeRpcController implements DemoCodeServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @GrpcMethod(DEMO_CODE_SERVICE_NAME, 'useDemoCode')
  useDemoCode(
    request: DemoCodeRequest,
    metadata: Metadata,
    ...rest
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new UseDemoCodeCommand(request));
  }

  @GrpcMethod(DEMO_CODE_SERVICE_NAME, 'listDemoCodeProperties')
  listDemoCodeProperties(
    request: ListDemoCodePropertiesRequest,
    metadata: Metadata,
    ...rest
  ):
    | Promise<ListDemoCodePropertiesResponse>
    | Observable<ListDemoCodePropertiesResponse>
    | ListDemoCodePropertiesResponse {
    return this.queryBus.execute(new ListDemoCodePropertiesQuery(request));
  }

  @GrpcMethod(DEMO_CODE_SERVICE_NAME, 'validateDemoCode')
  validateDemoCode(
    request: DemoCodeRequest,
    metadata: Metadata,
    ...rest
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new ValidateDemoCodeCommand(request));
  }
}
