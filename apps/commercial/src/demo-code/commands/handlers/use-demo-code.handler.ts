import { <PERSON><PERSON>us, CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { UseDemoCodeCommand } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { firstValueFrom } from 'rxjs';
import { PropertyService } from '../../../membership-client/property.service';
import { OrderService } from '../../../membership-client/order.service';
import { CreatePlanTrialCommand } from '../../../../../admin/src/plan/commands';
import { CreateAccountCommand } from '../../../auth/commands';
import { CreateDemoCommand } from '../../../package/commands';

@CommandHandler(UseDemoCodeCommand)
export class UseDemoCodeHandler implements ICommandHandler<UseDemoCodeCommand> {
  constructor(
    private prisma: PrismaService,
    private propertyService: PropertyService,
    private commandBus: CommandBus,
    private orderService: OrderService,
  ) {}

  async execute(command: UseDemoCodeCommand) {
    const { args } = command;

    let account = null;

    const demoCode = await this.prisma.demoCode.findFirst({
      where: {
        code: args.code,
      },
      include: {
        DemoCodeDetail: {
          where: { status: 'available' },
        },
        plans: true,
      },
    });

    if (!demoCode) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Demo-code not found',
      });
    }

    if (demoCode.DemoCodeDetail.length === 0) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Demo-code max limit usage',
      });
    }

    const demoDetail = demoCode.DemoCodeDetail[0];
    if (demoDetail.status === 'used') {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code already used',
      });
    }
    const currentDate = new Date();
    if (currentDate > demoCode.expiredAt) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code has expired',
      });
    }

    const userProperty = await firstValueFrom(
      this.propertyService.getPropertyUser(args.propertyId),
    );

    const existingDemoDetail = await this.prisma.demoCodeDetail.findFirst({
      where: {
        propertyId: userProperty.property.id,
      },
    });

    if (existingDemoDetail) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code already exists with this property',
      });
    }

    const onePlan = await this.prisma.plan.findMany({
      where: {
        id: { in: demoCode.plans.map((plan) => plan.planId) },
        subfolderId: userProperty.property.industryPlan,
        type: 'trial',
      },
    });

    if (!onePlan) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Industry tier not match',
      });
    }

    const order = await this.orderService
      .trialOrder(userProperty.userProperty.propertyId, 'PLAN', onePlan[0].id)
      .toPromise();

    userProperty.property['flag'] = 'DEMO';
    if (userProperty.user.businessType === 'Single') {
      const existingUser = await this.prisma.userProperty.findFirst({
        where: {
          AND: [
            {
              user: {
                AND: [
                  {
                    isActive: true,
                  },
                  {
                    isAdmin: true,
                  },
                  {
                    crmUserId: userProperty.user.id,
                  },
                ],
              },
            },
          ],
        },
      });
      if (!existingUser) {
        account = await this.commandBus.execute(
          new CreateAccountCommand(
            {
              id: userProperty.user.id,
              email: userProperty.user.email,
              password: userProperty.user.password,
              username: userProperty.user.username,
              dateOfBirth: userProperty.user.profile?.dateOfBirth,
              firstName: userProperty.user.profile.firstName,
              address: userProperty.user.profile?.address,
              lastName: userProperty.user.profile.lastName,
              placeOfBirth: userProperty.user.profile?.placeOfBirth,
              phoneNumber: userProperty.user.mobileNumber,
              gender: userProperty.user.profile.gender,
              property: userProperty.property,
              isAdmin: true,
              zone: userProperty.user.zone,
            },
            true,
          ),
        );
      } else {
        account.user = existingUser;
      }

      Object.assign(account.property, {
        categoryCode: userProperty.property.categoryId,
        unit: Number(userProperty.property.unit),
        licenseKey: userProperty.property?.licenseKey,
      });
    } else {
      account = await this.commandBus.execute(
        new CreateAccountCommand(
          {
            id: userProperty.user.id,
            email: userProperty.user.email,
            password: userProperty.user.password,
            username: userProperty.user.username,
            dateOfBirth: userProperty.user.profile?.dateOfBirth,
            firstName: userProperty.user.profile.firstName,
            address: userProperty.user.profile?.address,
            lastName: userProperty.user.profile.lastName,
            placeOfBirth: userProperty.user.profile?.placeOfBirth,
            phoneNumber: userProperty.user.mobileNumber,
            gender: userProperty.user.profile.gender,
            property: userProperty.property,
            isAdmin: userProperty.user.is_admin,
            zone: userProperty.user.zone,
          },
          false,
        ),
      );

      Object.assign(account.property, {
        categoryCode: userProperty.property.categoryId,
        unit: Number(userProperty.property.unit),
        licenseKey: userProperty.property?.licenseKey,
      });
    }

    const now = Date.now();
    const durationInMilliseconds = demoCode.duration * 24 * 60 * 60 * 1000;

    const duration = new Date(now + durationInMilliseconds);

    await this.commandBus.execute(
      new CreateDemoCommand(account, order, duration),
    );

    try {
      const internalProp = await this.prisma.property.findFirst({
        where: {
          crmPropertyId: userProperty.userProperty.propertyId,
        },
      });

      await this.prisma.demoCodeDetail.update({
        where: {
          id: demoCode.DemoCodeDetail[0].id,
        },
        data: {
          status: 'used',
          propertyId: internalProp.id,
        },
      });
    } catch (error) {
      console.error(error.message);
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Something went wrong',
      });
    }

    return { message: 'demo code successfully applied' };
  }
}
