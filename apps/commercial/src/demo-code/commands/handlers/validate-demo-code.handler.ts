import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { ValidateDemoCodeCommand } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { PropertyService } from '../../../membership-client/property.service';
import { OrderService } from '../../../membership-client/order.service';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { firstValueFrom } from 'rxjs';

@CommandHandler(ValidateDemoCodeCommand)
export class ValidateDemoCodeHandler
  implements ICommandHandler<ValidateDemoCodeCommand>
{
  constructor(
    private prisma: PrismaService,
    private propertyService: PropertyService,
  ) {}

  async execute(command: ValidateDemoCodeCommand) {
    const { args } = command;

    const demoCode = await this.prisma.demoCode.findFirst({
      where: {
        code: args.code,
      },
      include: {
        DemoCodeDetail: {
          where: { status: 'available' },
        },
        plans: true,
      },
    });

    if (!demoCode) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Demo-code not found',
      });
    }

    if (demoCode.DemoCodeDetail.length === 0) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Demo-code max limit usage',
      });
    }

    const demoDetail = demoCode.DemoCodeDetail[0];
    if (demoDetail.status === 'used') {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code already used',
      });
    }

    const currentDate = new Date();
    if (currentDate > demoCode.expiredAt) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code has expired',
      });
    }

    const userProperty = await firstValueFrom(
      this.propertyService.getPropertyUser(args.propertyId),
    );

    const existingDemoDetail = await this.prisma.demoCodeDetail.findFirst({
      where: {
        propertyId: userProperty.property.id,
      },
    });

    if (existingDemoDetail) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Demo-code already exists with this property',
      });
    }

    const onePlan = await this.prisma.plan.findMany({
      where: {
        id: { in: demoCode.plans.map((plan) => plan.planId) },
        subfolderId: userProperty.property.industryPlan,
        type: 'trial',
      },
    });

    if (!onePlan) {
      throw new RpcException({
        code: status.INVALID_ARGUMENT,
        message: 'Industry tier not match',
      });
    }

    return { message: 'ok' };
  }
}
