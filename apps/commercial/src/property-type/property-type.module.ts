import { Module } from '@nestjs/common';
import { PropertyTypeController } from './property-type.controller';
import { CqrsModule } from '@nestjs/cqrs';
import { PropertyTypeQueryHandlers } from './queries';
import { IntegrationModule } from '../integration/integration.module';

@Module({
  imports: [CqrsModule, IntegrationModule],
  controllers: [PropertyTypeController],
  providers: [...PropertyTypeQueryHandlers],
})
export class PropertyTypeModule {}
