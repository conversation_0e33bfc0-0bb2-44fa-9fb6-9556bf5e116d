import { GetOnePropertyTypeHandler } from './get-one-property-type.handler';
import { GetOnePropertySubLicenseHandler } from './get-property-sub-license.handler';
import { GetOnePropertySubHandler } from './get-property-sub.handler';
import { GetPropertyTypeInHandler } from './get-property-type-in.handler';
import { GetPropertyTypeHandler } from './get-property-type.handler';
import { GetPropertyTypesHandler } from './get-property-types.handler';
import { ListPropertyTypesHandler } from './list-property-types.handler';

export const PropertyTypeQueryHandlers = [
  Get<PERSON><PERSON>tyType<PERSON>and<PERSON>,
  GetPropertyTypesHandler,
  ListPropertyTypesHandler,
  GetOnePropertyTypeHandler,
  GetOnePropertySubHandler,
  GetOnePropertySubLicenseHandler,
  GetPropertyTypeInHandler,
];
