import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { ListPropertyTypes } from '../impl';
import { PrismaService } from 'libs/prisma';
import { ListPropertyTypesResponse } from '@app/proto-schema/index.internal';
import { Pagination, PaginatedResult, TreeView } from '@app/common';
import { Prisma, PropertyType } from '@prisma/client';

@QueryHandler(ListPropertyTypes)
export class ListPropertyTypesHandler
  implements IQueryHandler<ListPropertyTypes>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: ListPropertyTypes) {
    const { args } = query;
    const search = args.query || '';

    const item: PaginatedResult<PropertyType> = await Pagination<
      PropertyType,
      Prisma.PropertyTypeFindManyArgs
    >(this.prisma.propertyType, {
      where: {
        AND: [
          { name: { contains: search, mode: 'insensitive' } },
          { visibility: true },
          { parentId: null },
        ],
      },
      include: {
        children: {
          orderBy: [{ sequence: 'asc' }],
        },
      },
      orderBy: [{ sequence: 'asc' }],
    });

    type PropertyTypeWithChildren = PropertyType & {
      children?: PropertyType[];
    };

    item.data = item.data.map((property) => ({
      ...property,
      children: (property as PropertyTypeWithChildren).children || [],
    }));

    (item.data as PropertyTypeWithChildren[]).sort((a, b) => {
      if (
        (a.sequence === null && b.sequence === null) ||
        (a.sequence !== null && b.sequence !== null)
      ) {
        if (a.sequence !== null && b.sequence !== null) {
          return a.sequence - b.sequence;
        }

        return 0;
      }

      return a.sequence === null ? 1 : -1;
    });

    item.data = (item.data as PropertyTypeWithChildren[]).map((property) => {
      if (property.children && property.children.length > 0) {
        property.children.sort((a, b) => {
          if (
            (a.sequence === null && b.sequence === null) ||
            (a.sequence !== null && b.sequence !== null)
          ) {
            if (a.sequence !== null && b.sequence !== null) {
              return a.sequence - b.sequence;
            }
            return 0;
          }
          return a.sequence === null ? 1 : -1;
        });
      }
      return property;
    });

    const treeViewResult = TreeView(item.data);

    return {
      ...item,
      data: treeViewResult,
    };
  }
}
