import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPropertyTypeQuery } from '../impl';

@QueryHandler(GetPropertyTypeQuery)
export class GetPropertyTypeHandler
  implements IQueryHandler<GetPropertyTypeQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyTypeQuery) {
    const { id } = query;
    const item = await this.prisma.propertyType.findFirst({
      where: { id: id, visibility: true },
    });
    if (!item) {
      throw new NotFoundException();
    }
    return item;
  }
}
