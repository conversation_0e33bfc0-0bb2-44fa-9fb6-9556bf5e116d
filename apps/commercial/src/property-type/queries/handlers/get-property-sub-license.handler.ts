import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOnePropertySubLicense } from '../impl';
import exp from 'constants';
import { PrismaService } from 'libs/prisma';
import { Pagination } from '@app/common';
import { Prisma, PropertyType } from '@prisma/client';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetOnePropertySubLicense)
export class GetOnePropertySubLicenseHandler
  implements IQueryHandler<GetOnePropertySubLicense>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOnePropertySubLicense) {
    const { id, code } = query;

    const item = await Pagination<
      PropertyType,
      Prisma.PropertyTypeFindManyArgs
    >(this.prisma.propertyType, {
      where: {
        parentId: id,
        categoryCode: code,
        type: 'license',
        visibility: true,
      },
      orderBy: { sequence: 'asc' },
      include: { size: true, media: true, children: true },
    });

    if (!item) {
      throw new NotFoundException();
    }

    item.data = item.data.map((property) => ({
      ...property,
      children: (property as { children?: PropertyType[] }).children || [],
      hasChild:
        (property as { children?: PropertyType[] }).children?.length > 0,
    }));

    return item;
  }
}
