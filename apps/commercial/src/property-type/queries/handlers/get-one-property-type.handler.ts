import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOnePropertyType } from '../impl';
import { PrismaService } from 'libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GetOnePropertyType)
export class GetOnePropertyTypeHandler
  implements IQueryHandler<GetOnePropertyType>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOnePropertyType) {
    const { id } = query;
    const item = await this.prisma.propertyType.findFirst({
      where: { id: id, visibility: true },
      include: { children: true },
    });

    if (!item) {
      throw new NotFoundException();
    }

    return {
      ...item,
      hasChild: item.children.length > 0,
    };
  }
}
