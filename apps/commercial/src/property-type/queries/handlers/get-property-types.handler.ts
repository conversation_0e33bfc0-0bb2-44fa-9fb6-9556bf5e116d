import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPropertyTypesQuery } from '../impl';

@QueryHandler(GetPropertyTypesQuery)
export class GetPropertyTypesHandler
  implements IQueryHandler<GetPropertyTypesQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyTypesQuery) {
    const { args } = query;
    const search = args.search || '';
    const items = await this.prisma.propertyType.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        icon: true,
        categoryCode: true,
        visibility: true,
      },
      where: {
        AND: [
          { name: { contains: search, mode: 'insensitive' } },
          { parentId: { not: null } },
          { visibility: true },
        ],
      },
    });
    return items;
  }
}
