import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertyTypeInQuery } from '../impl/get-property-type-in.query';
import { PrismaService } from 'libs/prisma';
import { GetPropertyTypeInResponse } from '@app/proto-schema/index.internal';

@QueryHandler(GetPropertyTypeInQuery)
export class GetPropertyTypeInHandler
  implements IQueryHandler<GetPropertyTypeInQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPropertyTypeInQuery): Promise<any> {
    const { propertyTypeIds } = query;

    const data = await this.prisma.propertyType.findMany({
      where: {
        id: { in: propertyTypeIds.id },
        visibility: true,
      },
    });

    return {
      data: data.map((type) => ({
        id: type.id,
        name: type.name,
      })),
    };
  }
}
