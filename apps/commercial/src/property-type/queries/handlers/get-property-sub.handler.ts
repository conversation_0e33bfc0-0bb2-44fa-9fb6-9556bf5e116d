import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetOnePropertySub } from '../impl';
import { PrismaService } from 'libs/prisma';
import { NotFoundException } from '@nestjs/common';
import { Prisma, PropertyType } from '@prisma/client';
import { Pagination } from '@app/common';
import { IntegrationService } from '../../../integration/integration.service';

@QueryHandler(GetOnePropertySub)
export class GetOnePropertySubHandler
  implements IQueryHandler<GetOnePropertySub>
{
  constructor(
    private prisma: PrismaService,
    private integrationService: IntegrationService,
  ) {}

  async execute(query: GetOnePropertySub) {
    const { id, businessType } = query;
    console.log('[GetOnePropertySub] Query params:', { id, businessType });

    const item = await Pagination<
      PropertyType,
      Prisma.PropertyTypeFindManyArgs
    >(this.prisma.propertyType, {
      where: {
        parentId: id,
        type: 'plan',
        visibility: true,
        planSubfolder: {
          some: {
            AND: [
              { isActive: true },
              { publish: true },
              { businessType: businessType },
            ],
          },
        },
      },
      orderBy: { sequence: 'asc' },
      include: {
        size: true,
        media: true,
      },
    });

    if (!item) {
      console.log('[GetOnePropertySub] No items found');
      throw new NotFoundException();
    }

    item.data = await Promise.all(
      item.data.map(async (property) => {
        console.log('[GetOnePropertySub] Processing property:', {
          id: property.id,
          categoryCode: property.categoryCode,
        });

        const categoryDetail =
          await this.integrationService.DetailCategoryByCode(
            property.categoryCode,
          );

        console.log('[GetOnePropertySub] Category details:', {
          categoryCode: property.categoryCode,
          details: categoryDetail,
        });

        return {
          ...property,
          rangeLabel: categoryDetail.rangeLabel,
          unitLabel: categoryDetail.unitLabel,
          children: (property as { children?: PropertyType[] }).children || [],
          hasChild:
            (property as { children?: PropertyType[] }).children?.length > 0,
        };
      }),
    );

    return item;
  }
}
