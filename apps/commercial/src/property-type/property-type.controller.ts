import { <PERSON>, Get, Param, Query } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';
import { FilterPropertyTypeDto } from './dto/filter-property-type.dto';
import {
  GetOnePropertySub,
  GetOnePropertySubLicense,
  GetOnePropertyType,
  GetPropertyTypeInQuery,
  GetPropertyTypeQuery,
  GetPropertyTypesQuery,
  ListPropertyTypes,
} from './queries';
import {
  GetPropertySubLicenseRequest,
  GetPropertyTypeInRequest,
  GetPropertyTypeInResponse,
  GetPropertySubRequest,
  ListPropertyTypesResponse,
  PROPERTY_TYPE_SERVICE_NAME,
  PropertyType,
} from '@app/proto-schema/index.internal';
import { Query as CommonQuery, Id } from '@app/proto-schema/index.common';
import { Metadata } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { GrpcMethod } from '@nestjs/microservices';

// @ApiTags('Property Type')
@Controller('property-type')
export class PropertyTypeController {
  constructor(private queryBus: QueryBus) {}

  // @Get()
  // findAll(@Query() filter: FilterPropertyTypeDto) {
  //   return this.queryBus.execute(new GetPropertyTypesQuery(filter));
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.queryBus.execute(new GetPropertyTypeQuery(id));
  // }

  @GrpcMethod(PROPERTY_TYPE_SERVICE_NAME, 'listPropertyTypes')
  listPropertyTypes(
    request: CommonQuery,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<ListPropertyTypesResponse>
    | Observable<ListPropertyTypesResponse>
    | ListPropertyTypesResponse {
    return this.queryBus.execute(new ListPropertyTypes(request));
  }

  @GrpcMethod(PROPERTY_TYPE_SERVICE_NAME, 'getPropertyType')
  getOnePropertyTypes(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyType> | Observable<PropertyType> | PropertyType {
    return this.queryBus.execute(new GetOnePropertyType(request.id));
  }

  @GrpcMethod(PROPERTY_TYPE_SERVICE_NAME, 'getPropertySub')
  getOnePropertySub(
    request: GetPropertySubRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyType> | Observable<PropertyType> | PropertyType {
    return this.queryBus.execute(
      new GetOnePropertySub(request.id, request.businessType),
    );
  }

  @GrpcMethod(PROPERTY_TYPE_SERVICE_NAME, 'getPropertySubLicense')
  getOnePropertySubLicense(
    request: GetPropertySubLicenseRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<PropertyType> | Observable<PropertyType> | PropertyType {
    const { id, code } = request;
    return this.queryBus.execute(new GetOnePropertySubLicense(id, code));
  }

  @GrpcMethod(PROPERTY_TYPE_SERVICE_NAME, 'getPropertyTypeIn')
  getPropertyTypeIn(
    request: GetPropertyTypeInRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<GetPropertyTypeInResponse>
    | Observable<GetPropertyTypeInResponse>
    | GetPropertyTypeInResponse {
    return this.queryBus.execute(new GetPropertyTypeInQuery(request));
  }
}
