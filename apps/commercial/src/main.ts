import {
  ContextInterceptor,
  FormatCsrfHeaderInterceptor,
  validateErrorFormat,
} from '@app/common';
import {
  BadRequestException,
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory, Reflector } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { ValidationError, useContainer } from 'class-validator';
import compression from 'compression';
import { PrismaService } from 'libs/prisma';
import { join } from 'path';
import { cwd } from 'process';
import { RedisIoAdapter } from './adapters/redis-io.adapter';
import { CommercialModule } from './commercial.module';
import {
  MicroserviceOptions,
  RedisOptions,
  Transport,
} from '@nestjs/microservices';
import { NestMicroserviceOptions } from '@nestjs/common/interfaces/microservices/nest-microservice-options.interface';
import { AppClusterService } from './cluster.service';
import { ReflectionService } from '@grpc/reflection';
import { TrimPipe } from '@app/common/helpers/trims.helper';
import cookieParser from 'cookie-parser';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore: Unreachable code error
BigInt.prototype.toJSON = function () {
  const int = Number.parseInt(this.toString());
  return int ?? this.toString();
};

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(
    CommercialModule,
    {
      logger: ['error', 'warn'],
    },
  );

  const configService = app.get(ConfigService);
  const redisHost = configService.get<string>('REDIS_HOST');
  const redisPort = configService.get<string>('REDIS_PORT');
  const redisPass = configService.get<string>('REDIS_PASS');
  const grpcUrl = configService.get<string>('GRPC_URL');

  useContainer(app.select(CommercialModule), { fallbackOnErrors: true });
  app.enableCors({
    origin: [
      configService.get<string>('LOCAL_CLIENT_URL'),
      configService.get<string>('CLIENT_URL'),
    ],
    credentials: true,
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Csrf-Token',
      'x-csrf-token',
      'x-forwarded-for',
      'user-agent',
      'Accept',
      'Origin',
      'X-Requested-With',
      'credentials',
    ],
    methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  });

  app.use(compression());
  app.useStaticAssets(join(cwd(), 'storage'));
  app.enableVersioning({
    defaultVersion: '1',
    type: VersioningType.URI,
  });
  app.useGlobalInterceptors(new ContextInterceptor());
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.useGlobalPipes(new TrimPipe());
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: false,
      },
      whitelist: true,
      validateCustomDecorators: true,
      forbidUnknownValues: false,
      stopAtFirstError: true,
      exceptionFactory: (errors: ValidationError[]) => {
        const messages = validateErrorFormat(errors);
        return new BadRequestException(messages);
      },
    }),
  );

  app.use(cookieParser());
  // app.useGlobalInterceptors(new FormatCsrfHeaderInterceptor());

  const config = new DocumentBuilder()
    .setTitle('Velodiva Commercial API')
    .setDescription('The Velodiva Commercial API')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document, {
    swaggerOptions: {
      tagsSorter: 'alpha',
      apisSorter: 'alpha',
      operationsSorter: 'alpha',
      docExpansion: 'none',
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  const redisIoAdapter = new RedisIoAdapter(app);
  await redisIoAdapter.connectToRedis(20000); // Increase from default 2000ms

  app.useWebSocketAdapter(redisIoAdapter);

  const prismaService: PrismaService = app.get(PrismaService);
  prismaService.$on('query', (event) => {
    console.log(event);
  });

  await app.listen(configService.get<string>('APP_COMMERCIAL_PORT'));

  const redisOptions: RedisOptions = {
    transport: Transport.REDIS,
    options: {
      host: redisHost,
      port: Number(redisPort),
      password: redisPass,
    },
  };

  const grpcOptions: MicroserviceOptions = {
    transport: Transport.GRPC,
    options: {
      package: 'internal',
      protoPath: [
        join(cwd(), '_proto/internal-proto/plan.proto'),
        join(cwd(), '_proto/internal-proto/property-type.proto'),
        join(cwd(), '_proto/internal-proto/postal.proto'),
        join(cwd(), '_proto/internal-proto/bundle.proto'),
        join(cwd(), '_proto/internal-proto/license.proto'),
        join(cwd(), '_proto/internal-proto/addon.proto'),
        join(cwd(), '_proto/internal-proto/order-payment.proto'),
        join(cwd(), '_proto/internal-proto/voucher.proto'),
        join(cwd(), '_proto/internal-proto/device.proto'),
        join(cwd(), '_proto/internal-proto/package.proto'),
        join(cwd(), '_proto/internal-proto/auth.proto'),
        join(cwd(), '_proto/internal-proto/property.proto'),
        join(cwd(), '_proto/internal-proto/user.proto'),
        join(cwd(), '_proto/internal-proto/configuration.proto'),
        join(cwd(), '_proto/internal-proto/referral.proto'),
        join(cwd(), '_proto/internal-proto/job-position.proto'),
        join(cwd(), '_proto/internal-proto/partnership.proto'),
        join(cwd(), '_proto/internal-proto/demo-code.proto'),
        join(cwd(), '_proto/internal-proto/track.proto'),
        join(cwd(), '_proto/internal-proto/zone-type.proto'),
        join(cwd(), '_proto/internal-proto/jingle.proto'),
        join(cwd(), '_proto/internal-proto/monitor.proto'),
        join(cwd(), '_proto/internal-proto/product.proto'),
        join(cwd(), '_proto/internal-proto/product-variant.proto'),
      ],
      url: grpcUrl,
      onLoadPackageDefinition: (pkg, server) => {
        new ReflectionService(pkg).addToServer(server);
      },
      loader: {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [join(cwd(), '_proto')],
      },
    },
  };

  app.connectMicroservice<RedisOptions>(redisOptions);
  app.connectMicroservice(grpcOptions);
  app.use((req, res, next) => {
    res.removeHeader('X-Powered-By');
    next();
  });
  await app.startAllMicroservices();
}
bootstrap();
//AppClusterService.clusterize(bootstrap);
