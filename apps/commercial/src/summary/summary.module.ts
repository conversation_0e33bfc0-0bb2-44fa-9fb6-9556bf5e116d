import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';

import { SummaryQueryHandlers } from './queries';
import { SummaryController } from './summary.controller';
import { GmiModule } from '../gmi/gmi.module';
import { CacheModule } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import { createKeyv } from '@keyv/redis';
import { Keyv } from 'keyv';
import { CacheableMemory } from 'cacheable';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    //CacheModule.register({
    //  store: redisStore as unknown as CacheStoreFactory,
    //  host: '*************',
    //  port: 6379,
    //  password: 'Jaguar123',
    //  ttl: 5,
    //}),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => {
        return {
          stores: [
            new Keyv({
              store: new CacheableMemory({ ttl: 60000, lruSize: 5000 }),
            }),
            createKeyv({
              url: config.get<string>('REDIS')
            }),
          ],
        };
      },
    }),
  ],
  controllers: [SummaryController],
  providers: [...SummaryQueryHandlers],
})
export class SummaryModule {}
