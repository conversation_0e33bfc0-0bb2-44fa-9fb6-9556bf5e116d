import { IQuery } from '@nestjs/cqrs';
import { SummaryDetailPlaylistDto } from '../../dto/summary-detail-playlist';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';

export class DetailSummaryPlaylistQuery implements IQuery {
  constructor(
    public readonly id: string,
    public readonly args: SummaryDetailPlaylistDto,
    public readonly user: ICurrentUser
  ) {}
}
