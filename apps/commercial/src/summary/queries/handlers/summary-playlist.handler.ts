import { PaginationCursor } from '@app/common';
import { ConfigService } from '@nestjs/config';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PlatformType, Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import { PlaylistWithAlbumTrack } from '../../summary.type';
import { SummaryPlaylistQuery } from '../impl';
import { partitionArray } from 'libs/utils/partition-array.util';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { ItemsDto } from 'apps/commercial/src/order-payment/dto/payment-gateway.dto';
import { Logger } from '@nestjs/common';

@QueryHandler(SummaryPlaylistQuery)
export class SummaryPlaylistHandler
  implements IQueryHandler<SummaryPlaylistQuery>
{
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
    private gmiRestsService: GmiRESTService,
  ) {}

  private logger = new Logger(SummaryPlaylistHandler.name);
  private minioEndpoint = this.config.get<string>('MINIO_ENDPOINT');

  async execute(query: SummaryPlaylistQuery) {
    this.logger.debug('ON GOING');

    const { args, user } = query;
    const limitShowCase = args?.showcase ? Number(args.showcase) : 10;
    const limitTrack = args?.track ? Number(args.track) : 5;
    const cursor = args.cursor ? Number(args.cursor) : undefined;

    const baseQuery = `
  WITH parent AS (
    SELECT 
      p.id,
      p."playlistId",
      p."name", 
      p."color",
      p."type",
      p."totalDuration",
      p."propertyId",
      pp."order",
      0 as depth,
      LPAD(pp."order"::text, 3, '0') as sort_path,
      p.id as root_id,
      NULL::int as rn
    FROM "Playlist" p
    JOIN "PlaylistPlatform" pp ON pp."playlistId" = p.id
    JOIN "PlaylistPropertyType" ppt ON ppt."playlistId" = p.id
    WHERE p."playlistId" IS NULL
      AND p."propertyId" IS NULL
      AND p."publish" = true
      AND ppt."propertyTypeId" = $1
      AND pp."platform" = 'WEB'
      ${cursor !== undefined ? 'AND pp."order" > $4::int' : ''}
    ORDER BY pp."order"
    LIMIT $2
  ),

  child_raw AS (
    SELECT 
      p2.id,
      p2."playlistId",
      p2."name", 
      p2."color",
      p2."type",
      p2."totalDuration",
      p2."propertyId",
      pp2."order",
      1 as depth,
      pr.sort_path || '.' || LPAD(pp2."order"::text, 3, '0') AS sort_path,
      pr.id as root_id
    FROM "Playlist" p2
    JOIN "PlaylistPlatform" pp2 ON pp2."playlistId" = p2.id
    JOIN "PlaylistPropertyType" ppt2 ON ppt2."playlistId" = p2.id
    JOIN parent pr ON p2."playlistId" = pr.id
    WHERE pp2."platform" = 'WEB'
  ),

  ranked_child AS (
    SELECT 
      *,
      ROW_NUMBER() OVER (PARTITION BY root_id ORDER BY sort_path) as rn
    FROM child_raw
  ),

  limited_child AS (
    SELECT 
      id,
      "playlistId",
      "name",
      "color",
      "type",
      "totalDuration",
      "propertyId",
      "order",
      depth,
      sort_path,
      root_id,
      rn
    FROM ranked_child
    WHERE rn <= $3
  ),

  combined AS (
    SELECT * FROM parent
    UNION ALL
    SELECT * FROM limited_child
  )

  SELECT 
    id,
    "playlistId",
    "name",
    "color",
    "type",
    "totalDuration",
    "propertyId",
    "order",
    depth,
    sort_path
  FROM combined
  ORDER BY sort_path
`;

    const params: any[] = [user.propertyTypeId, args.take ?? 10, limitShowCase];

    if (cursor !== undefined) {
      params.push(cursor);
    }

    const items = await this.prisma.$queryRawUnsafe<any[]>(
      baseQuery,
      ...params,
    );
    const thumbnails = await this.prisma.playlistPlatformThumbnail.findMany({
      where: {
        playlistPlatform: {
          playlistId: { in: items.map((i) => i.id) },
          platform: 'WEB',
        },
      },
      select: {
        path: true,
        resolution: true,
        fileName: true,
        playlistPlatform: {
          select: {
            playlistId: true,
          },
        },
      },
    });

    const thumbnailMapByPlaylistId = new Map<string, typeof thumbnails>();

    for (const t of thumbnails) {
      const playlistId = t.playlistPlatform.playlistId;
      if (!thumbnailMapByPlaylistId.has(playlistId)) {
        thumbnailMapByPlaylistId.set(playlistId, []);
      }
      thumbnailMapByPlaylistId.get(playlistId)!.push(t);
    }

    const trackListIds = items
      .filter((el) => el.type === 'tracklist' && Number(el.depth) <= 1)
      .map((el) => el.id);

    const playlistTracks = await this.prisma.playlist.findMany({
      where: {
        id: { in: trackListIds },
      },
      select: {
        id: true,
        playlistFollower: {
          select: { activationId: true },
        },
        tracks: {
          select: {
            track: {
              // select: { key: true, source: true },
              select: { key: true },
            },
          },
          take: limitTrack,
        },
      },
    });

    const uniqueTrackKeys = [
      ...new Set(
        playlistTracks
          .flatMap((item) => item.tracks.map((t) => t.track?.key))
          .filter(Boolean),
      ),
    ];

    const gmiIdsPartition = partitionArray(uniqueTrackKeys, 50);

    const allGmiTracksResults = await Promise.all(
      gmiIdsPartition.map((ids) =>
        this.gmiRestsService.getTrackIn(ids, 'showcase'),
      ),
    );

    const allGmiTracks = allGmiTracksResults.flat();
    const validTrackKeys = new Set(allGmiTracks.map((track) => track.id));

    const filteredPlaylistTracks = playlistTracks
      .map((playlist) => ({
        ...playlist,
        tracks: playlist.tracks.filter(
          (t) => t.track?.key && validTrackKeys.has(t.track.key),
        ),
      }))
      .filter((playlist) => playlist.tracks.length > 0);

    const gmiTrackMap = new Map(
      allGmiTracks.map((track) => [track.key || track.id, track]),
    );

    const trackMap = new Map<string, any[]>();
    for (const pt of filteredPlaylistTracks) {
      const tracks = pt.tracks
        .map(({ track }) => {
          if (!track?.key || !validTrackKeys.has(track.key)) {
            return null;
          }

          const gmiTrack = gmiTrackMap.get(track.key);
          // const source = track?.source as any;
          // this.logger.debug(`GMI TRACK  pt. ${pt.id} `, gmiTrack);
          return {
            // ...(typeof source === 'object' &&
            // source !== null &&
            // 'source' in source
            //   ? source.source
            //   : source),
            ...gmiTrack,
            isLike: false,
          };
        })
        .filter(Boolean);

      if (tracks.length > 0) {
        trackMap.set(pt.id, tracks);
      }
    }

    const itemMap = new Map<string, any>();
    const rootItems: any[] = [];

    for (const item of items) {
      const tracks =
        item.type === 'tracklist' ? (trackMap.get(item.id) ?? []) : [];
      const totalDuration = tracks.reduce((sum, track) => {
        const duration =
          typeof track.duration === 'string'
            ? parseInt(track.duration, 10)
            : track.duration;
        return sum + (duration || 0);
      }, 0);

      const playlistTrack = filteredPlaylistTracks.find(
        (pt) => pt.id === item.id,
      );
      const isFollowed =
        item.type === 'tracklist' &&
        playlistTrack?.playlistFollower.some(
          (f) => f.activationId === user.deviceId,
        );

      const thumbnailsForItem = thumbnailMapByPlaylistId.get(item.id) ?? [];

      const thumbnail = thumbnailsForItem.map((t) => ({
        image: `https://${this.minioEndpoint}/${t.path}${t.resolution ? `/${t.resolution}` : ''}/${t.fileName}`,
        resolution: t.resolution ?? '512x512',
      }));

      const node = {
        id: item.id,
        name: item.name,
        type: item.type,
        order: item.order,
        isFollowed,
        // isNameHide: item.isNameHide,
        totalDuration,
        thumbnail: thumbnail,
        uri: `showcase:${item.id}`,
        tracks: Number(item.depth) === 0 ? tracks : [],
        childs: [],
      };

      itemMap.set(item.id, node);

      if (!item.playlistId) {
        rootItems.push(node);
      } else {
        const parent = itemMap.get(item.playlistId);
        if (parent) {
          parent.childs.push(node);
        } else {
          rootItems.push(node);
        }
      }
    }

    this.logger.debug('DONE');
    return {
      meta: {
        nextCursor:
          rootItems.length > 0 ? rootItems[rootItems.length - 1].order : null,
        hasNextPage: rootItems.length >= (args.take ?? 10),
        limit: args.take ?? 10,
        sortType: 'desc',
      },
      data: rootItems,
    };
  }
}
