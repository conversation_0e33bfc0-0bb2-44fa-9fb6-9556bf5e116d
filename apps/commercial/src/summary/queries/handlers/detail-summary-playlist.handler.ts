import { Pagination } from '@app/common';
import { NotFoundException } from '@nestjs/common';
import { IQuery<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { $Enums, Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import {
  PlaylistWithThumbnail,
  TrackOnPlaylistWithTrack,
} from '../../summary.type';
import { DetailSummaryPlaylistQuery } from '../impl';
import { ConfigService } from '@nestjs/config';

@QueryHandler(DetailSummaryPlaylistQuery)
export class DetailSummaryPlaylistHandler
  implements IQueryHandler<DetailSummaryPlaylistQuery>
{
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
  ) {}

  async execute(query: DetailSummaryPlaylistQuery) {
    const { id, args, user } = query;
    const type = await this.prisma.playlist.findFirst({
      where: { id: id },
      include: {
        playlistPlatform: {
          include: {
            playlistPlatformThumbnail: true,
          },
        },
        playlistFollower: {
          select: {
            activationId: true,
          },
        },
      },
    });
    if (!type) {
      throw new NotFoundException();
    }

    if (type.type == $Enums.PlaylistType.tracklist) {
      const searchTerm = `%${args.search}%`;

      const items = await Pagination<any, Prisma.TrackOnPlaylistFindManyArgs>(
        this.prisma.trackOnPlaylist,
        {
          where: {
            AND: [
              { playlist: { id: id } },
              args.search
                ? {
                    track: {
                      id: {
                        in: await this.prisma
                          .$queryRawUnsafe<
                            Array<{ id: string }>
                          >(`SELECT id::TEXT as id FROM "Track" WHERE source->>'title' ILIKE $1`, searchTerm)
                          .then((tracks) => tracks.map((track) => track.id)),
                      },
                    },
                  }
                : {},
            ],
          },
          orderBy: { order: 'asc' },
          include: {
            track: true,
            playlist: {
              include: { playlistFollower: { select: { activationId: true } } },
            },
          },
        },
        { page: args.page, limit: args.limit },
      );

      const trackLikes = await this.prisma.trackLike.findMany({
        where: {
          trackId: { in: items.data.map((item) => item.trackId) },
          activationId: user.deviceId,
        },
      });

      const trackLikeMap = new Map(trackLikes.map((tl) => [tl.trackId, true]));

      const serializeData = items.data.map((item) => {
        const trackSource = item.track.source;
        let result = {};

        if (typeof trackSource === 'object' && trackSource !== null) {
          if ('source' in trackSource) {
            const { source, ...rest } = trackSource as {
              source: Record<string, any>;
            };
            result = { ...source, ...rest };
          } else {
            result = { ...trackSource };
          }
        }
        Object.assign(result, {
          isFollowed:
            item.playlist.type === 'tracklist'
              ? item.playlist.playlistFollower.some(
                  (flw) => flw.activationId === user.deviceId,
                )
              : undefined,
        });

        const isLiked = trackLikeMap.has(item.trackId);
        return {
          ...result,
          name: item.playlist.name,
          uri: `playlist:${item.playlistId}:${item.track.key}`,
          isLike: isLiked,
        };
      });

      return {
        playlist: {
          id: type.id,
          name: type.name,
          description: type.description,
          color: type.color,
          isFollowed:
            type.type === 'tracklist'
              ? type.playlistFollower.some(
                  (flw) => flw.activationId === user.deviceId,
                )
              : undefined,
          type: type.type,
          // order: type.order,
          uri: `playlist:${type.id}`,
          thumbnail: type.playlistPlatform[0].playlistPlatformThumbnail?.length
            ? type.playlistPlatform[0].playlistPlatformThumbnail?.map((t) => ({
                image: `https://${this.config.get<string>('MINIO_ENDPOINT')}/${t.path}/${t.resolution}/${t.fileName}`,
                ratio: t.ratio,
              }))
            : [],
          publish: type.publish,
        },
        child: {
          data: serializeData,
          meta: items.meta,
        },
      };
    } else {
      const items = await Pagination<any, Prisma.PlaylistPlatformFindManyArgs>(
        this.prisma.playlistPlatform,
        {
          where: { playlist: { parent: { id: id } } },
          include: {
            playlistPlatformThumbnail: true,
            playlist: {
              include: { playlistFollower: { select: { activationId: true } } },
            },
          },
          orderBy: { order: 'asc' },
        },
        { page: args.page, limit: args.limit },
      );
      const serializeData = items.data.map((item) => {
        return {
          id: item.playlistId,
          name: item.playlist.name,
          description: item.playlist.description,
          color: item.playlist.color,
          type: item.playlist.type,
          isFollowed:
            item.playlist.type === 'tracklist'
              ? item.playlist.playlistFollower.some(
                  (flw) => flw.activationId === user.deviceId,
                )
              : undefined,
          order: item.order,
          thumbnail: item.playlistPlatformThumbnail.length
            ? item.playlistPlatformThumbnail.map((t) => ({
                image: `https://${this.config.get<string>('MINIO_ENDPOINT')}/${t.path}/${t.resolution}/${t.fileName}`,
                ratio: t.ratio,
              }))
            : [],
          publish: item.playlist.publish,
          uri: `playlist:${item.playlistId}`,
        };
      });

      return {
        playlist: {
          id: type.id,
          name: type.name,
          description: type.description,
          color: type.color,
          type: type.type,
          // order: type.order,
          uri: `showcase:${type.id}`,
          thumbnail: type.playlistPlatform[0].playlistPlatformThumbnail?.length
            ? type.playlistPlatform[0].playlistPlatformThumbnail?.map((t) => ({
                image: `https://${this.config.get<string>('MINIO_ENDPOINT')}/${t.path}/${t.resolution}/${t.fileName}`,
                ratio: t.ratio,
              }))
            : [],
          publish: type.publish,
        },
        child: {
          data: serializeData,
          meta: items.meta,
        },
      };
    }
  }
}
