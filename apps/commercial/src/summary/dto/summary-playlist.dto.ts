import { BaseCursorFilterDto } from '@app/common';
import { ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { IsOptional, IsString, ValidateIf } from 'class-validator';

export class SummaryPlaylistDto extends OmitType(BaseCursorFilterDto, [
  'search',
  'sort',
  'sortType',
] as const) {
  @IsOptional()
  @ApiPropertyOptional()
  @IsString()
  @ValidateIf((prop) => prop.folder != '')
  folder: number;
  @IsOptional()
  @ApiPropertyOptional()
  @IsString()
  @ValidateIf((prop) => prop.showcase != '')
  showcase: number;
  @IsOptional()
  @ApiPropertyOptional()
  @IsString()
  @ValidateIf((prop) => prop.track != '')
  track: number;
}
