import { Prisma } from '@prisma/client';

export type PlaylistWithAlbumTrack = Prisma.PlaylistPlatformGetPayload<{
  include: {
    playlist: {
      include: {
        tracks: { include: { track: true } };
        childs: {
          include: {
            playlistPlatform: {
              include: {
                playlistPlatformThumbnail: true;
                playlist: {
                  include: {
                    // thumbnail: true;
                    tracks: {
                      include: { track: { select: { source: true } } };
                    };
                  };
                };
              };
            };
            playlistFollower: {
              select: {
                activationId: true
              }
            };
          };
        };
        playlistFollower: {
          select: {
            activationId: true
          }
        };
        // thumbnail: true;
      };
    };
    playlistPlatformThumbnail: true;
  };
}>;

export type TrackOnPlaylistWithTrack = Prisma.TrackOnPlaylistGetPayload<{
  include: { track: true; thumbnail: true; playlist: true };
}>;

export type PlaylistWithThumbnail = Prisma.PlaylistPlatformGetPayload<{
  include: { playlistPlatformThumbnail: true; playlist: true };
}>;
