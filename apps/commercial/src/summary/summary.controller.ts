import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { SummaryDetailPlaylistDto } from './dto/summary-detail-playlist';
import { SummaryPlaylistDto } from './dto/summary-playlist.dto';
import { DetailSummaryPlaylistQuery, SummaryPlaylistQuery } from './queries';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { User } from 'apps/admin/src/auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('summary')
@ApiTags('Summary')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SummaryController {
  constructor(private readonly queryBus: QueryBus) {}

  @Get('playlist')
  // @UseInterceptors(CacheInterceptor)
  findAll(@Query() filter: SummaryPlaylistDto, @User() user: ICurrentUser) {
    return this.queryBus.execute(new SummaryPlaylistQuery(filter, user));
  }

  @Get('playlist/:id')
  @UseInterceptors(CacheInterceptor)
  findOnePlaylist(
    @Query() filter: SummaryDetailPlaylistDto,
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return this.queryBus.execute(
      new DetailSummaryPlaylistQuery(id, filter, user),
    );
  }
}
