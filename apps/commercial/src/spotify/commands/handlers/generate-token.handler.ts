import { HttpService } from '@nestjs/axios';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { firstValueFrom } from 'rxjs';
import { GenerateTokenCommand } from '../impl/generate-token.command';
import { ConfigService } from '@nestjs/config';

@CommandHandler(GenerateTokenCommand)
export class GenerateTokenHandler
  implements ICommandHandler<GenerateTokenCommand>
{
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {}

  async execute(command: GenerateTokenCommand) {
    const { code } = command;
    try {
      const reqCode = await firstValueFrom(
        this.httpService.post(
          'https://accounts.spotify.com/api/token',
          {
            grant_type: 'authorization_code',
            code,
            redirect_uri: this.configService.get<string>('SPOTIFY_REDIRECT_URI'),
            client_id: this.configService.get<string>('SPOTIFY_CLIENT_ID'),
            client_secret: this.configService.get<string>('SPOTIFY_CLIENT_SECRET')
          },
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
        )
      );
      return reqCode.data;
    } catch (error) {
      console.log(error)
      return 'failed generate token';
    }
  }
}
