import { HttpService } from '@nestjs/axios';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { firstValueFrom } from 'rxjs';
import { ImportSpotifyPlaylistsCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { PrismaService } from 'libs/prisma';
import { generateRandomColor } from '@app/common';
import { InternalServerErrorException } from '@nestjs/common';

@CommandHandler(ImportSpotifyPlaylistsCommand)
export class ImportSpotifyPlaylistsHandler
  implements ICommandHandler<ImportSpotifyPlaylistsCommand>
{
  constructor(
    private readonly httpService: HttpService,
    private readonly gmiService: GmiRESTService,
    private prisma: PrismaService
  ) {}

  async execute(command: ImportSpotifyPlaylistsCommand) {
    const { user, args } = command;
    try {
      const playlistsTracks = new Map<any, string[]>();

      // fetching user playlists
      for (let i of args.playlistIds) {
        const reqPlaylist = await firstValueFrom(
          this.httpService.get(
            `https://api.spotify.com/v1/playlists/${i}`,
            { 
              headers: {
                'Authorization': `Bearer ${args.accessToken}`,
                'Content-Type':'application/x-www-form-urlencoded',
              }
            }
          )
        );
        // console.log(reqPlaylist.data)

        if (reqPlaylist.data) {
          const tracks: string[] = [];
          if (reqPlaylist.data.tracks.items.length > 0) {
            for (let j of reqPlaylist.data.tracks.items) {
              tracks.push(j?.track.external_ids?.isrc);
            }
          }
          playlistsTracks.set(reqPlaylist.data, tracks);
        }
      }

      for (const [key, value] of playlistsTracks) {
        const res = await this.gmiService.getTracksByIsrcs(value);

        const totalDuration = res.reduce((sum, track) => {
          const duration = track?.duration ? parseInt(track.duration, 10) : 0;
          return sum + duration;
        }, 0);

        await this.prisma.$transaction(async (tr) => {
          const trackOnPlaylistData = [];

          const newPlaylist = await tr.playlist.create({
            data: {
              name: key.name,
              property: { connect: { id: user.propertyId } },
              description: key.description,
              color: generateRandomColor(),
              activation: { connect: { id: user.deviceId } },
              totalDuration,
            },
          });
          for (let idx = 0; idx < res.length; idx++) {
            const elm = res[idx];
            let track = await this.prisma.track.findFirst({
              where: { key: elm.id }, 
            });
  
            if (!track) {  
              track = await tr.track.create({
                data: {
                  key: elm.id,
                  source: elm,
                },
              });
            }
  
            const isTrackInPlaylist = await tr.trackOnPlaylist.findFirst({
              where: { trackId: track.id, playlistId: newPlaylist.id },
            });
            
  
            if (!isTrackInPlaylist) {
              trackOnPlaylistData.push({
                playlistId: newPlaylist.id,
                trackId: track.id,
                order: idx + 1,
              });
            }
          }
          
          await tr.trackOnPlaylist.createMany({
            data: trackOnPlaylistData,
          });
        });
      }
      return 'success import playlist';
    } catch (error) {
      console.log(error)
      throw new InternalServerErrorException('failed import playlist');
    }
  }
}
