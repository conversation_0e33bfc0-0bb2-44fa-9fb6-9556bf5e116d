import { HttpService } from '@nestjs/axios';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { RenewTokenCommand } from '../impl';

@CommandHandler(RenewTokenCommand)
export class RenewTokenHandler
  implements ICommandHandler<RenewTokenCommand>
{
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {}

  async execute(command: RenewTokenCommand) {
    const { user, args } = command;

    try {
      const reqCode = await firstValueFrom(
        this.httpService.post(
          'https://accounts.spotify.com/api/token',
          {
            grant_type: 'refresh_token',
            code: args.refreshToken,
            redirect_uri: this.configService.get<string>('SPOTIFY_REDIRECT_URI'),
            client_id: this.configService.get<string>('SPOTIFY_CLIENT_ID'),
            client_secret: this.configService.get<string>('SPOTIFY_CLIENT_SECRET')
          },
          { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } }
        )
      );
      return reqCode.data;
    } catch (error) {
      console.log(error)
      return 'failed generate token';
    }
  }
}
