import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  GenerateTokenCommand,
  ImportSpotifyPlaylistsCommand,
  RenewTokenCommand,
} from './commands';
import {
  GetAuthUrlQuery,
  GetSpotifyPlaylistQuery,
  IntervalHistoryQuery,
} from './queries/impl';
import { Throttle } from '@nestjs/throttler';
import { RenewTokenDto } from './dto/renew-token.dto';
import { ImportSpotifyPlaylistsDto } from './dto/import-playlist.dto';
import { Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('spotify')
@ApiTags('Spotify')
export class SpotifyController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
    private configService: ConfigService,
  ) {}

  //@Post('token')
  //@Throttle({ default: { limit: 20, ttl: 60000 } })
  //@ApiBearerAuth()
  //@UseGuards(JwtGuard, StatusSuspendedGuard)
  //generateToken(@User() user: ICurrentUser) {
  //  return this.commandBus.execute(new SpotifyTokenCommand(user));
  //}

  //@Get('interval')
  //intervalHistory() {
  //  return this.queryBus.execute(new IntervalHistoryQuery());
  //}

  //@Post('token')
  //@Throttle({ default: { limit: 20, ttl: 60000 } })
  //@ApiBearerAuth()
  //@UseGuards(JwtGuard, StatusSuspendedGuard)
  //generateAccessToken(@User() user: ICurrentUser) {
  //  return this.commandBus.execute(new GenerateTokenCommand(user));
  //}

  @Post('refresh')
  @ApiBearerAuth()
  @UseGuards(JwtGuard, StatusSuspendedGuard)
  async refresh(@User() user: ICurrentUser, @Body() payload: RenewTokenDto) {
    return await this.commandBus.execute(new RenewTokenCommand(user, payload));
  }

  @Post('import-playlists')
  @ApiBearerAuth()
  @UseGuards(JwtGuard, StatusSuspendedGuard)
  async import(
    @User() user: ICurrentUser,
    @Body() payload: ImportSpotifyPlaylistsDto,
  ) {
    return await this.commandBus.execute(
      new ImportSpotifyPlaylistsCommand(user, payload),
    );
  }

  @Get('auth')
  @ApiBearerAuth()
  @UseGuards(JwtGuard, StatusSuspendedGuard)
  authUrl(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetAuthUrlQuery());
  }

  @Get('callback')
  async callback(@Query('code') code: string, @Res() res: Response) {
    const { access_token, refresh_token } = await this.commandBus.execute(
      new GenerateTokenCommand(code),
    );
    return res.redirect(
      `${this.configService.get<string>('SPOTIFY_CLIENT_REDIRECT_URL')}/playlist?accessToken=${access_token}&refreshToken=${refresh_token}`,
    );
  }

  @Get('playlists')
  @ApiBearerAuth()
  @UseGuards(JwtGuard, StatusSuspendedGuard)
  getPlaylists(
    @User() user: ICurrentUser,
    @Query('accessToken') accessToken: string,
  ) {
    return this.queryBus.execute(new GetSpotifyPlaylistQuery(accessToken));
  }
}
