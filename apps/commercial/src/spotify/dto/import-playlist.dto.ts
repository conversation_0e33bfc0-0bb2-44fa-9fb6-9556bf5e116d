import { ApiProperty } from "@nestjs/swagger";
import { <PERSON><PERSON>rray, IsNotEmpty, IsString, ArrayMinSize } from "class-validator";
import { Type } from "class-transformer";

export class ImportSpotifyPlaylistsDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  accessToken: string;

  @ApiProperty({ type: [String], description: "Array of playlist IDs" })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @IsString({ each: true })
  @Type(() => String)
  playlistIds: string[];
}
