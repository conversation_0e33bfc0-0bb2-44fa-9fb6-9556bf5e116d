import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { SpotifyCommandHandlers } from './commands';
import { SpotifyQueryHandlers } from './queries/handlers';
import { SpotifyController } from './spotify.controller';
import { GmiModule } from '../gmi/gmi.module';

@Module({
  imports: [
    CqrsModule,
    HttpModule,
    GmiModule,
  ],
  controllers: [SpotifyController],
  providers: [...SpotifyCommandHandlers, ...SpotifyQueryHandlers],
})
export class SpotifyModule {}
