import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetSpotifyPlaylistQuery } from '../impl';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';

@QueryHandler(GetSpotifyPlaylistQuery)
export class GetSpotifyPlaylistHandler
  implements IQueryHandler<GetSpotifyPlaylistQuery>
{
  constructor(
    private readonly httpService: HttpService,
  ) {}

  async execute(query: GetSpotifyPlaylistQuery) {
    const { accessToken } = query;
    try {
      const resp = await firstValueFrom(
        this.httpService.get<any>('https://api.spotify.com/v1/me/playlists', {
          headers: { Authorization: `Bearer ${accessToken}` },
        })
      );
      
      return resp.data;
    } catch (error) {
      return 'failed get spotify playlists';
    }
  }
}
