import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetAuthUrlQuery } from '../impl';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GetAuthUrlQuery)
export class GetAuthUrlHandler
  implements IQueryHandler<GetAuthUrlQuery>
{
  constructor(
    private prisma: PrismaService,
    private configService: ConfigService
  ) {}

  async execute(query: GetAuthUrlQuery) {
    const scopes = encodeURIComponent(this.configService.get<string>('SPOTIFY_SCOPES'));
    return `https://accounts.spotify.com/authorize?client_id=${this.configService.get<string>('SPOTIFY_CLIENT_ID')}&response_type=code&redirect_uri=${this.configService.get<string>('SPOTIFY_REDIRECT_URI')}&scope=${scopes}`;
  }
}
