import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { IntervalHistoryQuery } from '../impl';

@QueryHandler(IntervalHistoryQuery)
export class IntervalHistoryHandler
  implements IQueryHandler<IntervalHistoryQuery>
{
  constructor(private prisma: PrismaService) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async execute(query: IntervalHistoryQuery) {
    const item = await this.prisma.setting.findFirst({
      where: { type: 'history' },
    });
    if (!item) {
      return {
        interval: 0,
      };
    }
    return item.option;
  }
}
