import { <PERSON>, Get, Param, Patch, UseGuards } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { ReadScheduleReminderCommand } from './commands';
import { GetScheduleRemindersQuery } from './queries';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('schedule-reminder')
@ApiTags('Schedule Reminder')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class ScheduleReminderController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  @Get()
  findAll(@User() user: ICurrentUser) {
    return this.queryBus.execute(new GetScheduleRemindersQuery(user));
  }

  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Schedule)
  @Patch(':id/read')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  update(@User() user: ICurrentUser, @Param('id') id: string) {
    return this.commandBus.execute(new ReadScheduleReminderCommand(user, id));
  }
}
