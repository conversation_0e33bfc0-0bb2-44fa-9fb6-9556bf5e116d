import { CommandBus, EventsHandler, IEventHandler } from '@nestjs/cqrs';
import { UpdatedScheduleReminderEvent } from '../impl';

@EventsHandler(UpdatedScheduleReminderEvent)
export class UpdatedScheduleReminderHandler
  implements IEventHandler<UpdatedScheduleReminderEvent>
{
  constructor(private commandBus: CommandBus) {}
  async handle(event: UpdatedScheduleReminderEvent) {
    const { args } = event;
    return;
  }
}
