import { <PERSON><PERSON>us, <PERSON><PERSON><PERSON>ler, IEventHandler } from '@nestjs/cqrs';
import { PlayerGateway } from 'apps/commercial/src/events/player.gateway';
import { PrismaService } from 'libs/prisma';
import { CreatedScheduleReminderEvent } from '../impl';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';

@EventsHandler(CreatedScheduleReminderEvent)
export class CreatedScheduleReminderHandler
  implements IEventHandler<CreatedScheduleReminderEvent>
{
  constructor(
    private commandBus: CommandBus,
    private playerGateway: PlayerGateway,
    private prisma: PrismaService,
    @Inject('mobile') private client: ClientProxy,
  ) {}
  async handle(event: CreatedScheduleReminderEvent) {
    const { itemId, playNow, zoneId, propertyId, type } = event;

    const item = await this.prisma.scheduleReminder.findFirst({
      where: { AND: [{ id: itemId }, { isRead: false }] },
      include: {
        schedule: {
          include: {
            contents: {
              include: {
                playlist: {
                  include: {
                    tracks: {
                      include: { track: true }, 
                      orderBy: { order: 'asc' },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (item) {
      if (playNow) {
        await this.prisma.scheduleReminder.update({
          where: {
            id: item.id,
          },
          data: {
            isRead: true,
          },
        });
      }

      const activations = await this.prisma.activation.findMany({
        where: {
          id: {
            in: zoneId
          }
        },
        include: {
          activationDevice: {
            where: {
              isActive: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            include: {
              device: {
                select: {
                  type: true
                }
              }
            }
          }
        }
      });

      for (let activation of activations) {
        switch (activation.activationDevice[0].device.type) {
          case 'web':
            const playlist = {
              id: item?.schedule?.contents?.playlist?.id,
              uri: `playlist:${item?.schedule?.contents?.playlist?.id}`,
              name: item?.schedule?.contents?.playlist?.name,
              color: item?.schedule?.contents?.playlist?.color,
              description: item?.schedule?.contents?.playlist?.description,
            };
            const payload = {
              id: item?.id,
              scheduleId: item?.schedule?.id,
              deviceIds: [activation.id],
              mode: item?.schedule?.mode,
              volume: item.schedule.setVolume ? item.schedule.volume : undefined,
              modeValue: item?.schedule?.modeValue,
              startTime: item?.schedule?.startTime,
              repeadEnd: item?.schedule?.repeatEnd,
              playMode: item?.schedule?.playMode,
              endTime: item?.schedule?.endTime,
              playNow: playNow,
              playlist: item?.schedule?.contents?.playlist ? playlist : null,
              tracks:
                item?.schedule?.contents?.playlist?.tracks.map((track) => {
                  return track?.track?.source;
                  // return sc['uri'];
                }) || [],
            };
      
            this.playerGateway.server.to(zoneId).emit('schedule:reminder', payload);
            break;
          case 'mob':
            this.client.emit('schedule-reminder-mob', {
              property_id: propertyId,
              data: {
                zoneId: [activation.id],
                schedule_id: item?.schedule?.id,
                playlist_id: item?.schedule?.contents?.playlist?.id,
                uri: `playlist:${item?.schedule?.contents?.playlist?.id}`,
                playlist_name: item?.schedule?.contents?.playlist?.name,
                type: type,
                volume: item.schedule.setVolume ? item.schedule.volume : undefined,
                message:
                  type === 'reminder'
                    ? 'The upcoming schedule is about to begin shortly'
                    : undefined,
                // start_time: this.convertToISO8601(item?.schedule?.startTime),
                // end_time: this.convertToISO8601(item?.schedule?.endTime),
                start_time: item?.schedule?.startTime,
                playMode: item?.schedule?.playMode,
                end_time: item?.schedule?.endTime,
                repeat_end: item?.schedule?.repeatEnd,
                tracks:
                  item?.schedule?.contents?.playlist?.tracks.map((track) => {
                    return track?.track?.source;
                    // return sc['uri'];
                  }) || [],
              },
            });
            break;
          case 'atv':
            this.client.emit('schedule-reminder-atv', {
              property_id: propertyId,
              data: {
                zoneId: [activation.id],
                schedule_id: item?.schedule?.id,
                playlist_id: item?.schedule?.contents?.playlist?.id,
                playlist_name: item?.schedule?.contents?.playlist?.name,
                uri: `playlist:${item?.schedule?.contents?.playlist?.id}`,
                playMode: item?.schedule?.playMode,
                type: type,
                volume: item.schedule.setVolume ? item.schedule.volume : undefined,
                message:
                  type === 'reminder'
                    ? 'The upcoming schedule is about to begin shortly'
                    : undefined,
                // start_time: this.convertToISO8601(item?.schedule?.startTime),
                // end_time: this.convertToISO8601(item?.schedule?.endTime),
                start_time: item?.schedule?.startTime,
                end_time: item?.schedule?.endTime,
                repeat_end: item?.schedule?.repeatEnd,
                tracks:
                  item?.schedule?.contents?.playlist?.tracks.map((track) => {
                    return track?.track?.source;
                    // return sc['uri'];
                  }) || [],
              },
            });
            break;
          default:
            break;
        }
      }
    }
  }
}
