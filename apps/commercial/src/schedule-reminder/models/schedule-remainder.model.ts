import { AggregateRoot } from '@nestjs/cqrs';
import {
  CreatedScheduleReminderEvent,
  UpdatedScheduleReminderEvent,
} from '../events';

export class ScheduleReminderModel extends AggregateRoot {
  constructor(private itemId: string) {
    super();
    this.autoCommit = true;
  }

  ScheduleReminderCreated(
    propertyId: string,
    playNow: boolean,
    zoneId: any[],
    type: string,
  ) {
    this.apply(
      new CreatedScheduleReminderEvent(
        this.itemId,
        propertyId,
        zoneId,
        type,
        playNow,
      ),
    );
  }
  ScheduleReminderUpdated() {
    this.apply(new UpdatedScheduleReminderEvent(''));
  }
}
