import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { DeleteScheduleReminderCommand } from '../impl';

@CommandHandler(DeleteScheduleReminderCommand)
export class DeleteScheduleReminderHandler
  implements ICommandHandler<DeleteScheduleReminderCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: DeleteScheduleReminderCommand) {
    const { user, id } = command;
    const item = await this.prisma.scheduleReminder.findFirst({
      where: {
        AND: [{ id: id }, { schedule: { AND: [ { property: { id: user.propertyId } }, { activation: { id: user.deviceId } } ] } }],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.scheduleReminder.delete({
        where: { id: item.id },
      });
      return 'successfully create <PERSON>minder';
    } catch (error) {
      return 'failed create Reminder';
    }
  }
}
