import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';

import { ScheduleReminderModel } from '../../models/schedule-remainder.model';
import { CreateScheduleReminderCommand } from '../impl';

@CommandHandler(CreateScheduleReminderCommand)
export class CreateScheduleReminderHandler
  implements ICommandHandler<CreateScheduleReminderCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
  ) {}

  async execute(command: CreateScheduleReminderCommand) {
    const { args } = command;
    try {
      const zones = args.zonesId.map((act) => act.activationId);
      const item = await this.prisma.$transaction(async (tr) => {
        await tr.scheduleReminder.updateMany({
          where: {
            AND: [
              {
                schedule: {
                  AND: [
                    {
                      property: { id: args.propertyId },
                    },
                    {
                      id: args.scheduleId,
                    },
                    {
                      scheduleZone: { some: { activationId: { in: zones } } },
                    },
                    // {
                    //   activation: { id: { in: zones } }
                    // }
                  ],
                },
              },
              { isRead: false },
            ],
          },
          data: { isRead: true },
        });

        return await tr.scheduleReminder.create({
          data: { schedule: { connect: { id: args.scheduleId } } },
        });
      });

      const scheduleReminderModel = this.publisher.mergeClassContext(
        ScheduleReminderModel,
      );
      const model = new scheduleReminderModel(item.id);
      model.ScheduleReminderCreated(args.propertyId, false, zones, args.type);
      return 'successfully create reminder';
    } catch (error) {
      return 'failed create reminder';
    }
  }
}
