import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { ReadScheduleReminderCommand } from '../impl';

@CommandHandler(ReadScheduleReminderCommand)
export class ReadScheduleReminderHandler
  implements ICommandHandler<ReadScheduleReminderCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: ReadScheduleReminderCommand) {
    const { id, user } = command;
    const item = await this.prisma.scheduleReminder.findFirst({
      where: {
        AND: [{ id: id }, { schedule: { AND: [ { property: { id: user.propertyId } }, { activation: { id: user.deviceId } } ] } }],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.scheduleReminder.update({
        where: { id: item.id },
        data: { isRead: true },
      });

      return 'successfully updated schedule Reminder';
    } catch (error) {
      return 'failed updated schedule Reminder';
    }
  }
}
