import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { UpdateScheduleReminderCommand } from '../impl';

@CommandHandler(UpdateScheduleReminderCommand)
export class UpdateScheduleReminderHandler
  implements ICommandHandler<UpdateScheduleReminderCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: UpdateScheduleReminderCommand) {
    const { user, id } = command;
    const item = await this.prisma.scheduleReminder.findFirst({
      where: {
        AND: [{ id: id }, { schedule: { AND: [ { property: { id: user.propertyId } }, { activation: { id: user.deviceId } } ] } }],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      await this.prisma.scheduleReminder.update({
        where: { id: item.id },
        data: { isRead: true },
      });
      return 'successfully updated Reminder';
    } catch (error) {
      return 'failed updated Reminder';
    }
  }
}
