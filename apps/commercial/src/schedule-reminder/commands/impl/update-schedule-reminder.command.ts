import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { UpdateScheduleReminderDto } from '../../dto/update-schedule-reminder.dto';

export class UpdateScheduleReminderCommand implements ICommand {
  constructor(
    public readonly user: ICurrentUser,
    public readonly id: string,
    public readonly args: UpdateScheduleReminderDto,
  ) {}
}
