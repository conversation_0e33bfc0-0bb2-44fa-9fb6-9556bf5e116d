import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetScheduleRemindersQuery } from '../impl';
import { partitionArray } from 'libs/utils/partition-array.util';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';

@QueryHandler(GetScheduleRemindersQuery)
export class GetScheduleRemindersHandler
  implements IQueryHandler<GetScheduleRemindersQuery>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestsService: GmiRESTService
  ) {}

  async execute(query: GetScheduleRemindersQuery) {
    const { user } = query;
    const items = await this.prisma.scheduleReminder.findMany({
      where: {
        AND: [
          { schedule: { activation: { id: user.deviceId } } },
          { isRead: false },
        ],
      },
      include: {
        schedule: {
          include: {
            contents: {
              include: {
                playlist: {
                  include: {
                    tracks: {
                      include: { track: true },
                      orderBy: { order: 'asc' },
                    },
                  },
                },
              },
            },
            scheduleZone: {
              include: {
                activation: {
                  include: {
                    activationDevice: {
                      where: {
                        isActive: true
                      },
                      include: {
                        device: true
                      }
                    }
                  }
                }
              }
            }
          },
        },
      },
    });

    const serializeData = items
    .filter((item) => item?.schedule?.scheduleZone.some(obj => obj.activation.activationDevice[0].device.type === 'web'))
    .map((item) => {
      const playlist = {
        id: item?.schedule?.contents?.playlist?.id,
        name: item?.schedule?.contents?.playlist?.name,
        color: item?.schedule?.contents?.playlist?.color,
        uri: `playlist:${item?.schedule?.contents?.playlist?.id}`,
        description: item?.schedule?.contents?.playlist?.description,
      };
  
      return {
        id: item?.id,
        scheduleId: item?.schedule?.id,
        mode: item?.schedule?.mode,
        modeValue: item?.schedule?.modeValue,
        startTime: item?.schedule?.startTime,
        endTime: item?.schedule?.endTime,
        playlist: item?.schedule?.contents?.playlist ? playlist : null,
        devices: item?.schedule?.scheduleZone.map((dev) => {
          return dev.activation.activationDevice.length > 0 ? {
            id: dev.activation.activationDevice[0]?.device?.id,
            serialNumber: dev.activation.activationDevice[0]?.device?.serialNumber,
            type: dev.activation.activationDevice[0]?.device?.type,
            name: dev.activation.activationDevice[0]?.device?.name,
          } : undefined;
        }),
        tracks:
          item?.schedule?.contents?.playlist?.tracks.map((track) => {
            const sc = track?.track?.source;
            return sc;
          }) || [],
      };
    });

    if (serializeData.length === 0) {
      return [];
    }

    for (let i=0; i<=serializeData.length-1; i++) {
      const trackGmiIds = serializeData[i]?.tracks.map((item) => item['id'])
      const filteredTrack = [];
      const gmiIdsPartition = partitionArray(trackGmiIds, 50);
      for (let idx=0; idx<=gmiIdsPartition.length-1; idx++) {
        const trackIn = await this.gmiRestsService.getTrackIn(gmiIdsPartition[idx], 'playlist');
        if (trackIn.length > 0) {
          for (let j=0; j<=trackIn.length-1; j++) {
            filteredTrack.push(trackIn[j]);
          }
        }
      }
      serializeData[i].tracks = filteredTrack;
    }
    return serializeData;
  }
}
