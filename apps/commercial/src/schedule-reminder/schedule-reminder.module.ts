import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { EventsModule } from '../events/events.module';
import { ScheduleReminderCommandsHandlers } from './commands';
import { ScheduleReminderEventsHandlers } from './events';
import { ScheduleReminderQueriesHandlers } from './queries';
import { ScheduleReminderController } from './schedule-reminder.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { GmiModule } from '../gmi/gmi.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleReminderEventController } from './schedule-reminder.event.controller';

@Module({
  imports: [
    CqrsModule,
    EventsModule,
    GmiModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [ScheduleReminderController, ScheduleReminderEventController],
  providers: [
    ...ScheduleReminderQueriesHandlers,
    ...ScheduleReminderCommandsHandlers,
    ...ScheduleReminderEventsHandlers,
  ],
})
export class ScheduleReminderModule {}
