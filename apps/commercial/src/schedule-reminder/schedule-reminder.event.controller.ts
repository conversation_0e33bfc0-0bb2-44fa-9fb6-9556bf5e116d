import { Controller } from '@nestjs/common';
import { EventPattern } from '@nestjs/microservices';
import { PlayerGateway } from '../events/player.gateway';
import { PrismaService } from 'libs/prisma';

interface ScheduleReminderPayload {
  payload: any;
  itemId: string;
  zoneId: string;
  playNow?: boolean;
}

@Controller()
export class ScheduleReminderEventController {
  constructor(
    private playerGateway: PlayerGateway,
    private prisma: PrismaService,
  ) {}

  @EventPattern('schedule-reminder-web')
  async handleScheduleReminderEvent(data: ScheduleReminderPayload) {
    this.playerGateway.server
      .to(data.zoneId)
      .emit('schedule:reminder', data.payload);

    if (data.playNow) {
      await this.prisma.scheduleReminder.update({
        where: {
          id: data.itemId,
        },
        data: {
          isRead: true,
        },
      });
    }
  }
}
