import { AggregateRoot } from '@nestjs/cqrs';
import { DlpRegisterCustomerEvent, DlpRenewLicenseEvent, RmsStoreHistoryEvent } from '../events';

export class IntegrationModel extends AggregateRoot {
  constructor() {
    super();
    this.autoCommit = true;
  }

  rmsStoreHistory(data: any) {
    this.apply(new RmsStoreHistoryEvent(data));
  }

  rmsRegisterCustomer(data: any) {
    this.apply(new DlpRegisterCustomerEvent(data));
  }

  rmsRenewLicense(data: any) {
    this.apply(new DlpRenewLicenseEvent(data));
  }
}
