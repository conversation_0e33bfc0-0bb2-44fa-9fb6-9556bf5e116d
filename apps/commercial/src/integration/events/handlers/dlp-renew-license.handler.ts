import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { DlpRenewLicenseEvent } from '../impl';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

@EventsHandler(DlpRenewLicenseEvent)
export class DlpRenewLicenseHandler implements IEventHandler<DlpRenewLicenseEvent> {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async handle(event: DlpRenewLicenseEvent) {
    const { args } = event;
    try {
      const token = await this.getToken();
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('DLP_HOST')}/license/renew`,
          args,
          {
            headers: {
              Authorization: `Bearer ${token}`.split('"').join(''),
              'Content-Type': 'application/json',
            },
          }
        )
      );

      if (response.status === 401) {
        const newToken = await this.getToken(token);
        await firstValueFrom(
          this.httpService.post<any>(
            `${this.configService.get<string>('DLP_HOST')}/license/renew`,
            args,
            {
              headers: {
                Authorization: `Bearer ${newToken}`.split('"').join(''),
                'Content-Type': 'application/json',
              },
            }
          )
        );
      }

    } catch (error) {
      console.log(error)
      console.log('failed activate customer to dlp');
    }
  }

  private async getToken(expiredToken?: string) {
    try {
      const existingToken = await this.redis.get(
        `dlp-integration-token`,
      );
      if (!existingToken) {
        const responseToken = await firstValueFrom(
          this.httpService.post<any>(
            `${this.configService.get<string>('DLP_HOST')}/auth/token`,
            {
              client_id: this.configService.get<string>('DLP_CLIENT_ID'),
              client_secret: this.configService.get<string>('DLP_CLIENT_SECRET'),
            }
          )
        );
    
        if (responseToken.status != 201) {
          return ''
        }
    
        await this.redis.set(
          `dlp-integration-token`,
          JSON.stringify(responseToken.data),
        );

        return responseToken.data.access_token;
      }

      if (expiredToken) {
        const responseToken = await firstValueFrom(
          this.httpService.post<any>(
            `${this.configService.get<string>('DLP_HOST')}/auth/refresh`,
            {},
            {
              headers: {
                Authorization: `Bearer ${JSON.parse(existingToken).refresh_token}`.split('"').join(''),
                'Content-Type': 'application/json',
              },
            }
          )
        );
    
        if (responseToken.status != 201) {
          return ''
        }
    
        await this.redis.set(
          `dlp-integration-token`,
          JSON.stringify(responseToken.data),
        );

        return responseToken.data.access_token;
      }

      return JSON.parse(existingToken).access_token;
    } catch (error) {
      console.log(error)
      return 'failed get dlp token';
    }
  }
}
