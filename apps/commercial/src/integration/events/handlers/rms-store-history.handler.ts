import { <PERSON><PERSON><PERSON><PERSON>, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RmsStoreHistoryEvent } from '../impl';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { HttpException, Logger } from '@nestjs/common';

@EventsHandler(RmsStoreHistoryEvent)
export class RmsStoreHistoryHandler
  implements IEventHandler<RmsStoreHistoryEvent>
{
  private readonly tokenKey = 'rms-integration-token';

  constructor(
    private prismaService: PrismaService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly gmiProvider: GmiRESTService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async handle(event: RmsStoreHistoryEvent) {
    const { args } = event;
    try {
      const property = await this.prismaService.property.findFirst({
        where: {
          id: args.propertyId,
        },
        include: {
          propertyType: true,
        },
      });

      const activation = await this.prismaService.activationDevice.findFirst({
        where: {
          AND: [
            {
              activation: {
                id: args.deviceId,
              },
            },
            {
              isActive: true,
            },
          ],
        },
        include: {
          device: {
            select: {
              type: true,
              serialNumber: true,
            },
          },
          activation: {
            select: {
              zone: true,
            },
          },
        },
      });

      const track = await this.prismaService.track.findFirst({
        where: {
          id: args.trackId,
        },
      });

      const trackGmi = await this.gmiProvider.detailTrack(track.key);
      const album = await this.gmiProvider.detailAlbum(trackGmi.album.id);

      if (trackGmi.isrc) {
        // const trackCount = await this.prismaService.$queryRawUnsafe(
        //   `
        //     select
        //       "sPh"."count"
        //     from (
        //       select
        //         "ph"."trackId",
        //         count("ph"."trackId")
        //         from "PlayHistory" as "ph"
        //         where "ph"."propertyId"=$1
        //         AND "ph"."playAt" IS NOT NULL
        //         group by "ph"."trackId"
        //     ) as "sPh"
        //     Inner Join "Track" as "tr"
        //     ON "tr"."id" = "sPh"."trackId"
        //     WHERE "tr"."id" = $2
        //   `,
        //   args.propertyId,
        //   track.id,
        // );

        const token = await this.getToken();
        try {
          await firstValueFrom(
            this.httpService.post<any>(
              `${this.configService.get<string>('RMS_HOST')}/track-usage/store`,
              {
                propertyId: property.id,
                cid: property.cid,
                brandName: property.brandName,
                industry: property.propertyType?.name,
                songTitle: trackGmi.title,
                singer: trackGmi.artists[0]?.name,
                durationSong: Number(trackGmi.duration),
                album: album?.name,
                upc: album.upc,
                label: album.label.title,
                releaseDate: album.releaseDate,
                isrc: trackGmi.isrc,
                playCount: 1, //trackCount[0].count,
                country: trackGmi.country.name,
                flag:
                  args.flag === 'DEMO' || args.flag === 'TRIAL'
                    ? 'VELODIVA[D]'
                    : 'VELODIVA',
                deviceId: activation?.device.serialNumber,
                deviceName: activation?.device?.type,
                zoneName: activation?.activation?.zone,
                origin: args?.country,
                os: args?.os,
                ip: args?.ip,
                durationPlay: args.playDuration,
                credits: trackGmi?.credits
                  ? JSON.stringify(trackGmi.credits)
                  : null,
              },
              {
                headers: {
                  Authorization: `Bearer ${token}`.split('"').join(''),
                  'Content-Type': 'application/json',
                },
              },
            ),
          );
        } catch (err) {
          if (err?.response?.status === 401) {
            console.log('[RMS] Token expired, refreshing and retrying request');
            const newToken = await this.getToken(token);
            await firstValueFrom(
              this.httpService.post<any>(
                `${this.configService.get<string>('RMS_HOST')}/track-usage/store`,
                {
                  propertyId: property.id,
                  cid: property.cid,
                  brandName: property.brandName,
                  industry: property.propertyType?.name,
                  songTitle: trackGmi.title,
                  singer: trackGmi.artists
                    .map((art) => {
                      return art?.name;
                    })
                    .join(','),
                  durationSong: Number(trackGmi.duration),
                  album: album?.name,
                  upc: album.upc,
                  label: album.label.title,
                  releaseDate: album.releaseDate,
                  isrc: trackGmi.isrc,
                  playCount: 1, //trackCount[0].count,
                  country: trackGmi.country.name,
                  flag:
                    args.flag === 'DEMO' || args.flag === 'TRIAL'
                      ? 'VELODIVA[D]'
                      : 'VELODIVA',
                  deviceId: activation?.device.serialNumber,
                  deviceName: activation?.device?.type,
                  zoneName: activation?.activation?.zone,
                  origin: args?.country,
                  os: args?.os,
                  ip: args?.ip,
                  durationPlay: args.playDuration,
                  credits: trackGmi?.credits
                    ? JSON.stringify(trackGmi.credits)
                    : null,
                },
                {
                  headers: {
                    Authorization: `Bearer ${newToken}`.split('"').join(''),
                    'Content-Type': 'application/json',
                  },
                },
              ),
            );
          }
          throw err;
        }
      }
    } catch (error) {
      console.log(error);
      console.log('failed store history to rms');
    }
  }

  private async requestNewToken(): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('RMS_HOST')}/auth/token`,
          {
            client_id: this.configService.get<string>('RMS_CLIENT_ID'),
            client_secret: this.configService.get<string>('RMS_CLIENT_SECRET'),
          },
        ),
      );

      if (response.status !== 201 || !response.data.access_token) {
        throw new Error('Failed to obtain new token');
      }

      try {
        await this.redis.set(this.tokenKey, JSON.stringify(response.data));
        await this.redis.expire(this.tokenKey, response.data.expires_in - 300);
      } catch (redisError) {
        console.error('Redis storage error:', redisError);
        // Continue even if Redis fails - we still have the token
      }

      return response.data.access_token;
    } catch (error) {
      console.error('New token request error:', error);
      throw error;
    }
  }

  private async refreshToken(refreshToken: string): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('RMS_HOST')}/auth/refresh`,
          {},
          {
            headers: {
              Authorization: `Bearer ${refreshToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.status !== 201 || !response.data.access_token) {
        return await this.requestNewToken();
      }

      try {
        await this.redis.set(this.tokenKey, JSON.stringify(response.data));
        await this.redis.expire(this.tokenKey, response.data.expires_in - 300);
      } catch (redisError) {
        console.error('Redis storage error:', redisError);
        // Continue even if Redis fails - we still have the token
      }

      return response.data.access_token;
    } catch (error) {
      return await this.requestNewToken();
    }
  }

  private async getToken(expiredToken?: string): Promise<string> {
    try {
      console.log(
        '[RMS] Getting token',
        expiredToken ? '(refresh)' : '(check cache)',
      );
      let existingToken: string | null = null;

      try {
        existingToken = await this.redis.get(this.tokenKey);
        console.log(
          '[RMS] Redis token status:',
          existingToken ? 'found' : 'not found',
        );
      } catch (redisError) {
        console.error('[RMS] Redis error:', redisError);
      }

      if (!existingToken) {
        console.log('[RMS] No existing token, requesting new one');
        return await this.requestNewToken();
      }

      const parsedToken = JSON.parse(existingToken);

      if (expiredToken) {
        console.log('[RMS] Token expired, refreshing');
        return await this.refreshToken(parsedToken.refresh_token);
      }

      console.log('[RMS] Using existing token');
      return parsedToken.access_token;
    } catch (error) {
      console.error('[RMS] Token retrieval error:', error);
      throw error;
    }
  }
}
