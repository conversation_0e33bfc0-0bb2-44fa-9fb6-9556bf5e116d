import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { GmiModule } from '../gmi/gmi.module';
import { HttpModule } from '@nestjs/axios';
import { IntegrationEventHandlers } from './events';
import { IntegrationService } from './integration.service';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    HttpModule,
  ],
  providers: [
    IntegrationService,
    ...IntegrationEventHandlers,
  ],
  exports: [
    IntegrationService,
  ]
})
export class IntegrationModule {}
