import { HttpService } from '@nestjs/axios';
import { Injectable, NotFoundException } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { InjectRedis } from '@nestjs-modules/ioredis';
import Redis from 'ioredis';

@Injectable()
export class IntegrationService {
  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRedis() private readonly redis: Redis,
  ) {}

  async DlpGetLicense(code: string): Promise<any> {
    try {
      const token = await this.dlpGetToken();
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('DLP_HOST')}/license/check`,
          {
            code,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`.split('"').join(''),
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.status === 401) {
        const newToken = await this.dlpGetToken();
        const response = await firstValueFrom(
          this.httpService.get<any>(
            `${this.configService.get<string>('DLP_HOST')}/license/${code}`,
            {
              headers: {
                Authorization: `Bearer ${newToken}`.split('"').join(''),
                'Content-Type': 'application/json',
              },
            },
          ),
        );
        if (response.status == 404) {
          throw new NotFoundException('license not found');
        }

        return response.data;
      }

      if (response.status == 404) {
        throw new NotFoundException('license not found');
      }

      return response.data;
    } catch (e) {
      return e;
    }
  }

  async DlpRegisterCustomer(args: any): Promise<any> {
    try {
      const token = await this.dlpGetToken();
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('DLP_HOST')}/customer`,
          args,
          {
            headers: {
              Authorization: `Bearer ${token}`.split('"').join(''),
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.status === 401) {
        const newToken = await this.dlpGetToken();
        await firstValueFrom(
          this.httpService.post<any>(
            `${this.configService.get<string>('DLP_HOST')}/customer`,
            args,
            {
              headers: {
                Authorization: `Bearer ${newToken}`.split('"').join(''),
                'Content-Type': 'application/json',
              },
            },
          ),
        );
      }

      return response.data.data;
    } catch (error) {
      console.log(error);
      console.log('failed register customer to dlp');
    }
  }

  async DetailCategoryByCode(code: string) {
    const token = await this.dlpGetToken();
    let response = await firstValueFrom(
      this.httpService.get<any>(
        `${this.configService.get<string>('DLP_HOST')}/category/code/${code}`,
        {
          headers: {
            Authorization: `Bearer ${token}`.split('"').join(''),
            'Content-Type': 'application/json',
          },
        },
      ),
    );

    if (response.status === 401) {
      const newToken = await this.dlpGetToken();
      response = await firstValueFrom(
        this.httpService.get<any>(
          `${this.configService.get<string>('DLP_HOST')}/category/code/${code}`,
          {
            headers: {
              Authorization: `Bearer ${newToken}`.split('"').join(''),
              'Content-Type': 'application/json',
            },
          },
        ),
      );
    }

    if (response.status === 200) {
      return response.data;
    } else {
      return response;
    }
  }

  async DlpRenewLicense(args: any): Promise<any> {
    try {
      const token = await this.dlpGetToken();
      const response = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('DLP_HOST')}/license/renew`,
          args,
          {
            headers: {
              Authorization: `Bearer ${token}`.split('"').join(''),
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.status === 401) {
        const newToken = await this.dlpGetToken();
        await firstValueFrom(
          this.httpService.post<any>(
            `${this.configService.get<string>('DLP_HOST')}/license/renew`,
            args,
            {
              headers: {
                Authorization: `Bearer ${newToken}`.split('"').join(''),
                'Content-Type': 'application/json',
              },
            },
          ),
        );
      }

      return response.data.data;
    } catch (error) {
      console.log(error);
      console.log('failed register customer to dlp');
    }
  }

  private async dlpGetToken() {
    try {
      // const existingToken = await this.redis.get(`dlp-integration-token`);
      // if (!existingToken) {
      const responseToken = await firstValueFrom(
        this.httpService.post<any>(
          `${this.configService.get<string>('DLP_HOST')}/auth/token`,
          {
            client_id: this.configService.get<string>('DLP_CLIENT_ID'),
            client_secret: this.configService.get<string>('DLP_CLIENT_SECRET'),
          },
        ),
      );

      if (responseToken.status != 201) {
        return '';
      }

      // await this.redis.set(
      //   `dlp-integration-token`,
      //   JSON.stringify(responseToken.data),
      // );

      return responseToken.data.access_token;
      // }

      // if (expiredToken) {
      //   const responseToken = await firstValueFrom(
      //     this.httpService.post<any>(
      //       `${this.configService.get<string>('DLP_HOST')}/auth/refresh`,
      //       {},
      //       {
      //         headers: {
      //           Authorization:
      //             `Bearer ${JSON.parse(existingToken).refresh_token}`
      //               .split('"')
      //               .join(''),
      //           'Content-Type': 'application/json',
      //         },
      //       },
      //     ),
      //   );
      //
      //   if (responseToken.status != 201) {
      //     return '';
      //   }
      //
      //   await this.redis.set(
      //     `dlp-integration-token`,
      //     JSON.stringify(responseToken.data),
      //   );
      //
      //   return responseToken.data.access_token;
      // }
      //
      // return JSON.parse(existingToken).access_token;
    } catch (error) {
      console.log(error);
      return 'failed get dlp token';
    }
  }
}
