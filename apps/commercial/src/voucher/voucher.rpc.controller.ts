import { Id, Status } from '@app/proto-schema/index.common';
import {
  CheckVoucherExistResponse,
  UpdateVoucherRequest,
  UseVoucherResponse,
  Voucher,
  VOUCHER_SERVICE_NAME,
  VoucherCodeRequest,
  VoucherServiceController,
} from '@app/proto-schema/index.internal';
import { Metadata } from '@grpc/grpc-js';
import { Controller } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import { Observable } from 'rxjs';
import {
  CheckVoucherByCodeQuery,
  CheckVoucherExistQuery,
  GetVoucherByCodeQuery,
  GetVoucherByIdQuery,
} from './queries/impl';
import { UpdateVoucherStatusCommand, UseVoucherCommand } from './commands';
@Controller()
export class VoucherRpcController implements VoucherServiceController {
  constructor(
    private readonly commandBus: CommandBus,
    private readonly queryBus: QueryBus,
  ) {}

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'detailVoucherByCode')
  detailVoucherByCode(
    request: VoucherCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Voucher> | Observable<Voucher> | Voucher {
    return this.queryBus.execute(new GetVoucherByCodeQuery(request.code));
  }

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'detailVoucherById')
  detailVoucherById(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Voucher> | Observable<Voucher> | Voucher {
    return this.queryBus.execute(new GetVoucherByIdQuery(request.id));
  }

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'checkVoucherByCode')
  checkVoucherByCode(
    request: VoucherCodeRequest,
    metadata: Metadata,
    ...rest: any
  ): Promise<Id> | Observable<Id> | Id {
    return this.queryBus.execute(new CheckVoucherByCodeQuery(request.code));
  }

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'checkVoucherExist')
  checkVoucherExist(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<CheckVoucherExistResponse>
    | Observable<CheckVoucherExistResponse>
    | CheckVoucherExistResponse {
    return this.queryBus.execute(new CheckVoucherExistQuery(request.id));
  }

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'useVoucher')
  useVoucher(
    request: VoucherCodeRequest,
    metadata: Metadata,
    ...rest: any
  ):
    | Promise<UseVoucherResponse>
    | Observable<UseVoucherResponse>
    | UseVoucherResponse {
    return this.commandBus.execute(new UseVoucherCommand(request));
  }

  @GrpcMethod(VOUCHER_SERVICE_NAME, 'updateStatusVoucher')
  updateStatusVoucher(
    request: UpdateVoucherRequest,
    metadata: Metadata,
    ...rest
  ): Promise<Status> | Observable<Status> | Status {
    return this.commandBus.execute(new UpdateVoucherStatusCommand(request));
  }
}
