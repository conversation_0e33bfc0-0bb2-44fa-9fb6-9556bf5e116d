import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ConfigService } from '@nestjs/config';
import { CheckVoucherByCodeQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

@QueryHandler(CheckVoucherByCodeQuery)
export class CheckVoucherByCodeHandler
  implements IQueryHandler<CheckVoucherByCodeQuery>
{
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
  ) {}

  async execute(query: CheckVoucherByCodeQuery) {
    const { code } = query;

    try {
      // Validate input
      if (!code || code.trim().length === 0) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Voucher code is required',
        });
      }

      // Find voucher by code first, then get the first available voucher detail
      const voucher = await this.prisma.voucher.findFirst({
        where: {
          code: code.trim(),
          publish: true,
        },
        include: {
          voucherDetails: {
            where: {
              status: 'available',
            },
            take: 1,
          },
        },
      });

      if (!voucher) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Voucher not found',
        });
      }

      if (voucher.voucherDetails.length === 0) {
        throw new RpcException({
          code: status.FAILED_PRECONDITION,
          message: 'No available voucher details found',
        });
      }

      return {
        id: voucher.voucherDetails[0].id,
      };
    } catch (error) {
      console.error('[CheckVoucherByCodeHandler Error]', error);

      if (error instanceof RpcException) {
        throw error;
      }

      // Handle Prisma specific errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new RpcException({
              code: status.NOT_FOUND,
              message: 'Voucher not found',
            });
          default:
            throw new RpcException({
              code: status.INTERNAL,
              message: 'Database error occurred',
            });
        }
      }

      // Handle Prisma validation errors
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Invalid voucher code format',
        });
      }

      // Default fallback
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Unexpected error while checking voucher',
      });
    }
  }
}
