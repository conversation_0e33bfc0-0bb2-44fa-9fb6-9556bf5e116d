import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ConfigService } from '@nestjs/config';
import { CheckVoucherExistQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

@QueryHandler(CheckVoucherExistQuery)
export class CheckVoucherExistHandler
  implements IQueryHandler<CheckVoucherExistQuery> {
  constructor(private prisma: PrismaService, private config: ConfigService) { }

  async execute(query: CheckVoucherExistQuery) {
    const { id } = query;

    try {
      // Validate input
      if (!id || id.trim().length === 0) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Voucher detail ID is required',
        });
      }

      const voucherDetail = await this.prisma.voucherDetail.findFirst({
        where: {
          id: id.trim(),
        }
      });

      if (!voucherDetail) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Voucher detail not found',
        });
      }

      return {
        id: voucherDetail.id,
        status: voucherDetail.status
      };
    } catch (error) {
      console.error('[CheckVoucherExistHandler Error]', error);

      if (error instanceof RpcException) {
        throw error;
      }

      // Handle Prisma specific errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new RpcException({
              code: status.NOT_FOUND,
              message: 'Voucher detail not found',
            });
          default:
            throw new RpcException({
              code: status.INTERNAL,
              message: 'Database error occurred',
            });
        }
      }

      // Handle Prisma validation errors
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Invalid voucher detail ID format',
        });
      }

      // Default fallback
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Unexpected error while checking voucher',
      });
    }
  }
}