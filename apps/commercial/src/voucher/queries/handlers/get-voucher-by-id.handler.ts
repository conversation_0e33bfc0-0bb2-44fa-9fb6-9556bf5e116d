import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ConfigService } from '@nestjs/config';
import { GetVoucherByIdQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

@QueryHandler(GetVoucherByIdQuery)
export class GetVoucherByIdHandler
  implements IQueryHandler<GetVoucherByIdQuery> {
  constructor(private prisma: PrismaService, private config: ConfigService) { }

  async execute(query: GetVoucherByIdQuery) {
    const { id } = query;

    try {
      // Validate input
      if (!id || id.trim().length === 0) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Voucher detail ID is required',
        });
      }

      const voucherDetail = await this.prisma.voucherDetail.findFirst({
        where: {
          id: id.trim(),
        },
        include: {
          voucher: {
            include: {
              propertyType: true
            }
          },
        }
      });

      if (!voucherDetail) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Voucher not found',
        });
      }

      return {
        id: voucherDetail.voucher.id,
        name: voucherDetail.voucher.name,
        propertyType: {
          id: voucherDetail.voucher.propertyType.id,
          name: voucherDetail.voucher.propertyType.name,
          slug: voucherDetail.voucher.propertyType.slug,
          icon: voucherDetail.voucher.propertyType.icon,
          createdAt: voucherDetail.voucher.propertyType.createdAt,
          updatedAt: voucherDetail.voucher.propertyType.updatedAt,
          parentId: voucherDetail.voucher.propertyType.parentId
        },
        code: voucherDetail.voucher.code,
        discountType: voucherDetail.voucher.discountType,
        discountValue: voucherDetail.voucher.discountValue,
        startDate: voucherDetail.voucher.startDate,
        endDate: voucherDetail.voucher.endDate,
        createdAt: voucherDetail.voucher.createdAt,
        updatedAt: voucherDetail.voucher.updatedAt,
      };
    } catch (error) {
      console.error('[GetVoucherByIdHandler Error]', error);

      if (error instanceof RpcException) {
        throw error;
      }

      // Handle Prisma specific errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new RpcException({
              code: status.NOT_FOUND,
              message: 'Voucher not found',
            });
          default:
            throw new RpcException({
              code: status.INTERNAL,
              message: 'Database error occurred',
            });
        }
      }

      // Handle Prisma validation errors
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Invalid voucher ID format',
        });
      }

      // Default fallback
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Unexpected error while retrieving voucher',
      });
    }
  }
}
