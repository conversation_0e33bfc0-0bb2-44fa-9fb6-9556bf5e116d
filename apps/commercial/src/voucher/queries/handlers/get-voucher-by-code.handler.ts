import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ConfigService } from '@nestjs/config';
import { GetVoucherByCodeQuery } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

@QueryHandler(GetVoucherByCodeQuery)
export class GetVoucherByCodeHandler
  implements IQueryHandler<GetVoucherByCodeQuery>
{
  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
  ) {}

  async execute(query: GetVoucherByCodeQuery) {
    const { code } = query;

    try {
      // Validate input
      if (!code || code.trim().length === 0) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Voucher code is required',
        });
      }

      const voucherDetail = await this.prisma.voucher.findFirst({
        where: {
          code: code.trim(),
          publish: true,
        },
        include: {
          voucherDetails: true,
          propertyType: true,
        },
      });

      if (!voucherDetail) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Voucher not found',
        });
      }

      return {
        id: voucherDetail.id,
        name: voucherDetail.name,
        propertyType: {
          id: voucherDetail.propertyType.id,
          name: voucherDetail.propertyType.name,
          slug: voucherDetail.propertyType.slug,
          icon: voucherDetail.propertyType.icon,
          createdAt: voucherDetail.propertyType.createdAt,
          updatedAt: voucherDetail.propertyType.updatedAt,
          parentId: voucherDetail.propertyType.parentId,
        },
        code: voucherDetail.code,
        discountType: voucherDetail.discountType,
        discountValue: voucherDetail.discountValue,
        startDate: voucherDetail.startDate,
        endDate: voucherDetail.endDate,
        createdAt: voucherDetail.createdAt,
        updatedAt: voucherDetail.updatedAt,
      };
    } catch (error) {
      console.error('[GetVoucherByCodeHandler Error]', error);

      if (error instanceof RpcException) {
        throw error;
      }

      // Handle Prisma specific errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new RpcException({
              code: status.NOT_FOUND,
              message: 'Voucher not found',
            });
          default:
            throw new RpcException({
              code: status.INTERNAL,
              message: 'Database error occurred',
            });
        }
      }

      // Handle Prisma validation errors
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Invalid voucher code format',
        });
      }

      // Default fallback
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Unexpected error while retrieving voucher',
      });
    }
  }
}
