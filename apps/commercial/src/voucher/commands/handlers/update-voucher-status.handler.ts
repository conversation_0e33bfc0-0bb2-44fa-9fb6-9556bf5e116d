import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ommand<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { UpdateVoucherStatusCommand } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { Prisma } from '@prisma/client';

@CommandHandler(UpdateVoucherStatusCommand)
export class UpdateVoucherStatusHandler
  implements ICommandHandler<UpdateVoucherStatusCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(request: UpdateVoucherStatusCommand) {
    const { args } = request;

    try {
      // Validate input
      if (!args.orderId || args.orderId.trim().length === 0) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Order ID is required',
        });
      }

      const result = await this.prisma.voucherDetail.update({
        where: { orderId: args.orderId.trim() },
        data: {
          status: 'available',
        },
      });

      return {
        success: true,
        message: 'Voucher status updated successfully',
      };
    } catch (error) {
      console.error('[UpdateVoucherStatusHandler Error]', error);

      if (error instanceof RpcException) {
        throw error;
      }

      // Handle Prisma specific errors
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        switch (error.code) {
          case 'P2025':
            throw new RpcException({
              code: status.NOT_FOUND,
              message: 'Voucher detail not found for the given order',
            });
          case 'P2002':
            throw new RpcException({
              code: status.ALREADY_EXISTS,
              message: 'Voucher status update conflict',
            });
          default:
            throw new RpcException({
              code: status.INTERNAL,
              message: 'Database error occurred',
            });
        }
      }

      // Handle Prisma validation errors
      if (error instanceof Prisma.PrismaClientValidationError) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Invalid order ID format',
        });
      }

      // Default fallback
      throw new RpcException({
        code: status.INTERNAL,
        message: 'Unexpected error while updating voucher status',
      });
    }
  }
}
