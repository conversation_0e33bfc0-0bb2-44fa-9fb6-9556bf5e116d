import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UseVoucherCommand } from '../impl';
import { RpcException } from '@nestjs/microservices';
import { status } from '@grpc/grpc-js';
import { OrderService } from '../../../membership-client/order.service';
import { firstValueFrom } from 'rxjs';
import { Prisma } from '@prisma/client';
import { GrpcErrorUtil } from '@app/common';

@CommandHandler(UseVoucherCommand)
export class UseVoucherHandler implements ICommandHandler<UseVoucherCommand> {
  constructor(
    private prisma: PrismaService,
    private orderService: OrderService,
  ) {}

  async execute(command: UseVoucherCommand) {
    const { args } = command;
    let discountPrice = 0;

    try {
      // Validate input parameters
      const code = GrpcErrorUtil.validateRequiredString(args.code, 'Voucher code');
      const orderId = GrpcErrorUtil.validateRequiredString(args.orderId, 'Order ID');
      const voucher = await this.prisma.voucher.findFirst({
        where: { code },
        include: {
          voucherDetails: { where: { status: 'available' } },
          propertyType: { select: { parentId: true } },
        },
      });

      if (!voucher) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Voucher not found',
        });
      }

      if (voucher.voucherDetails.length === 0) {
        throw new RpcException({
          code: status.FAILED_PRECONDITION,
          message: 'Voucher usage limit reached',
        });
      }

      const currentDate = new Date();
      if (currentDate < voucher.startDate || currentDate > voucher.endDate) {
        throw new RpcException({
          code: status.FAILED_PRECONDITION,
          message: 'Voucher is not valid at this time',
        });
      }

      const order = await firstValueFrom(
        this.orderService.getOrderDetail(orderId),
      );

      if (!order) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'Order not found',
        });
      }

      if (order.propertyType !== voucher.propertyType.parentId) {
        throw new RpcException({
          code: status.INVALID_ARGUMENT,
          message: 'Voucher does not apply to this industry',
        });
      }

      const existingVoucherDetail = await this.prisma.voucherDetail.findUnique({
        where: { orderId },
      });

      if (existingVoucherDetail) {
        throw new RpcException({
          code: status.ALREADY_EXISTS,
          message: 'Voucher already applied to this order',
        });
      }

      const item = order.orderDetail.find((i) => i.itemType === 'PLAN');
      if (!item) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'No PLAN item found in order',
        });
      }

      switch (voucher.discountType) {
        case 'percentage':
          discountPrice =
            (Number(voucher.discountValue) / 100) *
            (Number(item.price) * item.qty);
          break;
        case 'flat':
          discountPrice = Number(voucher.discountValue);
          break;
        default:
          throw new RpcException({
            code: status.INVALID_ARGUMENT,
            message: 'Unsupported discount type',
          });
      }

      const voucherDetailId = voucher.voucherDetails[0].id;

      await this.prisma.voucherDetail.update({
        where: { id: voucherDetailId },
        data: {
          status: 'reserved',
          orderId: order.id,
        },
      });

      return {
        voucherId: voucher.id,
        itemId: item.id,
        itemType: 'PLAN',
        totalPriceBefore: Number(item.price) * item.qty,
        discountPrice,
        totalPriceAfter: Number(item.price) * item.qty - discountPrice,
      };
    } catch (error) {
      GrpcErrorUtil.handleError(error, 'UseVoucherHandler');
    }
  }
}
