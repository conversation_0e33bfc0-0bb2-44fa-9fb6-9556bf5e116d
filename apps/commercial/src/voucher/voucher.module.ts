import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { VoucherRpcController } from './voucher.rpc.controller';
import { VoucherQueryHandlers } from './queries';
import { VoucherCommandHandlers } from './commands';
import { MembershipClientModule } from '../membership-client/membership-client.module';

@Module({
  imports: [CqrsModule, MembershipClientModule],
  controllers: [VoucherRpcController],
  providers: [...VoucherQueryHandlers, ...VoucherCommandHandlers],
})
export class VoucherModule {}
