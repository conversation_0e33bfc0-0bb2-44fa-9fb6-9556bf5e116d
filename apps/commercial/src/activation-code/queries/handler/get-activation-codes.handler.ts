import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetActivationCodesQuery } from '../impl';
import { Pagination } from '@app/common';
import { Activation, Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';

@QueryHandler(GetActivationCodesQuery)
export class GetActivationCodesHandler implements IQueryHandler<GetActivationCodesQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetActivationCodesQuery) {
    const { propertyId, args } = query;
    const items = await Pagination<any,Prisma.ActivationFindManyArgs>(
      this.prisma.activation,
      {
        where: {
          propertyId,
        },
        orderBy: { createdAt: 'desc' },
        include: {
          activationDevice: {
            where: {
              device: {
                type: {
                  not: 'web'
                }
              }
            }
          }
        },
      },
      {
        limit: args.limit,
        page: args.page,
      },
    );
    items.data = items.data.map((item) => {
      if (!(item.activationDevice.length === 0 && item.isUsed === true)) {
        return {
          id: item.id,
          code: item.code,
          expiredAt: item.expiredAt,
          isUsed: item.isUsed,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }
      }
    }).reduce((accumulator, currentValue) => {
      if (currentValue) {
        accumulator.push(currentValue);
      }
      return accumulator;
    }, []);
    items.meta.total = items.meta.total > 0 ? items.meta.total-1 : items.meta.total;
    items.meta.lastPage = items.meta.total < items.meta.limit ? items.meta.lastPage :  items.meta.total % items.meta.limit

    return items;
  }
}
