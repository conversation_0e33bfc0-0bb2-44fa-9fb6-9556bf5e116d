import {
  <PERSON>,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { FilterActivationCodeDto } from './dto/filter-activation-code.dto';
import { GetActivationCodesQuery } from './queries';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('activation-code')
@ApiTags('Activation Code')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class ActivationCodeController {
  constructor(
    private queryBus: QueryBus,
  ) {}

  // @Post()
  // @UseGuards(AccessFeatureGuard)
  // @AccessFeature(FEATURE.PlayerNode)
  // create(@User() user: ICurrentUser) {
  //   return this.commandBus.execute(
  //     new GenerateActivationCodeCommand(user),
  //   );
  // }

  // @Patch(':id/renew')
  // @Throttle({ default: { limit: 10, ttl: 60000 } })
  // @UseGuards(AccessFeatureGuard)
  // @AccessFeature(FEATURE.PlayerNode)
  // update(@User() user: ICurrentUser, @Param('id') id: string) {
  //   return this.commandBus.execute(new RenewActivationCodeCommand(user, id));
  // }

  @Get()
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.PlayerNode)
  findAll(
    @User() user: ICurrentUser,
    @Query() filter: FilterActivationCodeDto,
  ) {
    return this.queryBus.execute(
      new GetActivationCodesQuery(user.propertyId, filter),
    );
  }
}
