import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GrpcGetVariantQuery } from '../impl';
import { PrismaService } from '../../../../../../libs/prisma';
import { NotFoundException } from '@nestjs/common';

@QueryHandler(GrpcGetVariantQuery)
export class GrpcGetVariantHandler
  implements IQueryHandler<GrpcGetVariantQuery>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GrpcGetVariantQuery) {
    const { id } = query;

    const variant = await this.prisma.productVariant.findFirst({
      where: { id, deletedAt: null },
      include: {
        sku: true,
        product: { include: { taxProduct: { include: { tax: true } } } },
      },
    });

    if (!variant) {
      throw new NotFoundException('Product-variant not found');
    }

    return {
      id: variant.id,
      name: variant.name,
      price: Number(variant.price.toFixed()),
      description: variant.description,
      product: variant.product
        ? {
            id: variant.product.id,
            name: variant.product.name,
            description: variant.product.description,
            propertyTypeId: variant.product.propertyTypeId,
            subfolderId: variant.product.subfolderId,
            taxes: variant.product.taxProduct.map((tax) => ({
              id: tax.tax.id,
              name: tax.tax.name,
              nominal: Number(tax.tax.nominal),
              type: tax.tax.type,
              startDate: tax.tax.startDate,
              endDate: tax.tax.endDate,
            })),
            isMultiple: variant.product.isMultiple,
            createdAt: variant.product.createdAt.toISOString(),
            updatedAt: variant.product.updatedAt.toISOString(),
          }
        : null,
      sku: variant.sku
        ? {
            id: variant.sku.id,
            code: variant.sku.code,
            productType: variant.sku.productType,
          }
        : null,
      createdAt: variant.createdAt.toISOString(),
      updatedAt: variant.updatedAt.toISOString(),
    };
  }
}
