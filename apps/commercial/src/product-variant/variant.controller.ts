import { ApiTags } from '@nestjs/swagger';
import {
  Controller,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { GrpcMethod } from '@nestjs/microservices';
import {
  Variant,
  VARIANT_SERVICE_NAME,
} from '@app/proto-schema/internal-proto/product-variant';
import { Id } from '@app/proto-schema/common-proto/common';
import { Metadata, status } from '@grpc/grpc-js';
import { Observable } from 'rxjs';
import { GrpcGetVariantQuery } from './queries';

@ApiTags('Product-Variant')
@Controller('product-variant')
export class ProductVariantController {
  constructor(private queryBus: QueryBus) {}

  @GrpcMethod(VARIANT_SERVICE_NAME, 'detailVariant')
  detailVariant(
    request: Id,
    metadata: Metadata,
    ...rest: any
  ): Promise<Variant> | Observable<Variant> | Variant {
    try {
      return this.queryBus.execute(new GrpcGetVariantQuery(request.id));
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('product-variant not found');
      } else {
        throw new InternalServerErrorException('internal server error');
      }
    }
  }
}
