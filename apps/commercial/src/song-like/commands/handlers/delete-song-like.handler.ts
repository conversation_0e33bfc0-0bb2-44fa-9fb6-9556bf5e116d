import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { DeleteSongLikeCommand } from '../impl';

@CommandHandler(DeleteSongLikeCommand)
export class DeleteSong<PERSON>ikeHandler
  implements ICommandHandler<DeleteSongLikeCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: DeleteSongLikeCommand) {
    const { trackId, user } = command;

    const track = await this.prisma.track.findFirst({
      where: { key: trackId },
    });

    const item = await this.prisma.trackLike.findFirst({
      where: {
        AND: [
          { activation: { id: user.deviceId }},
          { property: { id: user.propertyId } },
          { track: { id: track.id} },
        ],
      },
    });
    if (!item) {
      return 'successfully unlike song';
    }

    try {
      await this.prisma.trackLike.delete({
        where: {
          trackId_propertyId_activationId: {
            activationId: item.activationId,
            propertyId: item.propertyId,
            trackId: item.trackId,
          },
        },
      });
      return 'successfully unlike song';
    } catch (error) {
      return 'failed unlike song';
    }
  }
}
