import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { JsonValue } from '@prisma/client/runtime/library';
import { PrismaService } from 'libs/prisma'; 
import { CreateSongLikeCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@CommandHandler(CreateSongLikeCommand)
export class CreateSongLikeHandler
  implements ICommandHandler<CreateSongLikeCommand>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestService: GmiRESTService
  ) {}

  async execute(command: CreateSongLikeCommand) {
    const { args, user } = command;

    let track = await this.prisma.track.findFirst({
      where: { key: args.id },
    });
    
    if (!track) {
      const gmiTrack = await this.gmiRestService.melodivaUse(args.id);
      await this.prisma.track.create({
        data: {
          key: args.id,
          source: gmiTrack,
        },
      });
    } else if (track.source && Object.keys(track.source).length === 0) {
      const gmiTrack = await this.gmiRestService.melodivaUse(args.id);
      track = await this.prisma.track.update({
        where: { key: args.id },
        data: { source: gmiTrack },
      });
    }
    
    let trackId = track?.key;    
    try {
      const existingLike = await this.prisma.trackLike.findFirst({
        where: {
          propertyId: user.propertyId,
          trackId: trackId,
        },
      });

      if (!existingLike) {
        await this.prisma.trackLike.create({
          data: {
            activation: { connect: { id: user.deviceId }},
            property: { connect: { id: user.propertyId } },
            track: { connect: { key: trackId } },
          },
        });
        return 'successfully liked song';
      }

    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        throw new BadRequestException('Song already liked');
      } else {
        console.error(error);
        throw new InternalServerErrorException('Failed to like song');
      }
    }
  }
}