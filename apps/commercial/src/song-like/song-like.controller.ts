import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { CreateSongLikeCommand, DeleteSongLikeCommand } from './commands';
import { CreateSongLikeDto } from './dto/create-song-like.dto';
import { FilterSongLikeDto } from './dto/filter-song-like.dto';
import { GetSongLikesQuery } from './queries';
import { Throttle } from '@nestjs/throttler';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('song-like')
@ApiTags('Song Like')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SongLikeController {
  constructor(
    private readonly queryBus: QueryBus,
    private readonly commandBus: CommandBus,
  ) {}

  @Post()
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  create(
    @User() user: ICurrentUser,
    @Body() createSongLikeDto: CreateSongLikeDto,
  ) {
    return this.commandBus.execute(
      new CreateSongLikeCommand(user, createSongLikeDto),
    );
  }

  @Get()
  findAll(@User() user: ICurrentUser, @Query() filter: FilterSongLikeDto) {
    return this.queryBus.execute(new GetSongLikesQuery(user, filter));
  }

  @Delete(':trackId')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  remove(@User() user: ICurrentUser, @Param('trackId') id: string) {
    return this.commandBus.execute(new DeleteSongLikeCommand(user, id));
  }
}
