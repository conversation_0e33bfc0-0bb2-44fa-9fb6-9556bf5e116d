import { Modu<PERSON> } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { SongLikeCommandsHandlers } from './commands';
import { SongLikeQueriesHandlers } from './queries';
import { SongLikeController } from './song-like.controller';
import { GmiModule } from '../gmi/gmi.module';

@Module({
  imports: [CqrsModule, GmiModule],
  controllers: [SongLikeController],
  providers: [...SongLikeCommandsHandlers, ...SongLikeQueriesHandlers],
})
export class SongLikeModule {}
