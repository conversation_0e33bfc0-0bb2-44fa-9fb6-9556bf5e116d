import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetSongLikesQuery } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { partitionArray } from 'libs/utils/partition-array.util';

@QueryHandler(GetSongLikesQuery)
export class GetSongLikesHandler implements IQueryHandler<GetSongLikesQuery> {
  constructor(
    private prisma: PrismaService,
    private gmiRestsService: GmiRESTService,
  ) {}

  async execute(query: GetSongLikesQuery) {
    const { args, user } = query;
    const search = (args.search || '').toLowerCase();
    const page = Number(args?.page || 1);
    const limit = Number(args?.limit || 100);
    const offset = (page - 1) * limit;

    const rawItems = await this.prisma.$queryRawUnsafe<any[]>(
      `
        SELECT tl."trackId", t."source"
        FROM "TrackLike" tl
        JOIN "Track" t ON tl."trackId" = t.id
        WHERE tl."propertyId" = $1
          AND tl."activationId" = $2
          AND LOWER(t."source"->>'title') LIKE $3
        ORDER BY tl."createdAt" DESC
        LIMIT $4 OFFSET $5
    `,
      user.propertyId,
      user.deviceId,
      `%${search}%`,
      limit,
      offset,
    );

    const countResult = await this.prisma.$queryRawUnsafe<{ count: number }[]>(
      `
        SELECT COUNT(*)::int as count
        FROM "TrackLike" tl
        JOIN "Track" t ON tl."trackId" = t.id
        WHERE tl."propertyId" = $1
          AND tl."activationId" = $2
          AND LOWER(t."source"->>'title') LIKE $3
    `,
      user.propertyId,
      user.deviceId,
      `%${search}%`,
    );

    const total = Number(countResult[0]?.count || 0);
    const lastPage = Math.ceil(total / limit);

    const trackSources = rawItems.map((item) => item.source);
    const trackGmiIds = trackSources.map((tr) => tr.id);

    const gmiIdsPartition = partitionArray(trackGmiIds, 50);
    const filteredTrack = [];

    for (const ids of gmiIdsPartition) {
      const trackIn = await this.gmiRestsService.getTrackIn(ids, 'like');
      const trackInMap = new Map(trackIn.map((track) => [track.id, track]));
      for (const id of ids) {
        if (trackInMap.has(id)) {
          filteredTrack.push(trackInMap.get(id));
        }
      }
    }

    return {
      meta: {
        total,
        lastPage,
        currentPage: page,
        limit,
        prev: page > 1 ? page - 1 : null,
        next: page < lastPage ? page + 1 : null,
      },
      data: filteredTrack,
    };
  }
}
