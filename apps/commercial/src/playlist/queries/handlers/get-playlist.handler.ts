import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPlaylistQuery } from '../impl';
import { ConfigService } from '@nestjs/config';
import { PlaylistPermission } from '@app/common';

@QueryHandler(GetPlaylistQuery)
export class GetPlaylistHandler implements IQueryHandler<GetPlaylistQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetPlaylistQuery) {
    const { id, filter, user } = query;

    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          { id: id },
          {
            OR: [
              {
                AND: [
                  {
                    activation: { id: user.deviceId },
                  },
                  {
                    property: { id: user.propertyId },
                  },
                ],
              },
              {
                playlistFollower: {
                  some: {
                    activation: {
                      id: user.deviceId
                    }
                  }
                }
              },
              {
                linkCode: filter.code,
              },
              {
                playlistAccess: {
                  some: {
                    OR: [
                      {
                        propertyId: user.propertyId
                      },
                      {
                        activationId: user.deviceId
                      }
                    ]
                  }
                }
              },
              {
                permission: PlaylistPermission.PUBLIC,
              },
            ],
          },
        ],
      },
      include: {
        _count: { select: { tracks: true, schedule: true } },
        playlistFollower: true,
        thumbnail: true,
      },
    });
    if (!item) {
      throw new NotFoundException();
    }

    const trackList = await this.prisma.trackOnPlaylist.findMany({
      where: { playlistId: item.id },
      select: {
        track: {
          select: {
            source: true,
          },
        },
      },
    });

    const totalDuration = trackList.reduce((sum, trackOnPlaylist) => {
      const source = trackOnPlaylist.track?.source;
      const duration =
        source && typeof source === 'object' && 'duration' in source
          ? parseInt(source.duration.toString(), 10)
          : 0;
      return sum + duration;
    }, 0);

    const thumbnail = item.thumbnail
      ? [
          {
            url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${item.thumbnail.path}/${item.thumbnail.fileName}`,
            width: 64,
            height: 64,
          },
          {
            url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${item.thumbnail.path}/${item.thumbnail.fileName}`,
            width: 300,
            height: 300,
          },
          {
            url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${item.thumbnail.path}/${item.thumbnail.fileName}`,
            width: 640,
            height: 640,
          },
        ]
      : [
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_64_URL'),
            width: 64,
            height: 64,
          },
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_300_URL'),
            width: 300,
            height: 300,
          },
          {
            url: this.configService.get<string>('DEFAULT_IMAGE_640_URL'),
            width: 640,
            height: 640,
          },
        ];

    return Object.assign(item, {
      uri: `myplaylist:${item.id}`,
      // thumbnail: trackList[0]?.track?.source['images'] ||
      thumbnail,
      firstTrack: trackList[0]?.track?.source['images'],
      totalDuration,
      permission: item.permission,
      isOwner: item.activationId === user.deviceId ? true : false,
      isFollowed: item?.playlistFollower.some((flw) => flw.activationId === user.deviceId),
    });
  }
}
