import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPlaylistRecipientsQuery } from '../impl';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';

@QueryHandler(GetPlaylistRecipientsQuery)
export class GetPlaylistRecipientsHandler implements IQueryHandler<GetPlaylistRecipientsQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetPlaylistRecipientsQuery) {
    const { id, user } = query;
    // const search = args.search || '';
    // const page = Number(args?.page || 1);
    // const limit = Number(args?.limit || 100);

    const items = await this.prisma.playlistAccess.findMany({
      where:{
        playlist: {
          AND: [
            {
              activationId: user.deviceId
            },
            {
              id
            }
          ]
        }
      },
      orderBy: { createdAt: 'desc' },
      include: {
        activation: {
          include: {
            user: {
              include: {
                profile: {
                  include: {
                    media: true
                  }
                }
              }
            }
          }
        },
        property: {
          include: {
            users: {
              where: {
                user: {
                  isAdmin: true
                }
              },
              include: {
                user: {
                  include: {
                    profile: {
                      include: {
                        media: true
                      }
                    }
                  }
                }
              }
            }
          }
        }
      },
    })

    return items.map((item) => {
      if (item.property.length > 0) {
        return {
          email: item.property[0].users[0].user.email,
          group: true,
          username: item.property[0].users[0].user.username,
          fistName: item.property[0].users[0].user.profile?.firstName,
          lastName: item.property[0].users[0].user.profile?.lastName,
          avatar: item.property[0].users[0].user.profile?.media
          ? {
              url: `${this.configService.get<string>('APP_COMMERCIAL_SERVER')}/avatar/${item.property[0].users[0].user.profile.media.fileName}`,
            }
          : null,
        }
      } else {
        return {
          email: item.activation.user.email,
          group: false,
          username: item.activation.user.username,
          fistName: item.activation.user.profile?.firstName,
          lastName: item.activation.user.profile?.lastName,
          avatar: item.activation.user.profile?.media
          ? {
              url: `${this.configService.get<string>('APP_COMMERCIAL_SERVER')}/avatar/${item.activation.user.profile.media.fileName}`,
            }
          : null,
        }
      }
    })
  }
}
