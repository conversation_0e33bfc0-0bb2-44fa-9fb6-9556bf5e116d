import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPlaylistsQuery } from '../impl';
import { ConfigService } from '@nestjs/config';

@QueryHandler(GetPlaylistsQuery)
export class GetPlaylistsHandler implements IQueryHandler<GetPlaylistsQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetPlaylistsQuery) {
    const { args, user } = query;
    const search = args.search || '';
    const page = Number(args?.page || 1);
    const limit = Number(args?.limit || 100);

    const skip = page > 0 ? limit * (page - 1) : 0;

    const playlists = await this.prisma.playlist.findMany({
      where: {
        OR: [
          {
            AND: [
              {
                playlistFollower: {
                  some: {
                    activationId: user.deviceId,
                  },
                },
              },
              { name: { contains: search, mode: 'insensitive' } },
            ],
          },
          {
            AND: [
              { property: { id: user.propertyId } },
              { activation: { id: user.deviceId } },
              { name: { contains: search, mode: 'insensitive' } },
            ],
          },
        ],
      },
      include: {
        _count: { select: { tracks: true } },
        thumbnail: true,
      },
      skip,
      take: limit,
    });
    //revamp
    const playlistsWithDurationAndFirstTrack = await Promise.all(
      playlists.map(async (playlist) => {
        try {
          const trackDurations = await this.prisma.trackOnPlaylist.findMany({
            where: { playlistId: playlist.id },
            select: {
              track: {
                select: {
                  source: true,
                },
              },
            },
          });

          const totalDuration = trackDurations.reduce(
            (sum, trackOnPlaylist) => {
              const source = trackOnPlaylist.track?.source;
              const duration =
                source && typeof source === 'object' && 'duration' in source
                  ? parseInt(source.duration.toString(), 10)
                  : 0;

              return sum + duration;
            },
            0,
          );

          const firstTrackData = await this.prisma.trackOnPlaylist.findFirst({
            where: { playlistId: playlist.id },
            // orderBy: { position: 'asc' }, // Assuming 'position' is the field that determines the order of tracks in the playlist
            select: {
              track: {
                select: {
                  id: true,
                  source: true, // Fetch the source to extract details
                },
              },
            },
          });

          const firstTrack = firstTrackData?.track
            ? {
                id: firstTrackData.track.id,
                title: firstTrackData.track.source['title'] || null,
                artists: firstTrackData.track.source['artists'] || [],
                duration: firstTrackData.track.source['duration'] || null,
                album: firstTrackData.track.source['album'] || null,
                // Add any other relevant fields from the source as needed
              }
            : null;

          const thumbnail = playlist.thumbnail
            ? [
                {
                  url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                  width: 64,
                  height: 64,
                },
                {
                  url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                  width: 300,
                  height: 300,
                },
                {
                  url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                  width: 640,
                  height: 640,
                },
              ]
            : [
                {
                  url: this.configService.get<string>('DEFAULT_IMAGE_64_URL'),
                  width: 64,
                  height: 64,
                },
                {
                  url: this.configService.get<string>('DEFAULT_IMAGE_300_URL'),
                  width: 300,
                  height: 300,
                },
                {
                  url: this.configService.get<string>('DEFAULT_IMAGE_640_URL'),
                  width: 640,
                  height: 640,
                },
              ];

          return {
            ...playlist,
            uri: `myplaylist:${playlist.id}`,
            firstTrack, // Include the structured first track data
            thumbnail,
            totalDuration,
          };
        } catch (error) {
          console.error(`Error processing playlist ${playlist.id}:`, error);
          return {
            ...playlist,
            uri: `myplaylist:${playlist.id}`,
            firstTrack: null, // Set first track to null in case of error
            thumbnail: [],
            totalDuration: 0,
          };
        }
      }),
    );

    const totalCount = await this.prisma.playlist.count({
      where: {
        AND: [
          { property: { id: user.propertyId } },
          { activation: { id: user.deviceId } },
          { name: { contains: search, mode: 'insensitive' } },
        ],
      },
    });

    const lastPage = Math.ceil(totalCount / limit);

    return {
      data: playlistsWithDurationAndFirstTrack,
      meta: {
        total: totalCount,
        lastPage,
        currentPage: page,
        limit,
        prev: page > 1 ? page - 1 : null,
        next: page < lastPage ? page + 1 : null,
      },
    };
  }
  catch(error) {
    console.error('Error fetching playlists:', error);
    throw new Error('Failed to fetch playlists');
  }
}
