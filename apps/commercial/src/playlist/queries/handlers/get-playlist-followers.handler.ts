import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON>and<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPlaylistFollowersQuery } from '../impl';
import { ConfigService } from '@nestjs/config';
import { Prisma } from '@prisma/client';

@QueryHandler(GetPlaylistFollowersQuery)
export class GetPlaylistFollowersHandler implements IQueryHandler<GetPlaylistFollowersQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService,
  ) {}

  async execute(query: GetPlaylistFollowersQuery) {
    const { id, args, user } = query;
    const search = args.search || '';
    const page = Number(args?.page || 1);
    const limit = Number(args?.limit || 100);

    const items = await Pagination<any, Prisma.PlaylistFollowerFindManyArgs>(
      this.prisma.playlistFollower,
      {
        where:{
          AND: [
            {
              activation: {
                user: {
                  OR: [
                    { username: { contains: search, mode: 'insensitive' } },
                    {
                      profile: {
                        OR: [
                          { firstName: { contains: search, mode: 'insensitive' } },
                          { lastName: { contains: search, mode: 'insensitive' } }
                        ]
                      }
                    }
                  ]
                }
              }
            },
            {
              playlist: {
                AND: [
                  {
                    id,
                    activationId: user.deviceId
                  }
                ]
              }
            }
          ]
        },
        orderBy: { createdAt: 'desc' },
        include: {
          activation: {
            include: {
              user: {
                include: {
                  profile: {
                    include: {
                      media: true
                    }
                  }
                }
              }
            }
          }
        },
      },
      {
        limit: limit,
        page: page,
      }
    );

    return items.data.map((item) => {
      return {
        id: item.id,
        firstName: item.activation.user.profile.firstName,
        lastName: item.activation.user.profile.lastName,
        avatar: item.activation.user.profile?.media
        ? {
            url: `${this.configService.get<string>('APP_COMMERCIAL_SERVER')}/avatar/${item.activation.user.profile.media.fileName}`,
          }
        : null,
      }
    })
  }
}
