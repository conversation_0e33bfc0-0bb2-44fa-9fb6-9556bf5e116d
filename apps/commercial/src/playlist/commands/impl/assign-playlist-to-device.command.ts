import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { AssignPlaylistDeviceDto } from '../../dto/assing-playlist-to-device.dto';

export class AssignPlaylistToDeviceCommand implements ICommand {
  constructor(
    public readonly id: string,
    public readonly args: AssignPlaylistDeviceDto,
    public readonly user: ICurrentUser,
  ) {}
}
