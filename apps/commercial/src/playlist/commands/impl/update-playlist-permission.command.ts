import { ICommand } from '@nestjs/cqrs';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';
import { UpdatePlaylistPermissionDto } from '../../dto/update-playlist-permission.dto';

export class UpdatePlaylistPermissionCommand implements ICommand {
  constructor(
    public readonly id: string,
    public readonly args: UpdatePlaylistPermissionDto,
    public readonly user: ICurrentUser,
  ) {}
}
