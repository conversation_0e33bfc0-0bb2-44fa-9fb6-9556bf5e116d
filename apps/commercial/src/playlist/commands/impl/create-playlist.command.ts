import { ICommand } from '@nestjs/cqrs';
import { CreatePlaylistDto } from '../../dto/create-playlist.dto';
import { ICurrentUser } from 'apps/commercial/src/auth/strategies/types/user.type';

export class CreatePlaylistCommand implements ICommand {
  constructor(
    public readonly args: CreatePlaylistDto,
    public readonly user: ICurrentUser,
    public readonly file?: Express.Multer.File,
  ) {}
}
