import { <PERSON><PERSON><PERSON><PERSON>, ICommand<PERSON>and<PERSON> } from "@nestjs/cqrs";
import { ThumbnailPlaylistCommand } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ConfigService } from "@nestjs/config";
import renameFile from "@app/common/helpers/file-manager/rename-file";
import { Client } from "minio";
import { Readable } from "stream";

@CommandHandler(ThumbnailPlaylistCommand)
export class ThumbnailPlaylistHandler implements ICommandHandler<ThumbnailPlaylistCommand> {
  constructor(
    private prisma: PrismaService,
    private readonly configService: ConfigService
  ) {
  }

  async execute(command: ThumbnailPlaylistCommand) {
    const { playlistId, file, metaMedia } = command
    const fileName = renameFile(metaMedia?.originalname)
    const item = await this.prisma.playlistThumbnail.findFirst({
      where: { playlist: { id: playlistId } }
    })
    const minioClient = new Client({
      endPoint: this.configService.get('MINIO_ENDPOINT'),
      port: parseInt(this.configService.get('MINIO_PORT') || '9000', 10),
      useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
      accessKey: this.configService.get('MINIO_ACCESS_KEY'),
      secretKey: this.configService.get('MINIO_SECRET_KEY'),
    });

    if (!item) {
      try {
        const chunks: Buffer[] = [];
        let fileLocation = '';
        if (this.configService.get('STORAGE_PROVIDER') === 'minio') {
          let useHttps = '';
          if (this.configService.get('MINIO_USE_SSL') === 'true') {
            useHttps = 'https';
          } else {
            useHttps = 'http';
          }

          fileLocation = `${this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`)}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`;
        }

        return new Promise((resolve, reject) => {
          file.subscribe({
            next: (chunk) => {
              chunks.push(Buffer.from(chunk.chunk));
            },
            complete: async () => {
              const buffer = Buffer.concat(chunks);
              const readableStream = new Readable({
                read() {
                  this.push(buffer);
                  this.push(null);
                },
              });

              await minioClient.putObject(
                this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`),
                `${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
                readableStream,
                readableStream.readableLength,
                { 'Content-Type': metaMedia.mimetype }
              );

              await this.prisma.playlistThumbnail.create({
                data: {
                  path: fileLocation,
                  fileName: fileName,
                  size: metaMedia?.size,
                  caption: metaMedia?.originalname.slice(0, -4),
                  encoding: metaMedia?.encoding,
                  mimeType: metaMedia?.mimetype,
                  name: metaMedia?.originalname.slice(0, -4),
                  playlist: {
                    connect: {
                      id: playlistId,
                    },
                  },
                },
              });

              resolve('successfully change thumbnail');
            },
            error: (err) => {
              reject(err);
            },
          });
        });
      } catch (error) {
        await minioClient.removeObject(
          this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`),
          `${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
        );
        return 'failed change thumbnail';
      }
    } else {
      try {
        const chunks: Buffer[] = [];
        let fileLocation = '';
        if (this.configService.get('STORAGE_PROVIDER') === 'minio') {
          let useHttps = '';
          if (this.configService.get('MINIO_USE_SSL') === 'true') {
            useHttps = 'https';
          } else {
            useHttps = 'http';
          }

          fileLocation = `${this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`)}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`;
        }

        return new Promise((resolve, reject) => {
          file.subscribe({
            next: (chunk) => {
              chunks.push(Buffer.from(chunk.chunk));
            },
            complete: async () => {
              const buffer = Buffer.concat(chunks);
              const readableStream = new Readable({
                read() {
                  this.push(buffer);
                  this.push(null);
                },
              });

              await minioClient.putObject(
                this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`),
                `${this.configService.get('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
                readableStream,
                readableStream.readableLength,
                { 'Content-Type': metaMedia.mimetype }
              );

              await this.prisma.playlistThumbnail.update({
                where: { id: item.id },
                data: {
                  path: fileLocation,
                  fileName: fileName,
                  size: metaMedia?.size,
                  caption: metaMedia?.originalname.slice(0, -4),
                  encoding: metaMedia?.encoding,
                  mimeType: metaMedia?.mimetype,
                  name: metaMedia?.originalname.slice(0, -4),
                },
              });

              resolve('successfully change thumbnail');
            },
            error: (err) => {
              reject(err);
            },
          });
        });
      } catch (error) {
        await minioClient.removeObject(
          this.configService.get(`${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`),
          `${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
        );
        return 'failed change thumbnail';
      }
    }
  }
}