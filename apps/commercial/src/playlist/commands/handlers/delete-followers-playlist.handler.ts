import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeleteFollowerPlaylistCommand } from '../impl';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeleteFollowerPlaylistCommand)
export class DeleteFollowPlaylistHandler
  implements ICommandHandler<DeleteFollowerPlaylistCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DeleteFollowerPlaylistCommand) {
    const { id, followerId, user } = command;
    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          { id: id },
          { activation: { id: user.deviceId } }
        ],
      },
    });
    if (!item) {
      throw new NotFoundException('playlist not found');
    }

    const follower = await this.prisma.playlistFollower.findFirst({
      where: {
        id: followerId
      },
    });
    if (!follower) {
      throw new NotFoundException('follower not found');
    }
    try {
      await this.prisma.playlistFollower.delete({
        where: {
          id: follower.id
        }
      });

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.increaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: follower.activationId
      });

      // Todo : should add new notification?

      return {
        status: 'success',
        message: 'successfully delete follower playlist',
      };
    } catch (error) {
      return 'failed delete follower playlist';
    }
  }
}
