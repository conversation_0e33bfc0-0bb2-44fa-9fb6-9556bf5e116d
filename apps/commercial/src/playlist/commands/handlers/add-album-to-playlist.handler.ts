import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { ForbiddenException, HttpStatus, NotFoundException } from '@nestjs/common';
import { AddAlbumToPlaylistCommand, AddArtistToPlaylistCommand } from '../impl';
import { PlaylistModel } from '../../model/playlist.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(AddAlbumToPlaylistCommand)
export class AddAlbumToPlaylistHandler
  implements ICommandHandler<AddAlbumToPlaylistCommand>
{
  constructor(
    private prisma: PrismaService,
    private publisher: EventPublisher,
  ) {}

  async execute(command: AddAlbumToPlaylistCommand) {
    const { albumId, id, user } = command;

    // const playlistPackage = await this.prisma.zoneFeature.findFirst({
    //   where: {
    //     AND: [
    //       {
    //         featureId: FEATURE.Playlist,
    //       },
    //       {
    //         activationId: user.deviceId,
    //       },
    //     ],
    //   },
    // });

    // if (!playlistPackage) {
    //   throw new ForbiddenException('Insufficient playlist quota');
    // }
    // if (playlistPackage.qouta < 1 && playlistPackage.qouta !== -1) {
    //   throw new ForbiddenException('Insufficient playlist quota');
    // }

    const playlist = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          {
            id
          },
          {
            activation: {
              id: user.deviceId
            }
          }
        ]
      }
    });
    if (!playlist) {
      throw new NotFoundException('playlist not found')
    }

    const playlistModel = this.publisher.mergeClassContext(PlaylistModel);
    const playlistMod = new playlistModel(user);
    playlistMod.addAlbumToPlaylist(id, albumId);

    return {
      status: HttpStatus.CREATED,
      message: 'successfully added album to playlist'
    };
  }
}