import { Inject, NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { AssignPlaylistToDeviceCommand } from '../impl';
import { ClientProxy } from '@nestjs/microservices';

@CommandHandler(AssignPlaylistToDeviceCommand)
export class AssignPlaylistToDeviceHandler
  implements ICommandHandler<AssignPlaylistToDeviceCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    @Inject('mobile') private client: ClientProxy,
  ) {}

  async execute(command: AssignPlaylistToDeviceCommand) {
    const { id, args, user } = command;
    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          {
            id: id,
          },
          {
            propertyId: user.propertyId,
          },
        ],
      },
      include: {
        tracks: {
          include: {
            track: true,
          },
        },
      },
    });
    if (!item) {
      throw new NotFoundException('playlist not found');
    }
    try {
      const devices = await this.prisma.device.findMany({
        where: {
          AND: [
            {
              id: {
                in: args.deviceIds,
              },
            },
            {
              isActive: true,
            },
            {
              type: {
                not: 'web',
              },
            },
          ],
        },
      });

      if (devices.length === 0) {
        throw new NotFoundException('devices not found');
      }

      this.client.emit('schedule-reminder-mob', {
        property_id: user.propertyId,
        devices: devices,
        data: {
          schedule_id: null,
          playlist_id: item?.id,
          playlist_name: item?.name,
          uri: `playlist:${item?.id}`,
          type: 'execute',
          // start_time: this.convertToISO8601(item?.schedule?.startTime),
          // end_time: this.convertToISO8601(item?.schedule?.endTime),
          start_time: null,
          end_time: null,
          repeat_end: null,
          tracks:
            item?.tracks.map((track) => {
              return track?.track?.source;
              // return sc['uri'];
            }) || [],
        },
      });

      this.client.emit('schedule-reminder-atv', {
        property_id: user.propertyId,
        devices: devices,
        data: {
          schedule_id: null,
          playlist_id: item?.id,
          playlist_name: item?.name,
          type: 'execute',
          uri: `playlist:${item?.id}`,
          // start_time: this.convertToISO8601(item?.schedule?.startTime),
          // end_time: this.convertToISO8601(item?.schedule?.endTime),
          start_time: null,
          end_time: null,
          repeat_end: null,
          tracks:
            item?.tracks.map((track) => {
              return track?.track?.source;
              // return sc['uri'];
            }) || [],
        },
      });
      return 'successfully assign playlist to device';
    } catch (error) {
      return 'failed assign playlist to device';
    }
  }
}
