import { NotFoundException } from '@nestjs/common';
import { CommandHandler, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UnFollowPlaylistCommand } from '../impl';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(UnFollowPlaylistCommand)
export class UnFollowPlaylistHandler
  implements ICommandHandler<UnFollowPlaylistCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: UnFollowPlaylistCommand) {
    const { id, user } = command;
    const follower = await this.prisma.playlistFollower.findFirst({
      where: {
        AND: [
          {
            activationId: user.deviceId
          },
          {
            playlistId: id
          }
        ]
      }
    });
    if (!follower) {
      throw new NotFoundException('you are not followed');
    }
    try {
      await this.prisma.playlistFollower.delete({
        where: {
          id: follower.id
        }
      });

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.increaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });

      // Todo : should add new notification?

      return {
        status: 'success',
        message: 'successfully un follow playlist',
      };
    } catch (error) {
      return 'failed un follow playlist';
    }
  }
}
