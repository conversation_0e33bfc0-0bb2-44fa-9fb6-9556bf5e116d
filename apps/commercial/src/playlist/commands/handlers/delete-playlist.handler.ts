import { ConflictException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { DeletePlaylistCommand } from '../impl';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(DeletePlaylistCommand)
export class DeletePlaylistHandler
  implements ICommandHandler<DeletePlaylistCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher
  ) {}

  async execute(command: DeletePlaylistCommand) {
    const { id, user } = command;
    const item = await this.prisma.playlist.findFirst({ where: { AND: [{ id: id }, { propertyId: user.propertyId }, { activation: { id: user.deviceId } } ] } });
    if (!item) {
      throw new NotFoundException();
    }

    const inSchedule = await this.prisma.scheduleContent.findFirst({
      where: {
        playlist: {
          id: item.id
        }
      }
    });
    if (inSchedule) {
      throw new ConflictException('Cannot delete playlist because it used on active schedule')
    }

    try {
      await this.prisma.playlist.delete({ where: { id: item.id } });
      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.increaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });
      return 'successfully deleted playlist';
    } catch (error) {
      return 'failed deleted playlist';
    }
  }
}
