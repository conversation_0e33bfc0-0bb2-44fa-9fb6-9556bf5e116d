import { generateRandomColor, PlaylistPermission } from '@app/common';
import { <PERSON><PERSON><PERSON>ler, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { CreatePlaylistCommand } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import {
  BadRequestException,
  ForbiddenException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import renameFile from '@app/common/helpers/file-manager/rename-file';
import { Client } from 'minio';
import { Readable } from 'stream';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';

@CommandHandler(CreatePlaylistCommand)
export class CreatePlaylistHandler
  implements ICommandHandler<CreatePlaylistCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly gmirestService: GmiRESTService,
    private readonly configService: ConfigService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: CreatePlaylistCommand) {
    const { user, args, file } = command;

    const playlistPackage = await this.prisma.zoneFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.Playlist,
          },
          {
            activationId: user.deviceId,
          },
        ],
      },
    });

    if (!playlistPackage) {
      throw new ForbiddenException('Insufficient playlist quota');
    }
    if (playlistPackage.qouta < 1 && playlistPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient playlist quota');
    }

    let allTracks = [];
    let totalDuration = 0;

    if (args.tracks && args.tracks.length > 0) {
      const trackKeys = args.tracks.map((trackDto) => trackDto.id);

      const existingTracks = await this.prisma.track.findMany({
        where: { key: { in: trackKeys } },
      });

      const existingTrackKeys = existingTracks.map((track) => track.key);
      const missingTrackKeys = trackKeys.filter(
        (key) => !existingTrackKeys.includes(key),
      );

      const gmiTrackPromises = missingTrackKeys.map((key) =>
        this.gmirestService.melodivaUse(key),
      );
      const gmiTracks = await Promise.all(gmiTrackPromises);

      let createdTracks = [];
      if (gmiTracks.length > 0) {
        await this.prisma.track.createMany({
          data: gmiTracks.map((gmiTrack) => ({
            key: gmiTrack.key,
            source: gmiTrack,
          })),
          skipDuplicates: true,
        });

        createdTracks = await this.prisma.track.findMany({
          where: { key: { in: missingTrackKeys } },
        });
      }

      allTracks = [...existingTracks, ...createdTracks];

      totalDuration = allTracks.reduce((sum, track) => {
        const duration = track.source?.duration
          ? parseInt(track.source.duration, 10)
          : 0;
        return sum + duration;
      }, 0);
    }

    const existingPlaylist = await this.prisma.playlist.findFirst({
      where: {
        name: args.name,
        activationId: user.deviceId,
      },
    });

    if (existingPlaylist) {
      throw new BadRequestException('Playlist with this name already exists');
    }

    try {
      const newPlaylist = await this.prisma.playlist.create({
        data: {
          name: args.name,
          property: { connect: { id: user.propertyId } },
          description: args.description,
          color: args.color || generateRandomColor(),
          activation: { connect: { id: user.deviceId } },
          totalDuration,
          permission: PlaylistPermission.PRIVATE,
        },
      });

      if (allTracks.length > 0) {
        await this.prisma.trackOnPlaylist.createMany({
          data: allTracks.map((track, index) => ({
            playlistId: newPlaylist.id,
            trackId: track.id,
            order: index,
          })),
          skipDuplicates: true,
        });
      }

      if (file) {
        const fileName = renameFile(file.originalname);
        const minioClient = new Client({
          endPoint: this.configService.get('MINIO_ENDPOINT'),
          port: parseInt(this.configService.get('MINIO_PORT') || '9000', 10),
          useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
          accessKey: this.configService.get('MINIO_ACCESS_KEY'),
          secretKey: this.configService.get('MINIO_SECRET_KEY'),
        });

        let fileLocation = '';
        if (this.configService.get('STORAGE_PROVIDER') === 'minio') {
          const useHttps =
            this.configService.get('MINIO_USE_SSL') === 'true'
              ? 'https'
              : 'http';

          fileLocation = `${useHttps}://${this.configService.get(
            'MINIO_ENDPOINT',
          )}:${this.configService.get('MINIO_PORT')}/${this.configService.get(
            `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
          )}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`;
        }

        const readableStream = new Readable({
          read() {
            this.push(file.buffer);
            this.push(null);
          },
        });

        await minioClient.putObject(
          this.configService.get(
            `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
          ),
          `${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
          readableStream,
          file.size,
          { 'Content-Type': file.mimetype }
        );

        await this.prisma.playlistThumbnail.create({
          data: {
            path: `${this.configService.get(
              `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
            )}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`,
            fileName: fileName,
            size: file.size,
            caption: file.originalname.slice(0, -4),
            encoding: file.encoding,
            mimeType: file.mimetype,
            name: file.originalname.slice(0, -4),
            playlist: {
              connect: {
                id: newPlaylist.id,
              },
            },
          },
        });
      } else {
        console.log('No thumbnail provided, skipping upload.');
      }

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.decreaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });

      return {
        statusCode: HttpStatus.CREATED,
        message: 'successfully Created Playlist',
        data: newPlaylist.id,
      };
    } catch (error) {
      throw new InternalServerErrorException(error.message);
    }
  }
}
