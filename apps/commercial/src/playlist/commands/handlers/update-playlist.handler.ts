import { NotFoundException } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdatePlaylistCommand } from '../impl';

@CommandHandler(UpdatePlaylistCommand)
export class UpdatePlaylistHandler
  implements ICommandHandler<UpdatePlaylistCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdatePlaylistCommand) {
    const { id, args, user } = command;
    const color = args.color ? { color: args.color } : undefined;
    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          { id: id },
          {
            property: { id: user.propertyId },
          },
        ],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    try {
      const existingPlaylist = await this.prisma.playlist.findFirst({
        where: {
          AND: [
            { name: args.name },
            {
              activation: { id: user.deviceId },
            },
            {
              property: { id: user.propertyId }
            },
            { NOT: { id } },
          ],
        },
      });

      if (existingPlaylist) {
        return 'Playlist with this name already exists';
      }
      
      await this.prisma.playlist.update({
        where: { id: id },
        data: {
          name: args.name,
          description: args.description,
          ...color,
        },
      });
      return 'successfully updated playlist';
    } catch (error) {
      return 'failed updated playlist';
    }
  }
}
