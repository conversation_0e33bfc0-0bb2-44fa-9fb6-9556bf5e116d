import { AddAlbumToPlaylistHandler } from './add-album-to-playlist.handler';
import { AddArtistToPlaylistHandler } from './add-artist-to-playlist.handler';
import { AssignPlaylistToDeviceHandler } from './assign-playlist-to-device.handler';
import { CreatePlaylistHandler } from './create-playlist.handler';
import { DeleteFollowPlaylistHandler } from './delete-followers-playlist.handler';
import { DeletePlaylistHandler } from './delete-playlist.handler';
import { FollowPlaylistHandler } from './follow-playlist.handler';
import { ImportPlaylistHandler } from './import-playlist.handler';
import { ThumbnailPlaylistHandler } from './thumbnail-playlist.handler';
import { UnFollowPlaylistHandler } from './un-follow-playlist.handler';
import { UpdatePlaylistPermissionHandler } from './update-playlist-permission.handler';
import { UpdatePlaylistHandler } from './update-playlist.handler';

export const PlaylistCommandHandlers = [
  Create<PERSON>laylist<PERSON>and<PERSON>,
  UpdatePlaylist<PERSON>andler,
  DeletePlaylistHandler,
  AssignPlaylistToDeviceHandler,
  AddArtistToPlaylistHandler,
  AddAlbumToPlaylistHandler,
  ThumbnailPlaylistHandler,
  UpdatePlaylistPermissionHandler,
  FollowPlaylistHandler,
  DeleteFollowPlaylistHandler,
  UnFollowPlaylistHandler,
  ImportPlaylistHandler
];
