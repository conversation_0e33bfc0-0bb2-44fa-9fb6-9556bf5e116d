import { BadRequestException, ForbiddenException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON>, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { FollowPlaylistCommand } from '../impl';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(FollowPlaylistCommand)
export class FollowPlaylistHandler
  implements ICommandHandler<FollowPlaylistCommand>
{
  constructor(
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: FollowPlaylistCommand) {
    const { id, user } = command;

    const playlistPackage = await this.prisma.zoneFeature.findFirst({
      where: {
        AND: [
          {
            featureId: FEATURE.Playlist,
          },
          {
            activationId: user.deviceId,
          },
        ],
      },
    });

    if (!playlistPackage) {
      throw new ForbiddenException('Insufficient playlist quota');
    }
    if (playlistPackage.qouta < 1 && playlistPackage.qouta !== -1) {
      throw new ForbiddenException('Insufficient playlist quota');
    }

    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          { id: id },
          {
            type: 'tracklist'
          }
          // {
          //   activationId: { not: null },
          // },
          // { propertyId: { not: null } }
        ],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }
    if (item.activationId === user.deviceId) {
      throw new BadRequestException('owner cannot follow this playlist');
    }
    try {
      await this.prisma.playlistFollower.create({
        data: {
          playlist: {
            connect: {
              id: item.id
            }
          },
          activation: {
            connect: {
              id: user.deviceId
            }
          }
        },
      });

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.decreaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });
      // Todo : should add new notification?

      return {
        status: 'success',
        message: 'successfully followed playlist',
      };
    } catch (error) {
      return 'failed followed playlist';
    }
  }
}
