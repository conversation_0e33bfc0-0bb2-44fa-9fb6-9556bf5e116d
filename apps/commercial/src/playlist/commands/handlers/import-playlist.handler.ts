import { HttpService } from '@nestjs/axios';
import { <PERSON><PERSON>andler, EventPublisher, ICommandHandler } from '@nestjs/cqrs';
import { firstValueFrom } from 'rxjs';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { PrismaService } from 'libs/prisma';
import { generateRandomColor } from '@app/common';
import { BadRequestException, ForbiddenException, HttpException, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { ImportPlaylistCommand } from '../impl';
import { ConfigService } from '@nestjs/config';
import { Client } from 'minio';
import { Readable } from 'stream';
import renameFile from '@app/common/helpers/file-manager/rename-file';
import { randStr } from 'libs/utils/rand-str.util';
import { partitionArray } from 'libs/utils/partition-array.util';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@CommandHandler(ImportPlaylistCommand)
export class ImportPlaylistHandler
  implements ICommandHandler<ImportPlaylistCommand>
{
  constructor(
    private readonly httpService: HttpService,
    private readonly gmiService: GmiRESTService,
    private readonly configService: ConfigService,
    private prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(command: ImportPlaylistCommand) {
    const { user, args } = command;
    try {
      const playlistPackage = await this.prisma.zoneFeature.findFirst({
        where: {
          AND: [
            {
              featureId: FEATURE.Playlist,
            },
            {
              activationId: user.deviceId,
            },
          ],
        },
      });
  
      if (!playlistPackage) {
        throw new ForbiddenException('Insufficient playlist quota');
      }
      if (playlistPackage.qouta < 1 && playlistPackage.qouta !== -1) {
        throw new ForbiddenException('Insufficient playlist quota');
      }

      const tracksMap: Map<string, number> = new Map();
      const tracks: string[] = [];
      const gmiTracks: any[] = [];

      // fetching user playlists
      const reqPlaylist = await firstValueFrom(
        this.httpService.get(
          args.url,
        )
      );

      if (reqPlaylist.data) {
        if (reqPlaylist.data?.data?.name.length >= 200) {
          throw new BadRequestException('Playlist name to long');
        }
        if (reqPlaylist.data.data.tracks.length > 0) {
          for (let j of reqPlaylist.data.data.tracks) {
            tracksMap.set(j?.isrc, j.order);
            tracks.push(j?.isrc);
          }
        }
      } else {
        throw new NotFoundException('playlist not found')
      }

      const trackIdsPartition = partitionArray(tracks, 30);
      for (let i=0; i<=trackIdsPartition.length-1; i++) {
        const res = await this.gmiService.getTracksByIsrcs(trackIdsPartition[i]);
        gmiTracks.push(...res)
      }
      const res = Array.from(
        new Map(gmiTracks.map(track => [track.isrc, track])).values()
      ) as any;

      const totalDuration = res.reduce((sum, track) => {
        const duration = track?.duration ? parseInt(track.duration, 10) : 0;
        return sum + duration;
      }, 0);

      const newPlaylist = await this.prisma.$transaction(async (tr) => {
        const trackOnPlaylistData = [];

        const playlist = await tr.playlist.create({
          data: {
            name: reqPlaylist.data.data.name,
            property: { connect: { id: user.propertyId } },
            description: reqPlaylist.data.data.description,
            color: generateRandomColor(),
            activation: { connect: { id: user.deviceId } },
            totalDuration,
          },
        });
        for (let idx = 0; idx <= res.length-1; idx++) {
          const elm = res[idx];
          let track = await this.prisma.track.findFirst({
            where: { key: elm.id }, 
          });

          if (!track) {  
            track = await tr.track.create({
              data: {
                key: elm.id,
                source: elm,
              },
            });
          }

          const isTrackInPlaylist = await tr.trackOnPlaylist.findFirst({
            where: { trackId: track.id, playlistId: playlist.id },
          });

          if (!isTrackInPlaylist) {
            trackOnPlaylistData.push({
              playlistId: playlist.id,
              trackId: track.id,
              order: Array.from(tracksMap.entries())
              .find(([key, _]) => key === track.source['isrc'])?.[1],
            });
          }
        }
        
        await tr.trackOnPlaylist.createMany({
          data: trackOnPlaylistData,
        });

        return playlist;
      },
      {
        maxWait: 5000, // 5 seconds max wait to connect
        timeout: 120000, // 120 seconds timeout
        retry: 3
      });

      if (reqPlaylist.data.data.images.length > 0) {
        const minioClient = new Client({
          endPoint: this.configService.get('MINIO_ENDPOINT'),
          port: parseInt(this.configService.get('MINIO_PORT') || '9000', 10),
          useSSL: this.configService.get('MINIO_USE_SSL') === 'true',
          accessKey: this.configService.get('MINIO_ACCESS_KEY'),
          secretKey: this.configService.get('MINIO_SECRET_KEY'),
        });

        let fileLocation = '';
        if (this.configService.get('STORAGE_PROVIDER') === 'minio') {
          const useHttps =
            this.configService.get('MINIO_USE_SSL') === 'true'
              ? 'https'
              : 'http';

          fileLocation = `${useHttps}://${this.configService.get(
            'MINIO_ENDPOINT',
          )}:${this.configService.get('MINIO_PORT')}/${this.configService.get(
            `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
          )}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`;
        }

        const response = await firstValueFrom(
          this.httpService.get(reqPlaylist.data.data.images[0].url, { responseType: 'stream' })
        );

        let originalFileName: string | undefined;
        const contentDisposition = response.headers['content-disposition'];

        if (contentDisposition) {
          const match = contentDisposition.match(/filename="?([^"]+)"?/);
          if (match) {
            originalFileName = match[1];
          }
        } else {
          originalFileName = randStr(32)
        }

        const fileName = renameFile(originalFileName);

        const stream = response.data as Readable;
        
        await minioClient.putObject(
          this.configService.get(
            `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
          ),
          `${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}/${fileName}`,
          stream,
          response.headers['content-length'],
          { 'Content-Type': response.headers['content-type'] }
        );

        await this.prisma.playlistThumbnail.create({
          data: {
            path: `${this.configService.get(
              `${this.configService.get('STORAGE_PROVIDER').toUpperCase()}_BUCKET_NAME`,
            )}/${this.configService.get<string>('PLAYLIST_THUMBNAIL_FOLDER')}`,
            fileName: fileName,
            size: response.headers['content-length'] ? Number(response.headers['content-length']) : 0,
            encoding: response.headers['content-encoding'] ?? 'identity',
            mimeType: response.headers['content-type'],
            name: originalFileName.slice(0, -4),
            playlist: {
              connect: {
                id: newPlaylist.id,
              },
            },
          },
        });
      } else {
        console.log('No thumbnail provided, skipping upload.');
      }

      const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      const limiter = new limiterModel();
      limiter.decreaseFeatureQty({
        id: FEATURE.Playlist,
        propertyId: user.propertyId,
        deviceId: user.deviceId
      });

      return 'success import playlist';
    } catch (error) {
      console.log(error)
      if (error?.response?.status === 404) {
        throw new NotFoundException(error?.response?.data?.error?.message)
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorException('failed import playlist');
    }
  }
}
