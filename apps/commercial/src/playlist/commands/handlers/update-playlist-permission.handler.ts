import { BadRequestException, HttpException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { UpdatePlaylistPermissionCommand } from '../impl';
import { generateRandomString, PlaylistPermission } from '@app/common';

@CommandHandler(UpdatePlaylistPermissionCommand)
export class UpdatePlaylistPermissionHandler
  implements ICommandHandler<UpdatePlaylistPermissionCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: UpdatePlaylistPermissionCommand) {
    const { id, args, user } = command;
    const item = await this.prisma.playlist.findFirst({
      where: {
        AND: [
          { id: id },
          {
            activation: { id: user.deviceId },
          },
          {
            property: { id: user.propertyId },
          },
        ],
      },
    });
    if (!item) {
      throw new NotFoundException('Playlist not found');
    }

    try {
      switch(args.permission) {
        case PlaylistPermission.PRIVATE:
        case PlaylistPermission.PUBLIC:
          await this.prisma.$transaction(async (tr) => {
            await tr.playlist.update({
              where: { id: id },
              data: {
                permission: args.permission,
                linkCode: null
              },
            });

            await tr.playlistAccess.deleteMany({
              where: {
                playlist: {
                  id: item.id
                }
              }
            });
          });
    
          return {
            status: 'success',
            message: 'successfully updated playlist permission',
          };
        case PlaylistPermission.RESTRICTED:
          if (args.username.length === 0) {
            throw new BadRequestException('invalid username')
          };

          const userNames = await this.prisma.userProperty.findMany({
            where: {
              user: {
                username: {
                  in: args.username
                }
              }
            },
            include: {
              user: {
                select: {
                  activation: {
                    select: {
                      id: true
                    }
                  },
                  isAdmin: true
                }
              },
              property: {
                select: {
                  id: true
                }
              }
            }
          });

          if (userNames.length === 0) {
            throw new BadRequestException('invalid usernames')
          }

          if (args.groupSharing) {
            if(!userNames.some((usr) => usr.user.isAdmin === true)) {
              throw new BadRequestException('invalid usernames')
            }
          }

          await this.prisma.$transaction(async (tr) => {
            await tr.playlist.update({
              where: { id: id },
              data: {
                permission: args.permission,
              },
            });

            for (let i of userNames) {
              const existingAccess = await tr.playlistAccess.findFirst({
                where: {
                  AND: [
                    {
                      playlistId: item.id
                    },
                    args.groupSharing ? {
                      propertyId: i.propertyId
                    } : {
                      activationId: i.user.activation.id
                    }
                  ]
                }
              });
              if (existingAccess) {
                await tr.playlistAccess.update({
                  where: {
                    id: existingAccess.id
                  },
                  data: {
                    playlistId: item.id,
                    activationId: !args.groupSharing ? i.user.activation.id : undefined,
                    propertyId: args.groupSharing ? i.property.id : undefined
                  }
                });
              } else {
                await tr.playlistAccess.create({
                  data: {
                    playlistId: item.id,
                    activationId: !args.groupSharing ? i.user.activation.id : undefined,
                    propertyId: args.groupSharing ? i.property.id : undefined
                  }
                });
              }
            }
          },
          {
            maxWait: 5000, // 5 seconds max wait to connect
            timeout: 20000, // 20 seconds timeout
            retry: 3
          });

          return {
            status: 'success',
            message: 'successfully updated playlist permission',
          };
        case PlaylistPermission.LINK:
          const code = generateRandomString(32);
          await this.prisma.playlist.update({
            where: { id: id },
            data: {
              linkCode: code,
              permission: PlaylistPermission.LINK,
            },
          });
    
          return {
            status: 'success',
            message: 'successfully updated playlist permission',
            data: {
              id,
              code,
            },
          };
        default:
          throw new BadRequestException('unknowon permission set')
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw new BadRequestException(error.message);
      }
      console.log(error)
      return 'failed updated playlist permission';
    }
  }
}
