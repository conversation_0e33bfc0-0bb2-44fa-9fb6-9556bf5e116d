import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  MaxFileSizeValidator,
  Param,
  ParseFilePipe,
  Patch,
  Post,
  Query,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiHeader,
  ApiOperation,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  AddAlbumToPlaylistCommand,
  AddArtistToPlaylistCommand,
  AssignPlaylistToDeviceCommand,
  CreatePlaylistCommand,
  DeleteFollowerPlaylistCommand,
  DeletePlaylistCommand,
  FollowPlaylistCommand,
  ImportPlaylistCommand,
  ThumbnailPlaylistCommand,
  UnFollowPlaylistCommand,
  UpdatePlaylistCommand,
  UpdatePlaylistPermissionCommand,
} from './commands';
import { CreatePlaylistDto } from './dto/create-playlist.dto';
import { FilterPlaylistDto } from './dto/filter-playlist.dto';
import { UpdatePlaylistDto } from './dto/update-playlist.dto';
import { GetPlaylistFollowersQuery, GetPlaylistRecipientsQuery, GetPlaylistQuery, GetPlaylistsQuery } from './queries';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { Throttle } from '@nestjs/throttler';
import { AssignPlaylistDeviceDto } from './dto/assing-playlist-to-device.dto';
import { AddArtistToPlaylistDto } from './dto/add-artist-to-playlist.dto';
import { Payload } from '@nestjs/microservices';
import { AddAlbumToPlaylistDto } from './dto/add-album-to-playlist.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { from, map } from 'rxjs';
import { TrackDto } from '../track/dto/track.dto';
import { LoggingInterceptor } from '../validator/logging.validator';
import { plainToClass } from 'class-transformer';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';
import { FilterPlaylistCodeDto } from './dto/filter-playlist-code.dto';
import { UpdatePlaylistPermissionDto } from './dto/update-playlist-permission.dto';
import { ImportPlaylistDto } from './dto/import-playlist.dto';

@ApiTags('Playlist')
@Controller('playlist')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class PlaylistController {
  constructor(
    private queryBus: QueryBus,
    private commandBus: CommandBus,
  ) {}

  @Post()
  @UseInterceptors(FileInterceptor('file'))
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'create playlist and tracks' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            description: { type: 'string' },
            color: { type: 'string' },
            tracks: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                },
              },
            },
          },
        },
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  async create(
    @Request() req,
    @Body('data') data: string,
    @User() user: ICurrentUser,
    @UploadedFile()
    file: Express.Multer.File,
  ) {
    if (file) {
      if (file.size > 1024 * 1024 * 2) {
        throw new BadRequestException('File size exceeds 2MB');
      }
      if (!['image/jpeg', 'image/png'].includes(file.mimetype)) {
        throw new BadRequestException('Invalid file type');
      }
    }
    try {
      const createPlaylistDto = plainToClass(
        CreatePlaylistDto,
        JSON.parse(data),
      );
      return this.commandBus.execute(
        new CreatePlaylistCommand(createPlaylistDto, user, file),
      );
    } catch (error) {
      console.error('Error parsing JSON:', error);
      throw new BadRequestException('Invalid JSON data');
    }
  }

  @Patch(':id/followers')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  //@UseGuards(AccessFeatureGuard)
  //@AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'follow playlist' })
  async follow(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new FollowPlaylistCommand(id, user),
    );
  }

  @Patch(':id/unfollow')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  //@UseGuards(AccessFeatureGuard)
  //@AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'unfollow playlist' })
  async unfollow(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new UnFollowPlaylistCommand(id, user),
    );
  }

  @Patch(':id/permission')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'update playlist permission' })
  async permission(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Body() payload: UpdatePlaylistPermissionDto
  ) {
    return this.commandBus.execute(
      new UpdatePlaylistPermissionCommand(id, payload, user),
    );
  }

  @Get(':id/recipients')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'get playlist recipients' })
  async getPermission(
    @Param('id') id: string,
    @User() user: ICurrentUser,
  ) {
    return this.queryBus.execute(
      new GetPlaylistRecipientsQuery(id, user),
    );
  }

  @Get(':id/followers')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'get playlist followers' })
  followers(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Query() filter: FilterPlaylistDto
  ) {
    return this.queryBus.execute(new GetPlaylistFollowersQuery(id, user, filter));
  }

  @Delete(':id/followers/:followerId')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'delete playlist follower' })
  deleteFollower(
    @Param('id') id: string,
    @Param('followerId') followerId: string,
    @User() user: ICurrentUser
  ) {
    return this.commandBus.execute(new DeleteFollowerPlaylistCommand(id, user, followerId));
  }

  @Patch(':id/artist')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'add artist to playlist' })
  addArtist(
    @Param('id') id: string,
    @Body() payload: AddArtistToPlaylistDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new AddArtistToPlaylistCommand(id, payload.artistId, user),
    );
  }

  @Patch(':id/album')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'add album to playlist' })
  addAlbum(
    @Param('id') id: string,
    @Body() payload: AddAlbumToPlaylistDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new AddAlbumToPlaylistCommand(id, payload.albumId, user),
    );
  }

  @Get()
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'get all playlists' })
  findAll(@Query() filter: FilterPlaylistDto, @User() user: ICurrentUser) {
    return this.queryBus.execute(new GetPlaylistsQuery(user, filter));
  }

  @Get(':id')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'get playlist by id' })
  findOne(
    @Param('id') id: string,
    @User() user: ICurrentUser,
    @Query() filter: FilterPlaylistCodeDto
  ) {
    return this.queryBus.execute(new GetPlaylistQuery(id, filter, user));
  }

  @Patch(':id')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'update playlist' })
  update(
    @Param('id') id: string,
    @Body() updatePlaylistDto: UpdatePlaylistDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new UpdatePlaylistCommand(id, updatePlaylistDto, user),
    );
  }

  @Patch(':id/assign')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'assign playlist to device' })
  assignPlaylistDevice(
    @Param('id') id: string,
    @Body() payload: AssignPlaylistDeviceDto,
    @User() user: ICurrentUser,
  ) {
    return this.commandBus.execute(
      new AssignPlaylistToDeviceCommand(id, payload, user),
    );
  }

  @Patch(':id/thumbnail')
  @UseInterceptors(FileInterceptor('thumbnail'))
  @ApiOperation({ summary: 'edit playlist thumbnail' })
  @ApiConsumes('multipart/form-data')
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        thumbnail: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  changeThumbnail(
    @Param('id') playlistId: string,
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 1024 * 1024 * 2 }),
          new FileTypeValidator({ fileType: 'image/jpeg|image/png' }),
        ],
      }),
    )
    file: Express.Multer.File,
  ) {
    const fileStream = from([file.buffer]).pipe(
      map((buffer) => ({ chunk: buffer })),
    );
    return this.commandBus.execute(
      new ThumbnailPlaylistCommand(playlistId, fileStream, file),
    );
  }

  @Delete(':id')
  @Throttle({ default: { limit: 30, ttl: 60000 } })
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  @ApiOperation({ summary: 'delete playlist' })
  remove(@Param('id') id: string, @User() user: ICurrentUser) {
    return this.commandBus.execute(new DeletePlaylistCommand(id, user));
  }

  @Post('import')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  @UseGuards(AccessFeatureGuard)
  @AccessFeature(FEATURE.Playlist)
  async import(
    @User() user: ICurrentUser,
    @Body() payload: ImportPlaylistDto,
  ) {
    return await this.commandBus.execute(
      new ImportPlaylistCommand(payload, user),
    );
  }
}
