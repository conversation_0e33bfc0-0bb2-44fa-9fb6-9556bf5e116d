import { Modu<PERSON> } from '@nestjs/common';
import { PlaylistController } from './playlist.controller';
import { PlaylistQueryHandlers } from './queries';
import { PlaylistCommandHandlers } from './commands';
import { CqrsModule } from '@nestjs/cqrs';
import { GmiModule } from '../gmi/gmi.module';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PlaylistEventHandlers } from './events/handlers';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    CqrsModule,
    GmiModule,
    HttpModule,
    ClientsModule.registerAsync([
      {
        name: 'mobile',
        imports: [ConfigModule],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.REDIS,
          options: {
            host: configService.get<string>('REDIS_HOST'),
            port: configService.get<number>('REDIS_PORT'),
            password: configService.get<string>('REDIS_PASS'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [PlaylistController],
  providers: [
    ...PlaylistQueryHandlers,
    ...PlaylistCommandHandlers,
    ...PlaylistEventHandlers
  ],
})
export class PlaylistModule {}
