import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsHexColor,
  IsOptional,
  IsString,
  MaxLength,
  ValidateIf,
} from 'class-validator';

export class UpdatePlaylistDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  description: string;
  @ApiProperty()
  @IsString()
  @MaxLength(50)
  name: string;
  @IsOptional()
  @ValidateIf((prop) => prop.color != '')
  @IsString()
  @IsHexColor()
  color: string;
}
