import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsHexColor,
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  <PERSON><PERSON>ength,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { TrackDto } from '../../track/dto/track.dto';

export class CreatePlaylistDto {
  @IsOptional()
  @IsString()
  @ApiPropertyOptional()
  description: string;

  @IsOptional()
  @ValidateIf((prop) => prop.color != '')
  @IsString()
  @IsHexColor()
  color?: string;

  @ApiProperty()
  @IsString()
  @MaxLength(50)
  @MinLength(1)
  @IsNotEmpty()
  @ValidateIf((prop) => prop.name != '')
  name: string;

  @ValidateNested({ each: true })
  @ApiPropertyOptional({ type: [TrackDto] })
  @Type(() => TrackDto)
  @IsOptional()
  tracks?: TrackDto[];

  @ApiPropertyOptional({ type: 'string', format: 'binary' })
  @IsOptional()
  file?: Express.Multer.File;
}