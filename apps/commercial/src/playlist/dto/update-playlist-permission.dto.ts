import { PlaylistPermission } from '@app/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
} from 'class-validator';

export class UpdatePlaylistPermissionDto {
  @IsEnum(PlaylistPermission)
  @ApiProperty({ enum: PlaylistPermission, default: PlaylistPermission.PRIVATE })
  permission: PlaylistPermission;

  @ApiPropertyOptional({ type: [String] })
  @IsOptional()
  // @IsEmail({}, { each: true })
  username: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  groupSharing: boolean;
}
