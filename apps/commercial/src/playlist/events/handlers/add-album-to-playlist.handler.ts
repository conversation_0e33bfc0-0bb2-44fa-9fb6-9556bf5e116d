import { EventPublisher, EventsHandler, IEventHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { AddAlbumToPlaylistEvent } from '../impl';
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { FEATURE } from 'apps/commercial/src/auth/enum/feature.enum';
import { LimiterModel } from 'apps/commercial/src/limiter/model/limiter.model';

@EventsHandler(AddAlbumToPlaylistEvent)
export class AddAlbumToPlaylistHandler implements IEventHandler<AddAlbumToPlaylistEvent> {
  constructor(
    private readonly prisma: PrismaService,
    private gmiRestService: GmiRESTService,
    private readonly publisher: EventPublisher,
  ) {}

  async handle(event: AddAlbumToPlaylistEvent) {
    const { id, albumId, user } = event;
    let ordering: number = 0;
    const tracks = []
    let lastCursor = '';

    try {
      const lastRecord = await this.prisma.trackOnPlaylist.findFirst({
        where: { playlist: { AND: [ { id: id } ] } },
        orderBy: { order: 'desc' },
      });
      if (lastRecord) {
        ordering = Number(lastRecord.order);
      }
  
      const trackArtist = await this.gmiRestService.listTrackAlbum({ id: albumId, limit: 100 });
      if (trackArtist?.data.length > 0) {
        for (let i in trackArtist.data) {
          tracks.push(trackArtist.data[i])
        }
        if (trackArtist?.meta?.hashNextPage) {
          lastCursor = trackArtist?.meta?.lastCursor;
        }
  
        while (lastCursor != '') {
          const newTrackArtist = await this.gmiRestService.listTrackArtist({ id: albumId, limit: 100, lastCursor: lastCursor });
          if (newTrackArtist?.data?.data) {
            for (let i in newTrackArtist?.data.data) {
              tracks.push(newTrackArtist.data[i]);
            }
            if (newTrackArtist?.data?.meta?.hashNextPage) {
              lastCursor = newTrackArtist?.data?.meta?.lastCursor;
            }
          }
        }
      }
      await this.prisma.$transaction(async (tr) => {
        const trackOnPlaylistData = [];

        for (let idx = 0; idx < tracks.length; idx++) {
          const elm = tracks[idx];
          let track = await this.prisma.track.findFirst({
            where: { key: elm.id }, 
          });

          if (!track) {
            track = await tr.track.create({
              data: {
                  key: tracks[idx].id,
                  source: tracks[idx],
              }
            });
          }

          const isTrackInPlaylist = await tr.trackOnPlaylist.findFirst({
            where: { trackId: track.id, playlistId: id },
          });

          if (!isTrackInPlaylist) {
            trackOnPlaylistData.push({
              playlistId: id,
              trackId: track.id,
              order: ordering + idx + 1,
            });
          }
        }

        await tr.trackOnPlaylist.createMany({
          data: trackOnPlaylistData,
        });
      },
      {
        maxWait: 10000, // 10 seconds max wait to connect
        timeout: 20000, // 20 seconds timeout
        retry: 3
      });

      // const limiterModel = this.publisher.mergeClassContext(LimiterModel);
      // const limiter = new limiterModel();
      // limiter.decreaseFeatureQty({
      //   id: FEATURE.Playlist,
      //   propertyId: user.propertyId,
      //   deviceId: user.deviceId
      // });
    } catch (error) {
      return 'failed add notification';
    }
  }
}
