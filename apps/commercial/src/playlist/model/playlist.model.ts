import { AggregateRoot } from '@nestjs/cqrs';
import { AddAlbumToPlaylistEvent, AddArtistToPlaylistEvent } from '../events/impl';
import { ICurrentUser } from '../../auth/strategies/types/user.type';

export class PlaylistModel extends AggregateRoot {
  constructor(private user: ICurrentUser) {
    super();
    this.autoCommit = true;
  }

  addArtistToPlaylist(playlistId: string, artistId: string) {
    this.apply(new AddArtistToPlaylistEvent(playlistId, artistId, this.user));
  }

  addAlbumToPlaylist(playlistId: string, albumId: string) {
    this.apply(new AddAlbumToPlaylistEvent(playlistId, albumId, this.user));
  }
}
