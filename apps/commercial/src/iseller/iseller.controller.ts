import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { CreateIsellerCommand } from './commands';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';
@ApiTags('Iseller')
@Controller('iseller')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class IsellerController {
  constructor(private commandBus: CommandBus) {}

  @Get(':orderId')
  getToken(@Param('orderId') orderId: string) {
    // return this.commandBus.execute(new TokenIsellerCommand());
    return this.commandBus.execute(new CreateIsellerCommand(orderId));
  }
}
