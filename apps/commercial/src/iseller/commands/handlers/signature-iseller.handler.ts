import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { SignatureIsellerCommand } from '../impl/signature-iseller.command';
import { PrismaService } from 'libs/prisma';
import { generateMd5 } from '@app/common';

@CommandHandler(SignatureIsellerCommand)
export class SignatureIsellerHandler
  implements ICommandHandler<SignatureIsellerCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: SignatureIsellerCommand) {
    const { id, price } = command;
    const { option } = await this.prisma.setting.findFirst({
      where: { type: 'iseller' },
    });

    const signature_ =
      option['clientId'] +
      '.' +
      id +
      '.' +
      price +
      '.' +
      option['clientSecret'];

    const signature = generateMd5(signature_);

    return signature;
  }
}
