import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import queryString from 'node:querystring';
import { firstValueFrom } from 'rxjs';
import { TokenIsellerCommand } from '../impl';

@CommandHandler(TokenIsellerCommand)
export class TokenIsellerHandler
  implements ICommandHandler<TokenIsellerCommand>
{
  private readonly logger = new Logger();
  constructor(
    private httpService: HttpService,
    private prisma: PrismaService,
  ) {}

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async execute(command: TokenIsellerCommand) {
    const { option } = await this.prisma.setting.findFirst({
      where: { type: 'iseller' },
    });

    const data = { grant_type: 'client_credentials' };
    try {
      const token = await firstValue<PERSON>rom(
        this.httpService.post(
          option['accessTokenUrl'],
          queryString.stringify(data),
          {
            headers: { 'content-type': 'application/x-www-form-urlencoded' },
            auth: {
              username: option['clientId'],
              password: option['clientSecret'],
            },
          },
        ),
      );
      return token.data;
    } catch (error) {
      console.log(error)
      this.logger.error('failed get token from iseller');
    }
  }
}
