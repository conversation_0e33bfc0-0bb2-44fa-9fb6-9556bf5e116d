import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetPostalQuery } from '../impl';

@QueryHandler(GetPostalQuery)
export class GetPostalHandler implements IQueryHandler<GetPostalQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetPostalQuery) {
    const { id } = query;
    const item = await this.prisma.postal.findFirst({ where: { id: id }, include: { provinces: true } });
    if (!item) {
      throw new NotFoundException();
    }
    const map = Object.assign(item, { province: item.provinces.name });
    delete map.provinces;
    return map;
  }
}
