import { <PERSON><PERSON>uery<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetUrbanQuery } from '../impl';

@QueryHandler(GetUrbanQuery)
export class GetUrbanHandler implements IQueryHandler<GetUrbanQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetUrbanQuery) {
    const { filter } = query;
    const item = await this.prisma.postal.groupBy({
      by: ['urban'],
      where: {
        AND: [
          { city: filter.city },
          { provinces: { id: filter.provinceId } },
          { district: filter.district },
        ],
      },
      orderBy: { urban: 'asc' },
    });
    return item;
  }
}
