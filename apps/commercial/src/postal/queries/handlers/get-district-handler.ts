import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetDistrictQuery } from '../impl';

@QueryHandler(GetDistrictQuery)
export class GetDistrictHandler implements IQueryHandler<GetDistrictQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetDistrictQuery) {
    const { filter } = query;
    const item = await this.prisma.postal.groupBy({
      by: ['district'],
      where: {
        AND: [{ city: filter.city }, { provinces: { id: filter.provinceId } }],
      },
      orderBy: { district: 'asc' },
    });
    return item;
  }
}
