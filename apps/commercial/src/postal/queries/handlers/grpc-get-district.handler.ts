import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetDistrictGrpc } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ListDistrictResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetDistrictGrpc)
export class GetDistrictGrpcHandler implements IQueryHandler<GetDistrictGrpc> {
  constructor(
    private readonly prisma: PrismaService
  ) {

  }
  async execute(query: GetDistrictGrpc): Promise<ListDistrictResponse> {
    const { provinceId, city } = query
    const items = await this.prisma.postal.groupBy({
      by: ['district'],
      where: {
        AND: [
          { city: city },
          { provinces: { id: provinceId } }
        ]
      },
      orderBy: { district: 'asc' }
    })

    const response: ListDistrictResponse = {
      data: items.map(district => district.district),
      status: { code: 200, message: 'Success' },
    }
    return response
  }
}