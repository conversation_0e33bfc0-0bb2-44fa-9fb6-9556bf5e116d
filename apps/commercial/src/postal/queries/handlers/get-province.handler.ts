import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetProvinceQuery } from '../impl';

@QueryHandler(GetProvinceQuery)
export class GetProvinceHandler implements IQueryHandler<GetProvinceQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetProvinceQuery) {
    const { filter } = query;
    const search = filter.search || '';
    const items = this.prisma.provinces.findMany({
      where: { OR: [{ name: { contains: search, mode: 'insensitive' } }] },
      orderBy: { name: 'asc' },
    });
    return items;
  }
}
