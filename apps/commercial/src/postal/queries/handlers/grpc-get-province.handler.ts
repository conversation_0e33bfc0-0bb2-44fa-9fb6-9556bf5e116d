import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetProvinceGrpc } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ListProvinceResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetProvinceGrpc)
export class GetProvinceGrpcHandler implements IQueryHandler<GetProvinceGrpc> {
  constructor(
    private readonly prisma: PrismaService
  ) {

  }

  async execute(query: GetProvinceGrpc): Promise<ListProvinceResponse> {
    const { filter } = query
    const search = filter.query || ''

    const provinces = await this.prisma.provinces.findMany({
      where: {
        name: {
          contains: search, mode: 'insensitive'
        }
      }
    })

    const response: ListProvinceResponse = {
      data: provinces.map(province => ({
        id: province.id,
        name: province.name
      })),
      status: { code: 200, message: 'Success' },
    }

    return response
  }
}