import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetCitiesGrpc } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ListCityResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetCitiesGrpc)
export class GetCityGrpcHandler implements IQueryHandler<GetCitiesGrpc> {
  constructor(private readonly prisma: PrismaService) {
  }

  async execute(query: GetCitiesGrpc): Promise<ListCityResponse> {
    const { provinceId } = query

    const items = await this.prisma.postal.groupBy({
      by: ['city'],
      where: { provinces: { id: provinceId } },
      orderBy: { city: 'asc' },
    });

    const response: ListCityResponse = {
      data: items.map(item => item.city),
      status: { code: 200, message: 'Success' },
    };

    return response;
  }
}