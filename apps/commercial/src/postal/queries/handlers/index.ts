import { GetCityHandler } from './get-city.handler';
import { GetDistrictHandler } from './get-district-handler';
import { GetPostalHandler } from './get-postal.handler';
import { GetProvinceHandler } from './get-province.handler';
import { GetUrbanHandler } from './get-urban.handler';
import { GetZipCodeHandler } from './get-zipcode.handler';
import { GetCityGrpcHandler } from './grpc-get-city.handler';
import { GetDistrictGrpcHandler } from './grpc-get-district.handler';
import { GetPostalByIdsHandlerGrpc } from './grpc-get-postal-by-ids.handler';
import { GetProvinceGrpcHandler } from './grpc-get-province.handler';
import { GetUrbanGrpcHandler } from './grpc-get-urban.handler';
import { GetZipcodeGrpcHandler } from './grpc-get-zipcode.handler';

export const PostalQueryHandlers = [
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ce<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  GetUrbanHandler,
  GetZipCodeHandler,
  GetProvinceGrpcHandler,
  GetCityGrpcHandler,
  GetDistrictGrpcHandler,
  GetUrbanGrpcHandler,
  GetZipcodeGrpcHandler,
  GetPostalByIdsHandlerGrpc,
];
