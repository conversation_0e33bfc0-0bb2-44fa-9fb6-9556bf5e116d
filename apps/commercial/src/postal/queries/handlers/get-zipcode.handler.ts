import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetZipCodeQuery } from '../impl';

@QueryHandler(GetZipCodeQuery)
export class GetZipCodeHandler implements IQueryHandler<GetZipCodeQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetZipCodeQuery) {
    const { filter } = query;
    const item = await this.prisma.postal.findFirst({
      where: {
        AND: [
          { city: filter.city },
          { provinces: { id: filter.provinceId } },
          { district: filter.district },
          { urban: filter.urban },
        ],
      },
      select: { id: true, code: true },
    });
    return item;
  }
}
