import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetUrbanGrpc } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ListUrbanResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetUrbanGrpc)
export class GetUrbanGrpcHandler implements IQueryHandler<GetUrbanGrpc> {
  constructor(
    private readonly prisma: PrismaService
  ) {

  }

  async execute(query: GetUrbanGrpc): Promise<ListUrbanResponse> {
    const { provinceId, city, district } = query

    const items = await this.prisma.postal.groupBy({
      by: ['urban'],
      where: {
        AND: [
          { city: city },
          { provinces: { id: provinceId } },
          { district: district }
        ]
      },
      orderBy: { urban: 'asc' }
    })

    const response: ListUrbanResponse = {
      data: items.map(urban => urban.urban),
      status: { code: 200, message: 'Success' },
    }

    return response
  }
}