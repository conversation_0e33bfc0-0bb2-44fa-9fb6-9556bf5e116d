import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@nestjs/cqrs";
import { GetZipCodeGrpc } from "../impl";
import { PrismaService } from 'libs/prisma'; 
import { ZipCodeResponse } from "@app/proto-schema/index.internal";

@QueryHandler(GetZipCodeGrpc)
export class GetZipcodeGrpcHandler implements IQueryHandler<GetZipCodeGrpc> {
  constructor(
    private readonly prisma: PrismaService
  ) {

  }

  async execute(query: GetZipCodeGrpc): Promise<ZipCodeResponse> {
    const { provinceId, city, district, urban } = query

    const item = await this.prisma.postal.findFirst({
      where: {
        AND: [
          { city: city },
          { provinces: { id: provinceId } },
          { district: district },
          { urban: urban },
        ],
      },
      select: { id: true, code: true }
    })

    const response: ZipCodeResponse = {
      id: item.id,
      code: item.code,
    };

    return response
  }
}