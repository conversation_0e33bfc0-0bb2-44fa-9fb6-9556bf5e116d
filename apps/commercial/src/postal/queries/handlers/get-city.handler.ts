import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { GetCityQuery } from '../impl';

@QueryHandler(GetCityQuery)
export class GetCityHandler implements IQueryHandler<GetCityQuery> {
  constructor(private readonly prisma: PrismaService) {}

  async execute(query: GetCityQuery) {
    const { filter } = query;
    const items = await this.prisma.postal.groupBy({
      by: ['city'],
      where: { provinces: { id: filter.provinceId } },
      orderBy: { city: 'asc' },
    });
    return items;
  }
}
