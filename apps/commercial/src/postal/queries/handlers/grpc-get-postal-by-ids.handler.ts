import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ry<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPostalByIdsQueryGrpc } from '../impl';
import { PrismaService } from 'libs/prisma';
import { Postal } from '@prisma/client';
import { GetPostalByIdsResponse } from '@app/proto-schema/index.internal';

@QueryHandler(GetPostalByIdsQueryGrpc)
export class GetPostalByIdsHandlerGrpc
  implements IQueryHandler<GetPostalByIdsQueryGrpc> {
  constructor(private prisma: PrismaService) { }

  async execute(
    query: GetPostalByIdsQueryGrpc,
  ): Promise<GetPostalByIdsResponse> {
    const { args } = query;

    const data = await this.prisma.postal.findMany({
      where: {
        id: { in: args.postalIds },
      },
      include: {
        provinces: true,
      },
    });

    return {
      data: data.map(postal => ({
        id: postal.id,
        urban: postal.urban,
        city: postal.city,
        district: postal.district,
        code: postal.code,
        province: postal.provinces?.name || '',
        provinceId: postal.provincesId || ''
      }))
    };
  }
}
