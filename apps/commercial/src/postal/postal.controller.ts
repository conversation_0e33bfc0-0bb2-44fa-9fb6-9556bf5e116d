import { Controller, Get, NotFoundException, Query } from '@nestjs/common';
import { QueryBus } from '@nestjs/cqrs';
import { ApiTags } from '@nestjs/swagger';
import { FilterCityDto } from './dto/filter-city.dto';
import { FilterDistrictDto } from './dto/filter-district.dto';
import { FilterProvinceDto } from './dto/filter-profince.dto';
import { FilterUrbanDto } from './dto/filter-urban.dto';
import { FilterZipCodeDto } from './dto/filter-zipcode.dto';
import {
  GetCitiesGrpc,
  GetCityQuery,
  GetDistrictGrpc,
  GetDistrictQuery,
  GetPostalByIdsQueryGrpc,
  GetPostalQuery,
  GetProvinceGrpc,
  GetProvinceQuery,
  GetUrbanGrpc,
  GetUrbanQuery,
  GetZipCodeGrpc,
  GetZipCodeQuery,
} from './queries';
import { GrpcMethod, RpcException } from '@nestjs/microservices';
import {
  CityRequest,
  DistrictRequest,
  GetPostalByIdsRequest,
  GetPostalByIdsResponse,
  ListCityResponse,
  ListDistrictResponse,
  ListPostalResponse,
  ListProvinceResponse,
  ListUrbanResponse,
  POSTAL_SERVICE_NAME,
  ProvinceRequest,
  UrbanRequest,
  ZipCodeRequest,
  ZipCodeResponse,
} from '@app/proto-schema/index.internal';
import { Metadata, status } from '@grpc/grpc-js';
import { Id } from '@app/proto-schema/index.common';
import { Postal } from '@prisma/client';

@ApiTags('Postal')
@Controller('postal')
export class PostalController {
  constructor(private queryBus: QueryBus) {}

  @Get('province')
  findProvince(@Query() filter: FilterProvinceDto) {
    return this.queryBus.execute(new GetProvinceQuery(filter));
  }

  @Get('city')
  findCity(@Query() filter: FilterCityDto) {
    return this.queryBus.execute(new GetCityQuery(filter));
  }

  @Get('district')
  findAll(@Query() filter: FilterDistrictDto) {
    return this.queryBus.execute(new GetDistrictQuery(filter));
  }

  @Get('urban')
  findUrban(@Query() filter: FilterUrbanDto) {
    return this.queryBus.execute(new GetUrbanQuery(filter));
  }

  @Get('zipCode')
  findZipCode(@Query() filter: FilterZipCodeDto) {
    return this.queryBus.execute(new GetZipCodeQuery(filter));
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetProvince')
  async GetProvince(
    request: ProvinceRequest,
    meta: Metadata,
  ): Promise<ListProvinceResponse> {
    return this.queryBus.execute(new GetProvinceGrpc(request.search));
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetCity')
  async GetCity(
    request: CityRequest,
    meta: Metadata,
  ): Promise<ListCityResponse> {
    return this.queryBus.execute(new GetCitiesGrpc(request.provinceId));
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetDistrict')
  async GetDistrict(
    request: DistrictRequest,
    meta: Metadata,
  ): Promise<ListDistrictResponse> {
    return this.queryBus.execute(
      new GetDistrictGrpc(request.provinceId, request.city),
    );
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetUrban')
  async GetUrban(
    request: UrbanRequest,
    meta: Metadata,
  ): Promise<ListUrbanResponse> {
    return this.queryBus.execute(
      new GetUrbanGrpc(request.provinceId, request.city, request.district),
    );
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetZipCode')
  async GetZipCode(
    request: ZipCodeRequest,
    meta: Metadata,
  ): Promise<ZipCodeResponse> {
    return this.queryBus.execute(
      new GetZipCodeGrpc(
        request.provinceId,
        request.city,
        request.district,
        request.urban,
      ),
    );
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetPostal')
  async GetPostal(request: Id, meta: Metadata): Promise<ListProvinceResponse> {
    try {
      return this.queryBus.execute(new GetPostalQuery(request.id));
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'postal not found',
        });
      }
    }
  }

  @GrpcMethod(POSTAL_SERVICE_NAME, 'GetPostalByIds')
  async GetPostalByIds(
    request: GetPostalByIdsRequest,
    meta: Metadata,
  ): Promise<GetPostalByIdsResponse> {
    try {
      return this.queryBus.execute(new GetPostalByIdsQueryGrpc(request));
    } catch (err) {
      if (err instanceof NotFoundException) {
        throw new RpcException({
          code: status.NOT_FOUND,
          message: 'postal not found',
        });
      }
    }
  }
}
