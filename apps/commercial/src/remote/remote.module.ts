import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { JwtModule } from '@nestjs/jwt';
import { RemoteCommandHandlers } from './commands';
import { RemoteQueryHandlers } from './queries';
import { RemoteController } from './remote.controller';
import { RemoteDeviceEventController } from './remote-device.event.controller';
import { EventsModule } from '../events/events.module';
import { GmiModule } from '../gmi/gmi.module';
import { WebGateway } from '../events/web.gateway';
import { PlayerGateway } from '../events/player.gateway';
import { JinggleModule } from '../jinggle/jinggle.module';

@Module({
  imports: [CqrsModule, JwtModule, EventsModule, GmiModule, JinggleModule],
  controllers: [RemoteController, RemoteDeviceEventController],
  providers: [
    ...RemoteQueryHandlers,
    ...RemoteCommandHandlers,
    // TOLONG JANGAN DI TAMBAHIN YAAA
    // WebGateway,
    // PlayerGateway,
  ],
  // INI JUGA JANGAN DI TAMBAHIN, BIKIN PUSING
  //exports: [WebGateway, PlayerGateway],
})
export class RemoteModule {}
