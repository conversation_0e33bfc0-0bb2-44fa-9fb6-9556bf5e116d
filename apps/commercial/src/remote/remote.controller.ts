import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import {
  AuthRemoteCommand,
  GenerateRemoteCommand,
  GenerateVerifyTokenCommand,
  RemoteSongLikeCommand,
  RemoteSongUnLikeCommand,
} from './commands';
import { AuthRemoteDto } from './dto/auth-remote.dto';
import { GenerateQrCodeDto } from './dto/generate-qr-code.dto';
import { GetVerifyQrCodeQuery } from './queries';
import { Throttle } from '@nestjs/throttler';
import { RemoteSongLikeDto } from './dto/remote-like-song.dto';
import { RemoteGuard } from '../auth/guards/remote.guard';
import { AccessFeatureGuard } from '../auth/guards/access-feature.guard';
import { AccessFeature } from '../auth/decorator/access-feature.decorator';
import { FEATURE } from '../auth/enum/feature.enum';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@Controller('remote')
@ApiTags('Remote')
export class RemoteController {
  constructor(
    private commandBus: CommandBus,
    private queryBus: QueryBus,
  ) {}

  @Post('qr-code')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  @ApiBearerAuth()
  @UseGuards(JwtGuard, AccessFeatureGuard, StatusSuspendedGuard)
  @AccessFeature(FEATURE.QrRemote)
  @ApiOperation({ summary: 'generate qr code' })
  generateQrCode(
    @User() user: ICurrentUser,
    @Body() generateQrCodeDto: GenerateQrCodeDto,
  ) {
    return this.commandBus.execute(
      new GenerateRemoteCommand(user, generateQrCodeDto),
    );
  }

  @Post('verify-code')
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  @ApiBearerAuth()
  @UseGuards(JwtGuard, StatusSuspendedGuard, AccessFeatureGuard)
  @AccessFeature(FEATURE.QrRemote)
  @ApiOperation({ summary: 'generate qr verify code' })
  generateVerifyCode(@User() user: ICurrentUser) {
    return this.commandBus.execute(new GenerateVerifyTokenCommand(''));
  }

  @Post('auth')
  @Throttle({ default: { limit: 15, ttl: 60000 } })
  @ApiOperation({ summary: 'login remote' })
  auth(@Body() authRemoteDto: AuthRemoteDto) {
    return this.commandBus.execute(new AuthRemoteCommand(authRemoteDto));
  }

  @Get('verify-qr/:qrCode')
  @ApiOperation({ summary: 'verify remote with code' })
  verifyQr(@Param('qrCode') qrCode: string) {
    return this.queryBus.execute(new GetVerifyQrCodeQuery(qrCode));
  }

  @Post('song-like')
  @ApiBearerAuth()
  @UseGuards(RemoteGuard)
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  create(@User() user: any, @Body() payload: RemoteSongLikeDto) {
    return this.commandBus.execute(new RemoteSongLikeCommand(user, payload));
  }

  @ApiBearerAuth()
  @UseGuards(RemoteGuard)
  @Post('song-unlike')
  @Throttle({ default: { limit: 20, ttl: 60000 } })
  remove(@User() user: any, @Body() payload: RemoteSongLikeDto) {
    return this.commandBus.execute(new RemoteSongUnLikeCommand(user, payload));
  }

  // @Get(':urlToken')
  // qrCodeVerify(@Param('urlToken') urlToken: string) {
  //   return urlToken;
  // }

  // @Get(':verifyCode')
  // verifyCode(@Param('verifyCode') verifyCode: string) {
  //   return verifyCode;
  // }
}
