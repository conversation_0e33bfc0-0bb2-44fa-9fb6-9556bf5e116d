import { Controller } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { EventPattern } from '@nestjs/microservices';
import { RemoteDeviceCommand } from './commands/impl/remote-device.command';
import { WebGateway } from '../events/web.gateway';
import { PlayerGateway } from '../events/player.gateway';
import { LoggedSessionCreate } from '../auth/types';

@Controller()
export class RemoteDeviceEventController {
  constructor(
    private commandBus: CommandBus,
    private webGateway: WebGateway,
    private playerGateway: PlayerGateway,
  ) {}

  @EventPattern('REMOTE-DEVICE')
  async handleRemoteDeviceEvent(data: Record<string, unknown>) {
    return this.commandBus.execute(new RemoteDeviceCommand(data));
  }

  @EventPattern('CLIENT-PLAYER')
  async handleClientPlayerEvent(data: {
    deviceId: string;
    userId: string;
    session: LoggedSessionCreate;
  }) {
    const sessionSocket = await this.webGateway.server
      .in(`session-${data.userId}`)
      .fetchSockets();

    if (sessionSocket.length > 0) {
      this.webGateway.server.to(data.userId).emit('session', {
        ip: data.session.ip.replace('::ffff:', ''),
        userAgent: data.session.media,
      });
    }

    this.playerGateway.server.to(data.deviceId).disconnectSockets();
    this.webGateway.server.to(data.userId).disconnectSockets();
  }
}
