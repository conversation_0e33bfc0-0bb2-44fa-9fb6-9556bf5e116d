import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHand<PERSON> } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetVerifyQrCodeQuery } from '../impl';

@QueryHandler(GetVerifyQrCodeQuery)
export class GetVerifyQrCodeHandler
  implements IQueryHandler<GetVerifyQrCodeQuery>
{
  constructor(private prisma: PrismaService) {}

  async execute(query: GetVerifyQrCodeQuery) {
    const { code } = query;
    const item = await this.prisma.remote.findFirst({ where: { id: code } });
    if (!item) {
      throw new NotFoundException();
    }
    return true;
  }
}
