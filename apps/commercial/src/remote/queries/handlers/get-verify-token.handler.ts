import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetVerifyQuery } from '../impl';

@QueryHandler(GetVerifyQuery)
export class GetVerifyHandler implements IQueryHandler<GetVerifyQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetVerifyQuery) {
    const { token } = query;
    const item = await this.prisma.remote.findFirst({
      where: { AND: [{ pairingCode: token }, { activation: { id: '' } }] },
    });
    if (!item) {
      throw new NotFoundException();
    }
    return true;
  }
}
