import { NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GetRemoteQuery } from '../impl';

@QueryHandler(GetRemoteQuery)
export class GetRemoteHandler implements IQueryHandler<GetRemoteQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetRemoteQuery) {
    const { code } = query;
    const item = await this.prisma.device.findFirst({ where: { id: code } });
    if (!item) {
      throw new NotFoundException();
    }
    return true;
  }
}
