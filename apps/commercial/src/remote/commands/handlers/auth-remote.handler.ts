import { UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CommandHandler, ICommandHandler } from '@nestjs/cqrs';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from 'libs/prisma'; 
import { AuthRemoteCommand } from '../impl';

@CommandHandler(AuthRemoteCommand)
export class AuthRemoteHandler implements ICommandHandler<AuthRemoteCommand> {
  constructor(
    private prisma: PrismaService,
    private jwtService: JwtService,
    private config: ConfigService,
  ) {}

  async execute(command: AuthRemoteCommand) {
    const { args } = command;
    const item = await this.prisma.remote.findFirst({
      where: { AND: [{ pairingCode: args.verifyCode }, { id: args.QrCode }] },
    });
    if (!item) {
      throw new UnauthorizedException();
    }
    const payload = {
      sub: item.id,
    };

    const at = await this.jwtService.signAsync(payload, {
      expiresIn: '1d',
      secret: this.config.get<string>('AT_SECRET'),
    });
    return { accessToken: at };
  }
}
