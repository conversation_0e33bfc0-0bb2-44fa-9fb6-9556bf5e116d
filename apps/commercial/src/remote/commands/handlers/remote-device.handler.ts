import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { RemoteDeviceCommand } from '../impl';
import { RemoteGateway } from 'apps/commercial/src/events/remote.gateway';
import { PrismaService } from 'libs/prisma'; 
import { WebGateway } from 'apps/commercial/src/events/web.gateway';

@CommandHandler(RemoteDeviceCommand)
export class RemoteDeviceCommandHandler
  implements ICommandHandler<RemoteDeviceCommand>
{
  constructor(
    private readonly remoteGateway: RemoteGateway,
    private readonly webGateway: WebGateway,
    private readonly prisma: PrismaService
  ) {}

  async execute(command: RemoteDeviceCommand) {
    const { args } = command;
    switch(args.event) {
      case 'currentState':
        this.remoteGateway.server.to(args.to as string).emit('currentState', args.value);
        const device = await this.prisma.activationDevice.findFirst({
          where: {
            AND: [
              { activationId: args.to },
              { isActive: true }
            ]
          },
          include: {
            activation: {
              include: {
                property: {
                  include: {
                    users: true
                  }
                }
              }
            }
          }
        });
        if (device) {
          this.webGateway.server.in(device.activation.property.users.map((usr) => usr.userId)).emit('deviceState', {
            deviceId: args.to,
            data: args.value
          })
        }
        break;
      case 'command':
        this.remoteGateway.server.to(args.to as string).emit('command', args.value);
        break;
      // case 'currentPlay':
      //   this.remoteGateway.server.to(args.to as string).emit('currentPlay', args.value);
      default:
        break;
    }
    return
  }
}
