import { randomNumber } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GenerateRemoteCommand } from '../impl';

@CommandHandler(GenerateRemoteCommand)
export class GenerateRemoteHandler
  implements ICommandHandler<GenerateRemoteCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: GenerateRemoteCommand) {
    const { args } = command;

    try {
      const item = await this.prisma.remote.create({
        data: {
          activation: { connect: { id: args.deviceId } },
          pairingCode: await this.genToken(),
        },
      });
      return { qrCode: item.id, verifyCode: item.pairingCode };
    } catch (error) {
      return 'failed generate remote Qr';
    }
  }

  private async genToken(): Promise<string> {
    const token = randomNumber(5);
    const check = await this.prisma.remote.count({
      where: { pairingCode: token },
    });
    if (check > 0) {
      return this.genToken();
    }
    return token;
  }
}
