import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { UpdateSocketRemoteCommand } from '../impl';

@CommandHandler(UpdateSocketRemoteCommand)
export class UpdateSocketRemoteHandler
  implements ICommandHandler<UpdateSocketRemoteCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: UpdateSocketRemoteCommand) {
    const { code, args } = command;
    const item = await this.prisma.remote.findFirst({
      where: { pairingCode: code },
    });

    if (item) {
      if (args.type == 'remote') {
        await this.prisma.remote.update({
          where: { id: item.id },
          data: { remoteOnline: args.online, remoteSocket: args.socketId },
        });
      } else {
        await this.prisma.remote.update({
          where: { id: item.id },
          data: {},
        });
      }
    }
  }
}
