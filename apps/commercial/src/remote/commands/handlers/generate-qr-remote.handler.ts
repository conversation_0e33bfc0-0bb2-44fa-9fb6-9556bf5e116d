import { NotFoundException } from '@nestjs/common';
import { <PERSON>Handler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GenerateQrRemoteCommand } from '../impl';

@CommandHandler(GenerateQrRemoteCommand)
export class GenerateQrRemoteHandler
  implements ICommandHandler<GenerateQrRemoteCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: GenerateQrRemoteCommand) {
    const { id } = command;
    const item = await this.prisma.device.findFirst({ where: { id: id } });
    if (!item) {
      throw new NotFoundException();
    }
    return item.id;
  }
}
