import { AuthRemoteHandler } from './auth-remote.handler';
import { GenerateRemoteHandler } from './generate-remote.handler';
import { RemoteDeviceCommandHandler } from './remote-device.handler';
import { RemoteSongLikeHandler } from './remote-song-like.handler';
import { RemoteSongUnLikeHandler } from './remote-song-un-like.handler';
import { UpdateSocketRemoteHandler } from './update-socket-remote.handler';

export const RemoteCommandHandlers = [
  GenerateRemoteHandler,
  UpdateSocketRemoteHandler,
  AuthRemoteHandler,
  RemoteDeviceCommandHandler,
  RemoteSongLikeHandler,
  RemoteSongUnLikeHandler
];
