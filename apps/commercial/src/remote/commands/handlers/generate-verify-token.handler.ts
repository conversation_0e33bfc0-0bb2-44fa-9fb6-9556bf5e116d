import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GenerateVerifyTokenCommand } from '../impl';

@CommandHandler(GenerateVerifyTokenCommand)
export class GenerateVerifyTokenHandler
  implements ICommandHandler<GenerateVerifyTokenCommand>
{
  constructor(private readonly prisma: PrismaService) {}

  async execute(command: GenerateVerifyTokenCommand) {
    const {} = command;
    try {
      const item = await this.prisma.remote.findFirst({});
      return item.pairingCode;
    } catch (error) {
      return 'failed created verify code for remote';
    }
  }
}
