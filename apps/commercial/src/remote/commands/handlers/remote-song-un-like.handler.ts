import { BadRequestException, NotFoundException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>ler, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma';
import { RemoteSongUnLikeCommand } from '../impl';

@CommandHandler(RemoteSongUnLikeCommand)
export class RemoteSongUnLikeHandler
  implements ICommandHandler<RemoteSongUnLikeCommand>
{
  constructor(private prisma: PrismaService) {}

  async execute(command: RemoteSongUnLikeCommand) {
    const { args, user } = command;

    const deviceProperty = await this.prisma.activationDevice.findFirst({
      where: {
        isActive: true,
        device: {
          id: user?.device
        }
      },
      include: {
        activation: {
          include: {
            property: true
          }
        }
      }
    });

    if (!deviceProperty) {
      throw new BadRequestException('device not found');
    }

    const track = await this.prisma.track.findFirst({
      where: { key: args.id },
    });

    const item = await this.prisma.trackLike.findFirst({
      where: {
        AND: [
          { property: { id: deviceProperty.activation.property.id } },
          { track: { id: track.id} },
        ],
      },
    });
    if (!item) {
      throw new NotFoundException();
    }

    try {
      await this.prisma.trackLike.delete({
        where: {
          trackId_propertyId_activationId: {
            propertyId: deviceProperty.activation.property.id,
            trackId: item.trackId,
            activationId: item.activationId
          },
        },
      });
      return 'successfully unlike song';
    } catch (error) {
      return 'failed unlike song';
    }
  }
}
