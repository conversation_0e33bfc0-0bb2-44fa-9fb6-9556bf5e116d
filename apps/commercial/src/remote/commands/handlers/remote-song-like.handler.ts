import { <PERSON><PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';
import { PrismaService } from 'libs/prisma'; 
import { GmiRESTService } from 'apps/commercial/src/gmi/gmi.service';
import { BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { RemoteSongLikeCommand } from '../impl';

@CommandHandler(RemoteSongLikeCommand)
export class RemoteSongLikeHandler
  implements ICommandHandler<RemoteSongLikeCommand>
{
  constructor(
    private prisma: PrismaService,
    private gmiRestService: GmiRESTService
  ) {}

  async execute(command: RemoteSongLikeCommand) {
    const { args, user } = command;

    const deviceProperty = await this.prisma.activationDevice.findFirst({
      where: {
        isActive: true,
        device: {
          id: user?.device
        }
      },
      include: {
        activation: {
          include: {
            property: true
          }
        }
      }
    });

    if (!deviceProperty) {
      throw new BadRequestException('device not found');
    }

    let track = await this.prisma.track.findFirst({
      where: { key: args.id },
    });
    
    if (!track) {
      const gmiTrack = await this.gmiRestService.melodivaUse(args.id);
      await this.prisma.track.create({
        data: {
          key: args.id,
          source: gmiTrack,
        },
      });
    } else if (track.source && Object.keys(track.source).length === 0) {
      const gmiTrack = await this.gmiRestService.melodivaUse(args.id);
      track = await this.prisma.track.update({
        where: { key: args.id },
        data: { source: gmiTrack },
      });
    }
    
    let trackId = track?.key;    
    try {
      const existingLike = await this.prisma.trackLike.findFirst({
        where: {
          propertyId: deviceProperty.activation.property.id,
          trackId: trackId,
        },
      });

      if (!existingLike) {
        await this.prisma.trackLike.create({
          data: {
            property: { connect: { id: deviceProperty.activation.property.id } },
            track: { connect: { key: trackId } },
            activation: { connect: { id: deviceProperty?.activationId }}
          },
        });
        return 'successfully liked song';
      }

    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        throw new BadRequestException('Song already liked');
      } else {
        console.error(error);
        throw new InternalServerErrorException('Failed to like song');
      }
    }
  }
}