import { Module } from '@nestjs/common';
import { CqrsModule } from '@nestjs/cqrs';
import { GmiModule } from '../gmi/gmi.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SearchController } from './search.controller';
import { SearchQueryHandlers } from './queries';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { CacheModule } from '@nestjs/cache-manager';
import Keyv from 'keyv';
import { CacheableMemory } from 'cacheable';
import { createKeyv } from '@keyv/redis';

@Module({
  imports: [
    CqrsModule,
    ElasticsearchModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => ({
        node: config.get<string>('ELASTIC_NODE'),
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => {
        return {
          stores: [
            new Keyv({
              store: new CacheableMemory({ ttl: 60000 }),
            }),
            createKeyv({
              url: config.get<string>('REDIS')
            }),
          ],
        };
      },
    }),
    GmiModule,
  ],
  controllers: [SearchController],
  providers: [
    ...SearchQueryHandlers,
  ],
})
export class SearchModule {}
