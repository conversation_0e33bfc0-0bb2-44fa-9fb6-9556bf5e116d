import { ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsOptional, IsString, ValidateIf } from 'class-validator';

export class SearchDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  keyword: string;
  @ApiPropertyOptional({ type: [String] }) // Indicate it can be an array in Swagger
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return [value];
    }
    return value;
  })
  key?: string[]; 

  @IsOptional()
  @ValidateIf((prop) => prop.page != '')
  @IsString()
  @ApiPropertyOptional()
  page?: number;

  @IsOptional()
  @ValidateIf((prop) => prop.limit != '')
  @IsString()
  @ApiPropertyOptional()
  limit?: number;
  
  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsString()
  // limit: number;

  // @ApiPropertyOptional()
  // @IsOptional()
  // @IsString()
  // cursor: string;
}
