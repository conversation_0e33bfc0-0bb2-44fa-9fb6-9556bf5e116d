import { <PERSON><PERSON>ublish<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { SearchQuery } from '../impl';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { InternalServerErrorException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SongModel } from 'apps/commercial/src/song/model/song.model';
import { PrismaService } from 'libs/prisma';

@QueryHandler(SearchQuery)
export class SearchHandler implements IQueryHandler<SearchQuery> {
  constructor(
    private readonly elasticService: ElasticsearchService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly publisher: EventPublisher,
  ) {}

  async execute(query: SearchQuery) {
    const { args, user } = query;
    const limit = args.limit ? Number(args.limit) : 20;
    const page = args.page || args.page > 0 ? args.page - 1 : 0;
    const keyword = args.keyword ? args.keyword : '';
    const resp = {};
    const compare = {
      type: null,
      score: 0,
      data: null,
    };

    try {
      if (args.keyword) {
        // console.log('CEK 1', args);
        for (let i = 0; i <= args.key.length - 1; i++) {
          // --- track ---
          if (args.key[i] === 'tracks') {
            const tracks = await this.elasticService.search({
              index: 'gmi.tracks',
              search_type: 'dfs_query_then_fetch',
              preference: 'primary',
              body: {
                query: {
                  bool: {
                    must: [{ term: { isPublish: true } }],
                    should: [
                      {
                        match: {
                          terms: keyword.trim(),
                        },
                      },
                      {
                        match: {
                          terms: {
                            query: keyword.trim(),
                            fuzziness: 'AUTO',
                          },
                        },
                      },
                    ],
                  },
                },
                sort: [{ _score: { order: 'desc' } }],
                from: page,
                size: limit,
              } as any,
            });
            const lastPage = Math.ceil(
              Number(tracks.hits.total['value']) / limit,
            );

            const mapTracks = tracks.hits.hits.map((trck) => {
              trck._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              trck._source['uri'] = `search:${trck._source['id']}:${keyword}`;

              if (tracks.hits.hits.indexOf(trck) == 0) {
                if (trck._score > compare.score) {
                  compare.type = 'song';
                  compare.score = trck._score;
                  compare.data = trck._source;
                }
              }
              return trck._source;
            });

            Object.assign(resp, {
              tracks: {
                meta: {
                  total: Number(tracks.hits.total['value']),
                  lastPage,
                  currentPage: page + 1,
                  limit,
                },
                data: mapTracks,
              },
            });
          }

          // --- artist ---
          if (args.key[i] === 'artists') {
            // const artists = await this.elasticService.search({
            //   index: 'gmi.artists',
            //   body: {
            //     query: {
            //       bool: {
            //         should: [
            //           {
            //             match: {
            //               name: keyword.trim(),
            //             },
            //           },
            //           {
            //             match: {
            //               name: {
            //                 query: keyword.trim(),
            //                 fuzziness: 'AUTO',
            //               },
            //             },
            //           },
            //         ],
            //       },
            //     },
            //     from: page,
            //     sort: [
            //       { verified: { order: 'desc' } },
            //       { _score: { order: 'desc' } }
            //       // { popularity: { order: "asc" } }
            //       //{
            //       //  id: 'asc',
            //       //},
            //     ],
            //     size: limit,
            //   },
            // });

            const artists = await this.elasticService.search({
              index: 'gmi.artists',
              body: {
                query: {
                  function_score: {
                    query: {
                      bool: {
                        must: [{ term: { isPublish: true } }],
                        should: [
                          {
                            match: {
                              name: keyword.trim(),
                            },
                          },
                          {
                            match: {
                              name: {
                                query: keyword.trim(),
                                fuzziness: 'AUTO',
                              },
                            },
                          },
                        ],
                      },
                    },
                    functions: [
                      {
                        filter: {
                          term: {
                            verified: true,
                          },
                        },
                        weight: 10,
                      },
                    ],
                    boost_mode: 'sum',
                    score_mode: 'sum',
                  },
                },
                sort: [{ _score: { order: 'desc' } }],
                from: page,
                size: limit,
              } as any,
            });
            const lastPage = Math.ceil(
              Number(artists.hits.total['value']) / limit,
            );

            const mapArtists = artists.hits.hits.map((art) => {
              art._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              art._source['uri'] = `artist:${art._source['id']}`;

              if (artists.hits.hits.indexOf(art) == 0) {
                if (art._score > compare.score) {
                  compare.type = 'artist';
                  compare.score = art._score;
                  compare.data = art._source;
                }
              }
              return art._source;
            });
            Object.assign(resp, {
              artists: {
                meta: {
                  total: Number(artists.hits.total['value']),
                  lastPage,
                  currentPage: page + 1,
                  limit,
                },
                data: mapArtists,
              },
            });
          }

          // -- album ---
          if (args.key[i] === 'albums') {
            const albums = await this.elasticService.search({
              index: 'gmi.albums',
              body: {
                query: {
                  bool: {
                    must: [{ term: { isPublish: true } }],
                    should: [
                      {
                        match: {
                          terms: keyword.trim(),
                        },
                      },
                      {
                        match: {
                          terms: {
                            query: keyword.trim(),
                            fuzziness: 'AUTO',
                          },
                        },
                      },
                    ],
                  },
                },
                from: page,
                sort: [
                  { _score: { order: 'desc' } },
                  // { id: 'asc' },
                ],
                size: limit,
              } as any,
            });
            const lastPage = Math.ceil(
              Number(albums.hits.total['value']) / limit,
            );

            const mapAlbums = albums.hits.hits.map((albm) => {
              albm._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              albm._source['uri'] = `album:${albm._source['id']}`;

              if (albums.hits.hits.indexOf(albm) == 0) {
                if (albm._score > compare.score) {
                  compare.type = 'album';
                  compare.score = albm._score;
                  compare.data = albm._source;
                }
              }
              return albm._source;
            });
            Object.assign(resp, {
              albums: {
                meta: {
                  meta: {
                    total: Number(albums.hits.total['value']),
                    lastPage,
                    currentPage: page + 1,
                    limit,
                  },
                },
                data: mapAlbums,
              },
            });
          }

          // -- playlists ---
          if (args.key[i] === 'playlists') {
            const skip = page > 0 ? limit * (page - 1) : 0;
            const playlists = await this.prisma.playlist.findMany({
              where: {
                AND: [
                  {
                    activation: { id: user.deviceId },
                  },
                  {
                    property: { id: user.propertyId },
                  },
                  { name: { contains: keyword.trim(), mode: 'insensitive' } },
                ],
              },
              include: {
                thumbnail: true,
              },
              skip,
              take: limit,
            });

            const playlistsRemap = await Promise.all(
              playlists.map(async (playlist) => {
                const thumbnail = playlist.thumbnail
                  ? [
                      {
                        url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                        width: 64,
                        height: 64,
                      },
                      {
                        url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                        width: 300,
                        height: 300,
                      },
                      {
                        url: `https://${this.configService.get<string>('MINIO_ENDPOINT')}/${playlist.thumbnail.path}/${playlist.thumbnail.fileName}`,
                        width: 640,
                        height: 640,
                      },
                    ]
                  : [
                      {
                        url: this.configService.get<string>(
                          'DEFAULT_IMAGE_64_URL',
                        ),
                        width: 64,
                        height: 64,
                      },
                      {
                        url: this.configService.get<string>(
                          'DEFAULT_IMAGE_300_URL',
                        ),
                        width: 300,
                        height: 300,
                      },
                      {
                        url: this.configService.get<string>(
                          'DEFAULT_IMAGE_640_URL',
                        ),
                        width: 640,
                        height: 640,
                      },
                    ];

                return {
                  ...playlist,
                  uri: `playlist:${playlist.id}`,
                  thumbnail,
                };
              }),
            );

            const totalCount = await this.prisma.playlist.count({
              where: {
                AND: [
                  { property: { id: user.propertyId } },
                  { name: { contains: keyword.trim(), mode: 'insensitive' } },
                ],
              },
            });

            const lastPage = Math.ceil(totalCount / limit);

            Object.assign(resp, {
              playlists: {
                meta: {
                  meta: {
                    total: totalCount,
                    lastPage,
                    currentPage: page,
                    limit,
                  },
                },
                data: playlistsRemap,
              },
            });
          }
        }
        // compare score track, album, artist

        //if (resp['tracks'] && resp['tracks']['data'].length > 0) {
        //  const songModel = this.publisher.mergeClassContext(SongModel);
        //  const songEvent = new songModel();
        //  songEvent.UpdateSearchHistory(
        //    user,
        //    args?.key,
        //    keyword,
        //    resp['tracks']['data'],
        //  );
        //}
        delete compare.score;
        Object.assign(resp, { top: compare });
        return resp;
      } else {
        const defaultLimit = args.limit ? Number(args.limit) : 5;

        for (let i = 0; i <= args.key.length - 1; i++) {
          if (args.key[i] === 'tracks') {
            // --- track ---
            const tracks = await this.elasticService.search({
              index: 'gmi.tracks',
              body: {
                sort: [{ id: 'desc' }],
                from: page,
                size: defaultLimit, //limit,
              } as any,
            });
            const lastPageTrack = Math.ceil(
              Number(tracks.hits.total['value']) / defaultLimit,
            );
            const mapTracks = tracks.hits.hits.map((trck) => {
              trck._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              trck._source['uri'] = `search:${trck._source['id']}`;
              return trck._source;
            });

            Object.assign(resp, {
              tracks: {
                meta: {
                  total: Number(tracks.hits.total['value']),
                  lastPage: lastPageTrack,
                  currentPage: page + 1,
                  limit: defaultLimit,
                },
                data: mapTracks,
              },
            });
          }

          if (args.key[i] === 'albums') {
            // -- album ---
            const albums = await this.elasticService.search({
              index: 'gmi.albums',
              body: {
                sort: [
                  {
                    id: 'asc',
                  },
                ],
                from: page,
                size: defaultLimit, //limit,
              } as any,
            });
            const lastPageAlbum = Math.ceil(
              Number(albums.hits.total['value']) / defaultLimit,
            );
            const mapAlbums = albums.hits.hits.map((albm) => {
              albm._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              albm._source['uri'] = `album:${albm._source['id']}`;
              return albm._source;
            });

            Object.assign(resp, {
              albums: {
                meta: {
                  total: Number(albums.hits.total['value']),
                  lastPage: lastPageAlbum,
                  currentPage: page + 1,
                  limit: defaultLimit,
                },
                data: mapAlbums,
              },
            });
          }

          if (args.key[i] === 'artists') {
            // artist diambil dari top result song
            // --- artist ---
            const artists = await this.elasticService.search({
              index: 'gmi.artists',
              body: {
                sort: [
                  {
                    id: 'asc',
                  },
                ],
                from: page,
                size: defaultLimit, //limit,
              } as any,
            });
            const lastPageArtist = Math.ceil(
              Number(artists.hits.total['value']) / defaultLimit,
            );
            const mapArtists = artists.hits.hits.map((art) => {
              art._source['images'].map((image) => {
                image.url = image.url
                  ? `${this.configService.get<string>('CDN_URL').replace(/\/$/, '')}${image.url}`
                  : this.configService.get<string>('DEFAULT_IMAGE_64_URL');
              });
              art._source['uri'] = `artist:${art._source['id']}`;
              return art._source;
            });

            Object.assign(resp, {
              artists: {
                meta: {
                  total: Number(artists.hits.total['value']),
                  lastPage: lastPageArtist,
                  currentPage: page + 1,
                  limit: defaultLimit,
                },
                data: mapArtists,
              },
            });
          }
        }
        // ==== result ====
        return resp;
      }
    } catch (err) {
      console.log(err);
      throw new InternalServerErrorException('Internal Server Error');
    }
  }
}
