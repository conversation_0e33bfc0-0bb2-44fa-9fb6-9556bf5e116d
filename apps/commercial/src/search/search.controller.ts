import {
  <PERSON>,
  Get,
  Ip,
  Param,
  Query,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { QueryBus } from '@nestjs/cqrs';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { User } from '../auth/decorator/user.decorator';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { SearchDto } from './dto/search.dto';
import { SearchQuery } from './queries';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { StatusSuspendedGuard } from '../auth/guards/status-suspend.guard';

@ApiTags('Search')
@Controller('search')
@ApiBearerAuth()
@UseGuards(JwtGuard, StatusSuspendedGuard)
export class SearchController {
  constructor(private queryBus: QueryBus) {}

  @Get()
  // @UseInterceptors(CacheInterceptor)
  search(@Query() args: SearchDto, @User() user: ICurrentUser) {
    return this.queryBus.execute(new SearchQuery(args, user));
  }
}
