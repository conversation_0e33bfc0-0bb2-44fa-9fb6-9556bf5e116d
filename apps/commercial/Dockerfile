FROM node:20-slim AS development
RUN apt-get update && apt-get install -y ca-certificates wget openssl
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
# RUN npm config set registry https://registry.npmmirror.com/
# Install specific version of pnpm
RUN npm install -g pnpm@9.7.0
WORKDIR /app
COPY --chown=node:node . .

#FROM development AS build
#RUN pnpm install -g node-prune
## RUN wget -O - https://gobinaries.com/tj/node-prune | sh
#RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
#RUN pnpm prisma generate
#RUN pnpm run build commercial
#RUN /usr/local/bin/node-prune /app/node_modules
#RUN node-prune /app/node_modules

FROM development AS build
RUN wget https://github.com/tj/node-prune/releases/download/v1.0.1/node-prune_1.0.1_linux_amd64.tar.gz \
    && tar xvf node-prune_1.0.1_linux_amd64.tar.gz \
    && mv node-prune /usr/local/bin/ \
    && rm node-prune_1.0.1_linux_amd64.tar.gz
RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
RUN pnpm prisma generate
RUN pnpm run build commercial
RUN node-prune /app/node_modules

FROM node:20-slim AS production
RUN apt-get update && apt-get install -y ca-certificates wget openssl
WORKDIR /app
RUN mkdir certs
COPY --chown=node:node --from=build /app/node_modules ./node_modules
COPY --chown=node:node --from=build /app/dist ./dist
COPY --chown=node:node --from=build /app/_proto ./_proto

EXPOSE 8001
EXPOSE 50051

CMD [ "node","dist/apps/commercial/main.js" ]