apiVersion: apps/v1
kind: Deployment
metadata:
  name: velodiva-backend-commercial
  namespace: production
spec:
  replicas: 3
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
  selector:
    matchLabels:
      app: velodiva-backend-commercial
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
  template:
    metadata:      
      labels:
        app: velodiva-backend-commercial
    spec:
      containers:
        - name: velodiva-backend-commercial
          image: gitlab.vnt.co.id:5050/velodiva-1/backend-5-service-velodiva/commercial-prod:latest
          imagePullPolicy: Always
          env:
            - name: DATABASE_URL
              valueFrom:
                secretKeyRef:
                  name: be-commercial-sc
                  key: DATABASE_URL
          envFrom:
            - configMapRef:
                name: velodiva-backend-commercial-configmap
          ports:
            - containerPort: 8001
              protocol: TCP
            - containerPort: 50051
              protocol: TCP
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
            limits:
              cpu: "1"
              memory: 1Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      restartPolicy: Always
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: pull-image-secret
      nodeSelector:
        node-role.kubernetes.io/worker: worker
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
