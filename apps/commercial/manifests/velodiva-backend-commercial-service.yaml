apiVersion: v1
kind: Service
metadata:
  name: velodiva-backend-commercial-service
  namespace: production
spec:
  type: ClusterIP  
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  internalTrafficPolicy: Cluster
  selector:
    app: velodiva-backend-commercial
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 8001
    - name: grpc
      port: 50051
      protocol: TCP
      targetPort: 50051
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
