apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: velodiva-backend-commercial-ingress
  namespace: production
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/affinity: cookie
    nginx.ingress.kubernetes.io/session-cookie-name: route
    nginx.ingress.kubernetes.io/session-cookie-hash: sha1
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/cors-allow-methods: GET, PUT, POST, DELETE, PATCH, OPTIONS
    nginx.ingress.kubernetes.io/cors-allow-headers: DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,deptstail,X-Custom-Header,Authorization,Accept,Origin
    nginx.ingress.kubernetes.io/cors-expose-headers: Content-Length,Content-Range
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
    nginx.ingress.kubernetes.io/proxy-buffering: "off"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/use-forwarded-headers: "true"
    nginx.ingress.kubernetes.io/proxy-set-header: "Upgrade $http_upgrade"
    nginx.ingress.kubernetes.io/proxy-set-header: "Connection upgrade"
spec:
  rules:
  - host: api-player.velodiva.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: velodiva-backend-commercial-service
            port:
              number: 80
