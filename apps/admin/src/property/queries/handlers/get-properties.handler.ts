import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Query<PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { PrismaService } from 'libs/prisma';
import { PropertyWithInclude } from '../../types';
import { GetPropertiesQuery } from '../impl';
import { UserService } from '../../../membership-client/user.service';
import { firstValueFrom } from 'rxjs';

@QueryHandler(GetPropertiesQuery)
export class GetPropertiesHandler implements IQueryHandler<GetPropertiesQuery> {
  constructor(
    private prisma: PrismaService,
    private readonly userService: UserService,
  ) {}

  async execute(query: GetPropertiesQuery) {
    const { args } = query;
    const search = args?.search || '';
    const status = args?.status;

    // Build where clause with search and status filters
    const whereClause: Prisma.PropertyWhereInput = {
      AND: [
        // Search filter
        {
          OR: [
            { companyName: { contains: search, mode: 'insensitive' } },
            { brandName: { contains: search, mode: 'insensitive' } },
          ],
        },
        status ? { status: status as any } : {},
        { packages: { some: { isActive: true } } },
      ],
    };

    const items = await Pagination<
      PropertyWithInclude,
      Prisma.PropertyFindManyArgs
    >(
      this.prisma.property,
      {
        where: whereClause,
        include: {
          postal: { include: { provinces: true } },
          users: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  phoneNumber: true,
                  crmUserId: true,
                },
              },
            },
          },
          propertyType: { select: { id: true, name: true } },
          packages: true,
        },
      },
      { page: args.page, limit: args.limit },
    );

    for (const property of items.data) {
      for (const u of property.users) {
        if (u.user?.id) {
          const userProperty = await firstValueFrom(
            this.userService.getUserById(u.user.id),
          );
          console.log(userProperty);
        }
      }
    }

    return items;
  }
}
