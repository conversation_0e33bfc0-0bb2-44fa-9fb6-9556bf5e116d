# OpenCode.md – Backend Velodiva Agent Coding Guide

## Build / Lint / Test
- Install deps: `pnpm install`
- Build all: `pnpm run build`
- Lint: `pnpm run lint`
- Format: `pnpm run format`
- Test all: `pnpm run test`
- Watch tests: `pnpm run test:watch`
- Test coverage: `pnpm run test:cov`
- E2E tests: `pnpm run test:e2e`
- Run single test: `pnpm run test -- <pattern>`

## Code Style Guidelines
- Language: TypeScript, NestJS (decorator pattern, DI)
- Imports: Use absolute for packages, relative for project files, one import per line
- Types: Prefer interfaces for DTOs/types, extend/implement as needed
- Naming: PascalCase for classes/types, camelCase for vars, CONSTANT_CASE for consts
- Formatting: Use Prettier defaults (see `format` script)
- Error handling: Always catch errors, throw NestJS HTTP exceptions for user errors, use `Logger` for logging
- File structure: One class/control/handler per file, grouped by feature
- Use DTOs for request/response validation, use PartialType for updates
- Swagger decorators (`@ApiProperty`) for API models
- Prefer immutability, avoid magic numbers/strings
- Always type public APIs
