enum DeviceType {
  web
  mobile
}

//Schema for device & remote feature
model Device {
  id           String             @id @default(cuid())
  serialNumber String?            @db.String(120)
  guid         String?            @db.String(50)
  macAddr      String?            @db.String(50)
  name         String?            @db.String(120)
  type         String?            @db.String(50)
  platform     String?            @db.String(50)
  version      String?            @db.String(16)
  isActive     Boolean            @default(true)
  updatedAt    DateTime           @default(now())
  createdAt    DateTime           @updatedAt()
  activation   ActivationDevice[]

  // currentPlayer CurrentPlayer[]
  AuthToken AuthToken[]

  @@unique([guid])
  @@index([serialNumber])
}

model ActivationDevice {
  id           String     @id @default(cuid())
  deviceId     String?
  device       Device?    @relation(fields: [deviceId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  activationId String
  activation   Activation @relation(fields: [activationId], references: [id])
  activatedAt  DateTime?  @db.Timestamptz(3)
  qty          Int?       @default(0)
  isActive     Boolean    @default(true)
  updatedAt    DateTime   @default(now())
  createdAt    DateTime   @updatedAt()
}

model AuthToken {
  id            String   @id @default(cuid())
  deviceId      String
  Device        Device   @relation(fields: [deviceId], references: [id])
  access_token  String?  @db.String
  refresh_token String?  @db.String
  updatedAt     DateTime @default(now())
  createdAt     DateTime @updatedAt()

  @@unique([deviceId])
}

model Connection {
  id               String     @id @default(cuid())
  activationId     String
  activation       Activation @relation(fields: [activationId], references: [id])
  socketId         String?    @db.String(120)
  isOnline         Boolean?   @default(false)
  publicIP         String?    @db.String(64)
  privateIP        String?    @db.String(30)
  geo              String?    @db.String(60)
  lastConnected    DateTime?  @db.Timestamptz(3)
  lastDisconnected DateTime?  @db.Timestamptz(3)
  connectedCount   BigInt?
  isActive         Boolean    @default(true)
  updatedAt        DateTime   @default(now())
  createdAt        DateTime   @updatedAt()

  @@unique([activationId])
}

model Config {
  id                 String     @id @default(cuid())
  activationId       String
  activation         Activation @relation(fields: [activationId], references: [id])
  allowSettingAccess Boolean?   @default(false)
  player             Json?      @db.JsonB
  license            Json?      @db.JsonB
  updatedAt          DateTime   @default(now())
  createdAt          DateTime   @updatedAt()

  @@unique([activationId])
}

model TemporaryDevice {
  id             String   @id @default(cuid())
  guid           String?  @db.String(50)
  serialNumber   String?  @db.String(120)
  publicIP       String?  @db.String(30)
  privateIP      String?  @db.String(30)
  geo            String?  @db.String(60)
  macAddr        String?  @db.String(50)
  isActive       Boolean  @default(true)
  connectedCount BigInt?
  updatedAt      DateTime @default(now())
  createdAt      DateTime @updatedAt()

  @@unique([guid])
}

model Remote {
  id           String      @id @default(uuid())
  remoteSocket String?     @db.String(120)
  remoteOnline Boolean     @default(false)
  pairingCode  String      @db.String(5)
  activation   Activation? @relation(fields: [activationId], references: [id])
  activationId String?
  createdAt    DateTime    @default(now())

  @@index([remoteSocket, remoteOnline, pairingCode])
}

model Activation {
  id                String              @id @default(cuid())
  code              String              @unique
  isUsed            Boolean?            @default(false)
  zone              String?             @db.String(120)
  blacklisted       Boolean?            @default(false)
  sequence          Int?
  property          Property?           @relation(fields: [propertyId], references: [id])
  propertyId        String?
  licenseKey        String?
  qty               Int?                @default(0)
  activationDevice  ActivationDevice[]
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt()
  scheduleZone      ScheduleZone[]
  currentPlayer     CurrentPlayer[]
  playHistory       PlayHistory[]
  config            Config?
  trackLike         TrackLike[]
  queue             Queue[]
  remote            Remote[]
  connection        Connection?
  playlist          Playlist[]
  zoneFeature       ZoneFeature[]
  schedule          Schedule[]
  playlistFollower  PlaylistFollower[]
  playlistAccess    PlaylistAccess[]
  runningTextDevice RunningTextDevice[]
  user              User?
  zoneType          ZoneType?           @relation(fields: [zoneTypeId], references: [id])
  zoneTypeId        String?
  isCustom          Boolean?            @default(true)
  isLock            Boolean?            @default(false)
  timezone          String?
  isActive          Boolean?
}

model ZoneFeature {
  id           String      @id @default(cuid())
  activation   Activation? @relation(fields: [activationId], references: [id])
  activationId String?
  feature      Feature?    @relation(fields: [featureId], references: [id])
  featureId    String?
  package      Package?    @relation(fields: [packageId], references: [id])
  packageId    String?
  qouta        Int?        @default(0)

  @@index([activationId, featureId])
}

model Repository {
  id          String   @id @default(cuid())
  name        String?  @db.String
  fileName    String?  @db.String(150)
  path        String?  @db.String
  versionName String?  @db.String(15)
  versionCode Int?     @default(1)
  packageName String?  @db.String(40)
  description String?  @db.String(200)
  size        Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt()
}

model RunningText {
  id                String              @id @default(cuid())
  name              String?             @db.String(150)
  content           String?             @db.String()
  startTime         String?             @db.String(30)
  endTime           String?             @db.String(30)
  broadcast         Boolean?            @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt()
  property          Property?           @relation(fields: [propertyId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  propertyId        String?
  runningTextDevice RunningTextDevice[]

  @@index([startTime, endTime])
}

model RunningTextDevice {
  id            String      @id @default(cuid())
  runningTextId String
  activationId  String?
  runningText   RunningText @relation(fields: [runningTextId], references: [id], onDelete: Cascade)
  activation    Activation? @relation(fields: [activationId], references: [id], onDelete: Cascade)
}

model Banner {
  id          String  @id @default(cuid())
  order       Int
  title       String? @db.String
  subtitle    String? @db.String
  actionTitle String? @db.String
  actionUrl   String? @db.String
  published   Boolean @default(true)
  media       Media?  @relation(fields: [mediaId], references: [id])
  mediaId     String? @unique
}

model ZoneType {
  id               String             @id @default(cuid())
  name             String?            @db.String
  activation       Activation[]
  ZoneTypeProperty ZoneTypeProperty[]
}

model ZoneTypeProperty {
  id             String        @id @default(cuid())
  zoneType       ZoneType?     @relation(fields: [zoneTypeId], references: [id])
  zoneTypeId     String?
  propertytype   PropertyType? @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId String?
}

model Greeting {
  id              String            @id @default(cuid())
  name            String?           @db.String(150)
  description     String?           @db.String()
  active          Boolean?          @default(false)
  type            String?           @db.String()
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt()
  greetingContent GreetingContent[]
}

model GreetingContent {
  id         String   @id @default(cuid())
  order      Int?     @default(0)
  duration   Int?     @default(0)
  shown      Boolean? @default(false)
  greetingId String
  greeting   Greeting @relation(fields: [greetingId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  mediaId    String
  media      Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt()
}

// model UserDevice {
//   id       String @id @default(cuid())
//   userId   String @unique   
//   deviceId String @unique   
//   device   Device? @relation(fields: [deviceId], references: [id], onDelete: Cascade)

//   user     User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
// }
