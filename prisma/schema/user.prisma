enum UserType {
  internal
  commercial
  creator
  reseller
  lmkn
  lmk
  other
}

enum GenderType {
  male
  female
  unknown
}

enum CreatorType {
  SONGWRITER
  SINGER
  LABEL
  PARTNER
}

enum RoleType {
  internal
  lmkn
  lmk
}

model User {
  id                String          @id @default(cuid())
  email             String?         @db.String(50)
  phoneNumber       String?         @db.String(50)
  username          String?         @db.String(50)
  password          String?         @db.String(200)
  shownPassword     Boolean?        @default(false)
  activationCode    String?         @db.String(50)
  resetPasswordCode String?         @db.String(50)
  type              UserType?
  status            String?         @default("inActive") @db.String(30)
  provider          String          @default("local") @db.String(30)
  activationAt      DateTime?
  crmUserId         String?
  isActive          Boolean         @default(false)
  createdAt         DateTime?       @default(now())
  updatedAt         DateTime?       @updatedAt()
  profile           Profile?
  role              Role?           @relation(fields: [roleId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  roleId            String?
  reseller          Reseller?
  businessType      String?
  session           UserSession[]
  to                Notification[]  @relation("to")
  from              Notification[]  @relation("from")
  hashRt            String?         @db.String(150)
  lmkId             String?
  property          Property?
  properties        UserProperty[]
  parent            User?           @relation("UserParent", fields: [parentId], references: [id])
  parentId          String?
  children          User[]          @relation("UserParent")
  // userDevice        UserDevice?
  isAdmin           Boolean         @default(false)
  activationId      String?         @unique
  activation        Activation?     @relation(fields: [activationId], references: [id])
  zone              String?
  playlist          Playlist[]
  backgroundImage   BackgroudImage?

  @@unique([email, username])
}

model BackgroudImage {
  id      String  @id @default(cuid())
  fileUri String?
  user    User?   @relation(fields: [userId], references: [id])
  userId  String? @unique
}

model Profile {
  id           String     @id @default(cuid())
  firstName    String?    @db.String(50)
  lastName     String?    @db.String(50)
  placeOfBirth String?
  dateOfBirth  DateTime?  @db.Date
  gender       GenderType @default(unknown)
  address      String?    @db.String(50)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt()
  user         User?      @relation(fields: [userId], references: [id])
  userId       String?    @unique
  media        Media?     @relation(fields: [mediaId], references: [id])
  mediaId      String?    @unique
}

model UserSession {
  id        String   @id @default(cuid())
  ip        String?
  media     String?
  hashAt    String?  @unique @db.String(150)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  user      User?    @relation(fields: [userId], references: [id])
  userId    String?

  @@index([userId, hashAt, isActive])
}

model Property {
  id                 String           @id @default(cuid())
  cid                String?          @db.String(100)
  companyName        String?          @db.String(100)
  brandName          String           @db.String(100)
  companyEmail       String           @db.String(50)
  companyPhoneNumber String           @db.String(20)
  npwp               String?          @db.String(20)
  status             PropertyStatus   @default(inActive)
  address            String?
  flag               String?          @db.String(50)
  crmPropertyId      String?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt()
  deletedAt          DateTime?
  configuration      Configuration?
  playlists          Playlist[]
  propertyType       PropertyType?    @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId     String?
  postal             Postal?          @relation(fields: [postalId], references: [id])
  user               User?            @relation(fields: [userId], references: [id])
  userId             String?          @unique
  postalId           String?
  licenseKey         String?
  licenseType        String?          @db.String(50)
  byPlayed           PlayHistory[]
  schedules          Schedule[]
  packages           Package[]
  likes              TrackLike[]
  Queue              Queue[]
  search             SearchHistory[]
  activation         Activation[]
  currentPlayer      CurrentPlayer[]
  runningText        RunningText[]
  users              UserProperty[]
  contactPerson      ContactPerson[]
  DemoCodeDetails    DemoCodeDetail[]
  playlistAccess     PlaylistAccess[]

  @@index([cid, companyName, brandName, propertyTypeId])
}

model UserProperty {
  id         String   @id @default(cuid())
  user       User     @relation(fields: [userId], references: [id])
  userId     String
  property   Property @relation(fields: [propertyId], references: [id])
  propertyId String
  isDefault  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt()

  @@unique([userId, propertyId])
}

model Configuration {
  id         String    @id @default(cuid())
  options    Json      @db.JsonB
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt()
  property   Property? @relation(fields: [propertyId], references: [id])
  propertyId String?   @unique
}

model PropertyType {
  id                   String                 @id @default(cuid())
  name                 String                 @db.String(50)
  slug                 String                 @unique
  icon                 String?
  property             Property[]
  categoryCode         String?                @db.String(10)
  codeProperty         String?                @db.String(10)
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt()
  parent               PropertyType?          @relation("ParentChild", fields: [parentId], references: [id])
  parentId             String?
  children             PropertyType[]         @relation("ParentChild")
  type                 String?
  size                 PropertyTypeSize[]
  description          String?                @db.String(50)
  isHidden             Boolean?
  visibility           Boolean?               @default(true)
  addons               AddOn[]
  plan                 Plan[]
  planSubfolder        Plan[]                 @relation("PlanSubfolder")
  Voucher              Voucher[]
  license              License[]
  licenseSubfolder     License[]              @relation("LicenseSubfolder")
  product              Product[]
  productSubfolder     Product[]              @relation("ProductSubfolder")
  media                Media?                 @relation(fields: [mediaId], references: [id])
  mediaId              String?
  sequence             Int?
  DemoCodeSubfolder    DemoCodeSubProperty[]
  zoneTypeProperty     ZoneTypeProperty[]
  playlistPropertyType PlaylistPropertyType[]

  @@index([name])
  @@index([isHidden, visibility])
}

// Schema for Role Permission

model Role {
  id         String             @id @default(cuid())
  name       String             @db.String(50)
  type       RoleType           @default(internal)
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt()
  permission PermissionOnRole[]
  user       User[]
}

model PermissionOnRole {
  role         Role?       @relation(fields: [roleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  roleId       String
  permission   Permission? @relation(fields: [permissionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  permissionId String
  manage       Boolean     @default(true)
  read         Boolean     @default(true)
  create       Boolean     @default(true)
  update       Boolean     @default(true)
  delete       Boolean     @default(true)

  @@unique([roleId, permissionId])
}

model Permission {
  id        String             @id @default(cuid())
  module    String             @db.String(50)
  type      RoleType           @default(internal)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt()
  role      PermissionOnRole[]
}

model Media {
  id              String            @id @default(cuid())
  path            String            @db.String
  fileName        String            @unique
  name            String?           @db.String
  caption         String?           @db.String
  encoding        String?           @db.String(50)
  mimeType        String?           @db.String(50)
  size            Int               @default(0)
  dimension       String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt()
  profile         Profile?
  PropertyType    PropertyType[]
  partnership     Partnership[]
  product         Product[]
  banner          Banner?
  greetingContent GreetingContent[]
}

model Setting {
  type   String @id
  option Json   @db.JsonB
}

model Notification {
  id        String   @id @default(cuid())
  to        User?    @relation("to", fields: [toId], references: [id])
  toId      String?
  title     String?
  url       String?
  message   String?
  related   String?
  relatedId String?
  type      String?
  tags      String[]
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  from      User?    @relation("from", fields: [fromId], references: [id])
  fromId    String?
}

model JobPosition {
  id            String          @id @default(cuid())
  name          String          @db.String(100)
  description   String?         @db.String(255)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt()
  allowToCopy   JobPosition?    @relation("AllowToCopy", fields: [allowToCopyId], references: [id])
  allowToCopyId String?
  copiedBy      JobPosition[]   @relation("AllowToCopy")
  contactPerson ContactPerson[]
}

model ContactPerson {
  id            String      @id @default(cuid())
  firstName     String?     @db.String(50)
  lastName      String?     @db.String(50)
  email         String?     @db.String(100)
  phone         String?     @db.String(20)
  jobPosition   JobPosition @relation(fields: [jobPositionId], references: [id])
  jobPositionId String
  property      Property    @relation(fields: [propertyId], references: [id])
  propertyId    String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt()

  @@index([email, phone])
}
