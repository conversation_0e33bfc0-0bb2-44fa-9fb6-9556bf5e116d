generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["omitApi", "prismaSchemaFolder"]
}

datasource db {
  provider = "cockroachdb"
  url      = env("DATABASE_URL")
}

model AdminLog {
  id        String   @id @default(cuid())
  module    String
  action    String
  endpoint  String
  method    String
  userId    String?
  userName  String?
  createdAt DateTime @default(now())
}
