enum QueueType {
  all
  track
  album
  playlist
  myplaylist
}

model Queue {
  id                 String       @id @default(cuid())
  name               String?
  activationId       String
  activation         Activation   @relation(fields: [activationId], references: [id], onDelete: Cascade)
  tracks             QueueTrack[]
  currentlyPlaying   Track?       @relation(fields: [currentlyPlayingId], references: [id])
  currentlyPlayingId String?
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt
  Property           Property?    @relation(fields: [propertyId], references: [id])
  propertyId         String?
  type               QueueType    @default(track)

  @@index([propertyId, activationId])
}

model QueueTrack {
  id      String   @id @default(cuid())
  queueId String
  trackId String
  queue   Queue    @relation(fields: [queueId], references: [id], onDelete: Cascade)
  track   Track    @relation(fields: [trackId], references: [id])
  uri     String?  @db.String(50)
  order   Int      @default(0)
  current Boolean? @default(false)
  addedAt DateTime @default(now())

  @@index([queueId, order])
  @@index([queueId, current])
  @@index([queueId])
}
