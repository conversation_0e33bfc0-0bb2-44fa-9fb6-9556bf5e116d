enum PlaylistType {
  folder
  showcase
  tracklist
}

// Schema for Playlists
model Playlist {
  id                     String              @id @default(cuid())
  name                   String              @db.String(200)
  description            String?
  color                  String?
  tracks                 TrackOnPlaylist[]
  property               Property?           @relation(fields: [propertyId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  propertyId             String?
  activation             Activation?         @relation(fields: [activationId], references: [id])
  activationId           String?
  playlistCategory       PlaylistCategory?   @relation(fields: [playlistCategoryId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  playlistCategoryId     String?
  schedule               ScheduleContent[]
  type                   PlaylistType        @default(tracklist)
  permission             String?
  linkCode               String?
  playlistId             String?
  order                  Int                 @default(0)
  parent                 Playlist?           @relation("PlaylistSub", fields: [playlistId], references: [id])
  childs                 Playlist[]          @relation("PlaylistSub")
  createdAt              DateTime            @default(now())
  updatedAt              DateTime            @updatedAt()
  publish                Boolean             @default(true)
  thumbnail              PlaylistThumbnail?
  totalDuration          Int?                @default(0)
  playlistFollower       PlaylistFollower[]
  playlistAccess         PlaylistAccess[]
  user                   User?               @relation(fields: [userId], references: [id])
  userId                 String?
  platform               String?
  playlistPlatform       PlaylistPlatform[]
  playlistPropertyType   PlaylistPropertyType[]


  @@index([name, publish])
  @@index([playlistId, activationId, propertyId])
}

model PlaylistPlatform{
  id                        String                        @id @default(cuid())
  playlistId                String?
  playlist                  Playlist?                     @relation(fields: [playlistId], references: [id])
  platform                  String?
  order                     Int                           @default(0)
  isNameHide                Boolean?                      @default(false)
  createdAt                 DateTime                      @default(now())
  updatedAt                 DateTime                      @updatedAt()
  playlistPlatformThumbnail PlaylistPlatformThumbnail[]
  isPriority                Boolean                       @default(false)
}

// used for filtering data per industry
model PlaylistPropertyType{
  id                        String                        @id @default(cuid())
  playlistId                String?
  playlist                  Playlist?                     @relation(fields: [playlistId], references: [id])
  propertyType              PropertyType?                 @relation(fields: [propertyTypeId], references: [id], onDelete: SetNull, onUpdate: SetNull)
  propertyTypeId            String?
  createdAt                 DateTime                      @default(now())
  updatedAt                 DateTime                      @updatedAt()
}

model PlaylistPlatformThumbnail {
  id                 String               @id @default(cuid())
  path               String?              @db.String
  fileName           String?              @db.String(150)
  name               String?              @db.String
  caption            String?              @db.String
  encoding           String?              @db.String(50)
  mimeType           String?              @db.String(50)
  size               Int                  @default(0)
  ratio              String?
  resolution         String? 
  playlistPlatform   PlaylistPlatform?    @relation(fields: [playlistPlatformId], references: [id])
  playlistPlatformId String?              
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt()
}

model PlaylistThumbnail {
  id         String    @id @default(cuid())
  path       String?   @db.String
  fileName   String?   @db.String(150)
  name       String?   @db.String
  caption    String?   @db.String
  encoding   String?   @db.String(50)
  mimeType   String?   @db.String(50)
  size       Int       @default(0)
  playlist   Playlist? @relation(fields: [playlistId], references: [id])
  playlistId String?   @unique
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt()
}

model PlaylistCategory {
  id        String     @id @default(cuid())
  name      String     @unique
  playlists Playlist[]
}

model PlaylistLabel {
  id   String @id @default(cuid())
  name String @unique
}

model PlaylistFollower {
  id               String          @id @default(cuid())
  playlist         Playlist?       @relation(fields: [playlistId], references: [id])
  playlistId       String?
  propertyId       String?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt()
  activation       Activation?     @relation(fields: [activationId], references: [id])
  activationId     String?
}

model PlaylistAccess {
  id               String          @id @default(cuid())
  playlist         Playlist?       @relation(fields: [playlistId], references: [id])
  playlistId       String?
  propertyId       String?
  property         Property[]
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt()
  activation       Activation?     @relation(fields: [activationId], references: [id])
  activationId     String?
}