enum PropertyStatus {
  active
  suspend
  pending
  inActive
}

enum ProductType {
  plan
  license
  product
  variant
}

enum TimeType {
  hourly
  daily
  monthly
  weekly
  yearly
}

enum PlanType {
  fix
  enterprise
  custom
  trial
}

enum AddOnType {
  enterprise
  custom
}

enum PaymentStatus {
  unpaid
  paid
  waiting
  cancel
}

enum OrderStatus {
  pending
  completed
  cancelled
}

enum VoucherAction {
  created
  updated
  activated
  inactivated
  deleted
  redeemed
}

enum packageDuration {
  year
  month
  day
}

enum TypeProperty {
  license
  plan
}

enum VoucherDetailStatus {
  available
  used
  expired
  reserved
  cancelled
}

enum PaymentType {
  bank_transfer
  e_wallet
  credit_card
  cash
  other
}

enum PaymentMethodStatus {
  active
  inactive
}

enum DiscountType {
  flat
  percentage
}

enum InputType {
  percentage
  number
}

model Reseller {
  id        String   @id @default(cuid())
  name      String?  @db.String(50)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
  user      User?    @relation(fields: [userId], references: [id])
  userId    String   @unique

  @@index([name])
}

model Tax {
  id          String       @id @default(cuid())
  name        String       @db.String(30)
  type        String?      @db.String(30)
  startDate   DateTime?    @db.Date
  endDate     DateTime?    @db.Date
  nominal     Decimal      @default(0) @db.Decimal(5, 2)
  description String       @db.String(50)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt()
  taxLicense  TaxLicense[]
  taxPlan     TaxPlan[]
  taxAddon    TaxAddon[]
  taxProduct  TaxProduct[]

  @@index([name])
}

model TaxLicense {
  id        String  @id @default(cuid())
  tax       Tax     @relation(fields: [taxId], references: [id])
  taxId     String
  license   License @relation(fields: [licenseId], references: [id])
  licenseId String

  @@index([taxId, licenseId])
}

model TaxPlan {
  id     String @id @default(cuid())
  tax    Tax    @relation(fields: [taxId], references: [id])
  taxId  String
  plan   Plan   @relation(fields: [planId], references: [id])
  planId String

  @@index([taxId, planId])
}

model TaxProduct {
  id        String  @id @default(cuid())
  tax       Tax     @relation(fields: [taxId], references: [id])
  taxId     String
  product   Product @relation(fields: [productId], references: [id])
  productId String

  @@index([taxId, productId])
}

model TaxAddon {
  id      String @id @default(cuid())
  tax     Tax    @relation(fields: [taxId], references: [id])
  taxId   String
  addon   AddOn  @relation(fields: [addonId], references: [id])
  addonId String

  @@index([taxId, addonId])
}

model AddOn {
  id             String        @id @default(cuid())
  price          Decimal       @default(0) @db.Decimal(10, 2)
  qty            Int           @default(1)
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt()
  type           AddOnType     @default(custom)
  propertyType   PropertyType? @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId String?
  feature        Feature?      @relation(fields: [featureId], references: [id])
  featureId      String?
  duration       TimeType      @default(monthly)
  taxAddon       TaxAddon[]
}

model TierPlan {
  id        String    @id @default(cuid())
  name      String
  tier      Int       @default(0)
  createdAt DateTime  @default(now())
  updatedAt DateTime? @updatedAt()
  Plan      Plan[]
}

model Product {
  id             String           @id @default(cuid())
  name           String           @db.String(50)
  description    String?          @db.String(250)
  isMultiple     Boolean?
  qty            Int?
  price          Decimal?         @default(0) @db.Decimal(10, 2)
  sku            Sku?             @relation(fields: [skuId], references: [id])
  skuId          String?
  publish        Boolean          @default(false)
  taxProduct     TaxProduct[]
  sequence       Int?
  businessType   String?
  variants       ProductVariant[]
  propertyType   PropertyType?    @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId String?
  subfolder      PropertyType?    @relation("ProductSubfolder", fields: [subfolderId], references: [id])
  subfolderId    String?
  media          Media?           @relation(fields: [mediaId], references: [id])
  mediaId        String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime?        @updatedAt()
  deletedAt      DateTime?
}

model ProductVariant {
  id          String    @id @default(cuid())
  name        String    @db.String(50)
  description String?   @db.String(250)
  price       Decimal?  @default(0) @db.Decimal(10, 2)
  product     Product?  @relation(fields: [productId], references: [id])
  productId   String?
  sku         Sku?      @relation(fields: [skuId], references: [id])
  skuId       String?
  sequence    Int?
  publish     Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt()
  deletedAt   DateTime?
}

model Plan {
  id             String          @id @default(cuid())
  name           String?
  description    String?         @db.String(250)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime?       @updatedAt()
  deletedAt      DateTime?
  basePrice      Decimal?        @default(0) @db.Decimal(10, 2)
  duration       TimeType?       @default(monthly)
  type           PlanType        @default(fix)
  isActive       Boolean         @default(false)
  endDate        DateTime?
  startDate      DateTime?
  addons         PlanAddon[]
  publish        Boolean         @default(false)
  propertyType   PropertyType?   @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId String?
  subfolder      PropertyType?   @relation("PlanSubfolder", fields: [subfolderId], references: [id])
  subfolderId    String?
  package        Package[]
  taxPlan        TaxPlan[]
  sku            Sku?            @relation(fields: [skuId], references: [id])
  skuId          String?
  FeatureCustom  FeatureCustom[]
  sequence       Int?
  DemoCodePlan   DemoCodePlan[]
  businessType   String?
  tierPlan       TierPlan?       @relation(fields: [tierPlanId], references: [id])
  tierPlanId     String?
}

model PlanAddon {
  id        String    @id @default(cuid())
  price     Decimal?  @default(0) @db.Decimal(10, 2)
  plan      Plan?     @relation(fields: [planId], references: [id])
  planId    String?
  qty       Int       @default(1)
  order     Int       @default(0)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt()
  deletedAt DateTime?
  feature   Feature?  @relation(fields: [featureId], references: [id])
  featureId String?
}

model Feature {
  id            String           @id @default(cuid())
  name          String           @unique @db.String(50)
  valueType     String?
  parent        Feature?         @relation("ParentChild", fields: [parentId], references: [id])
  parentId      String?
  children      Feature[]        @relation("ParentChild")
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt()
  packages      PackageFeature[]
  addon         AddOn[]
  plans         PlanAddon[]
  FeatureCustom FeatureCustom[]

  zoneFeature ZoneFeature[]
}

model FeatureCustom {
  id        String   @id @default(cuid())
  value     Int      @default(1)
  price     Decimal  @default(0) @db.Decimal(10, 2)
  plan      Plan?    @relation(fields: [planId], references: [id])
  planId    String?
  feature   Feature? @relation(fields: [featureId], references: [id])
  featureId String?
}

model Partnership {
  id           String                @id @default(cuid())
  name         String
  popularName  String
  crmUserId    String?
  media        Media?                @relation(fields: [mediaId], references: [id])
  mediaId      String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt()
  ReferralCode ReferralCode[]
  postal       Postal?               @relation(fields: [postalId], references: [id])
  postalId     String?
  properties   PropertyPartnership[]
}

model PropertyPartnership {
  id            String       @id @default(cuid())
  crmPropertyId String
  partnershipId String?
  partnership   Partnership? @relation(fields: [partnershipId], references: [id])
}

model ReferralCode {
  id                 String               @id @default(cuid())
  partnership        Partnership          @relation(fields: [partnershipId], references: [id])
  partnershipId      String
  code               String               @unique @db.String(15)
  discountType       DiscountType
  discountValue      Decimal?             @db.Decimal(10, 2)
  startDate          DateTime             @db.Date
  endDate            DateTime             @db.Date
  maxDiscountAmount  Decimal?             @db.Decimal(10, 2)
  maxUsage           Int?
  createdBy          String?              @db.String(50)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt()
  deletedAt          DateTime?
  status             Boolean              @default(true)
  lastSequence       Int                  @default(0)
  ReferralCodeDetail ReferralCodeDetail[]
  DemoCode           DemoCode[]
}

model Voucher {
  id                    String                  @id @default(cuid())
  name                  String                  @db.String(50)
  propertyType          PropertyType            @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId        String
  code                  String                  @unique @db.String(15)
  discountType          DiscountType
  discountValue         Decimal?                @db.Decimal(10, 2)
  startDate             DateTime                @db.Date
  endDate               DateTime                @db.Date
  maxDiscountAmount     Decimal?                @db.Decimal(10, 2)
  packageDuration       packageDuration?
  maxUsage              Int?
  createdBy             String?                 @db.String(50)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt()
  deletedAt             DateTime?
  status                Boolean                 @default(true)
  lastSequence          Int                     @default(0)
  lastUsedSequence      Int?
  publish               Boolean                 @default(true)
  voucherDetails        VoucherDetail[]
  voucherPaymentMethods VoucherPaymentMethods[]

  @@index([code, status, propertyTypeId, startDate, endDate])
}

model VoucherDetail {
  id        String              @id @default(cuid())
  voucher   Voucher             @relation(fields: [voucherId], references: [id], onDelete: Cascade)
  voucherId String
  code      String              @unique @db.String(100)
  sequence  Int
  status    VoucherDetailStatus @default(available)
  orderId   String?             @unique
  createdAt DateTime            @default(now())
  updatedAt DateTime?           @updatedAt()
  deletedAt DateTime?

  @@index([voucherId, status])
}

model ReferralCodeDetail {
  id             String              @id @default(cuid())
  referralCode   ReferralCode        @relation(fields: [referralCodeId], references: [id])
  referralCodeId String
  code           String              @unique @db.String(100)
  sequence       Int
  status         VoucherDetailStatus @default(available)
  orderId        String?             @unique
  deletedAt      DateTime?

  @@index([referralCodeId, status])
}

model DemoCode {
  id               String                @id @default(cuid())
  name             String                @db.String(50)
  plans            DemoCodePlan[]
  referralCode     ReferralCode?         @relation(fields: [referralCodeId], references: [id])
  referralCodeId   String?
  code             String                @unique @db.String(15)
  unpairedTill     Int?
  duration         Int
  status           Boolean               @default(true)
  maxUsage         Int?
  expiredAt        DateTime?             @db.Date
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  lastSequence     Int                   @default(0)
  isActive         Boolean               @default(true)
  lastUsedSequence Int?
  deletedAt        DateTime?
  DemoCodeDetail   DemoCodeDetail[]
  subFolder        DemoCodeSubProperty[]
}

model DemoCodePlan {
  democode   DemoCode? @relation(fields: [demoCodeId], references: [id])
  demoCodeId String
  plan       Plan?     @relation(fields: [planId], references: [id])
  planId     String

  @@id([demoCodeId, planId])
}

model DemoCodeSubProperty {
  demoCode       DemoCode?     @relation(fields: [demoCodeId], references: [id])
  demoCodeId     String
  propertyType   PropertyType? @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId String

  @@id([demoCodeId, propertyTypeId])
}

model DemoCodeDetail {
  id         String              @id @default(cuid())
  demoCode   DemoCode            @relation(fields: [demoCodeId], references: [id])
  demoCodeId String
  code       String              @unique @db.String(100)
  sequence   Int
  status     VoucherDetailStatus @default(available)
  deletedAt  DateTime?
  propertyId String?
  property   Property?           @relation(fields: [propertyId], references: [id], onDelete: Cascade)
}

model PaymentMethod {
  id                    String                  @id @default(cuid())
  name                  String                  @db.String(30)
  code                  String                  @db.String(20)
  type                  PaymentType?
  status                PaymentMethodStatus     @default(active)
  description           String                  @db.String(100)
  createdAt             DateTime                @default(now())
  updatedAt             DateTime                @updatedAt()
  VoucherPaymentMethods VoucherPaymentMethods[]

  @@index([name, id, type])
}

model VoucherPaymentMethods {
  id              String        @id @default(cuid())
  voucher         Voucher       @relation(fields: [voucherId], references: [id])
  voucherId       String
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId String

  @@index([voucherId, paymentMethodId])
}

model Package {
  id            String           @id @default(cuid())
  name          String
  itemType      String?          @db.String(50)
  itemId        String?
  isActive      Boolean          @default(false)
  qty           Int?             @default(1)
  activeAt      BigInt?
  expiredAt     BigInt?
  contractEndAt BigInt?
  trialEndAt    BigInt?
  status        String?          @db.String(30)
  duration      String?          @db.String(30)
  isTrial       Boolean?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt()
  property      Property?        @relation(fields: [propertyId], references: [id])
  propertyId    String?
  features      PackageFeature[]
  zoneFeature   ZoneFeature[]
  plan          Plan?            @relation(fields: [planId], references: [id])
  planId        String?
  orderId       String?

  @@index([propertyId, isActive])
  @@index([status, isActive])
}

model PackageFeature {
  id        String   @id @default(cuid())
  feature   Feature? @relation(fields: [featureId], references: [id])
  featureId String
  package   Package? @relation(fields: [packageId], references: [id])
  packageId String
  price     Decimal? @default(0) @db.Decimal(10, 2)
  qty       Int      @default(1)
  qouta     Int?     @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt()
}

model License {
  id               String         @id @default(cuid())
  propertyType     PropertyType?  @relation(fields: [propertyTypeId], references: [id])
  propertyTypeId   String?
  subfolder        PropertyType   @relation("LicenseSubfolder", fields: [subfolderId], references: [id])
  subfolderId      String
  name             String         @db.String(50)
  price            Decimal        @default(0) @db.Decimal(10, 2)
  isActive         Boolean        @default(true)
  description      String         @db.String(100)
  expiredAt        DateTime?      @db.Timestamptz(3)
  paymentFrequency TimeType?      @default(yearly)
  paymentStatus    PaymentStatus? @default(unpaid)
  taxLicense       TaxLicense[]
  startDate        DateTime?      @db.Date
  endDate          DateTime?      @db.Date
  createdAt        DateTime?      @default(now())
  updatedAt        DateTime?      @updatedAt
  sku              Sku?           @relation(fields: [skuId], references: [id])
  skuId            String?

  @@index([propertyTypeId, name, expiredAt])
}

model Sku {
  id             String           @id @default(cuid())
  code           String           @unique
  productType    ProductType
  plans          Plan[]
  licenses       License[]
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  Product        Product[]
  ProductVariant ProductVariant[]

  @@index([code])
}
