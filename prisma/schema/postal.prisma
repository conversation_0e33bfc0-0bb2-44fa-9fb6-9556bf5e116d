model Provinces {
  id     String   @id @default(cuid())
  name   String   @db.String(100)
  postal Postal[]

  @@index([name])
}

model Postal {
  id          String        @id @default(cuid())
  urban       String        @db.String(100)
  city        String        @db.String(100)
  district    String        @db.String(100)
  code        String        @db.String(10)
  provinces   Provinces?    @relation(fields: [provincesId], references: [id])
  provincesId String?
  property    Property[]
  partnership Partnership[]

  @@index([urban, city, district, code])
}
