enum ScheduleType {
  daily
  weekday
  weekend
  manual
}

enum ScheduleContentMode {
  Playlist
  Track
}

model Schedule {
  id           String             @id @default(cuid())
  name         String?            @db.String(150)
  mode         ScheduleType       @default(daily)
  modeValue    String[]
  startTime    String             @db.String(30)
  endTime      String             @db.String(30)
  repeatEnd    Boolean            @default(false)
  setVolume    Boolean?           @default(false)
  volume       Float              @default(0.0)
  expiredDate  DateTime?
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt()
  contents     ScheduleContent?
  property     Property?          @relation(fields: [propertyId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  propertyId   String?
  activation   Activation?        @relation(fields: [activationId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  activationId String?
  playMode     String?
  remainders   ScheduleReminder[]
  scheduleZone ScheduleZone[]
}

model ScheduleZone {
  scheduleId   String
  activationId String
  schedule     Schedule   @relation(fields: [scheduleId], references: [id], onDelete: Cascade)
  activation   Activation @relation(fields: [activationId], references: [id], onDelete: Cascade)

  @@id([scheduleId, activationId])
}

model ScheduleReminder {
  id         String    @id @default(cuid())
  schedule   Schedule? @relation(fields: [scheduleId], references: [id])
  scheduleId String?
  isRead     Boolean   @default(false)
  createdAt  DateTime  @default(now())
}

model ScheduleContent {
  id         String    @id @default(cuid())
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt()
  schedule   Schedule? @relation(fields: [scheduleId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  scheduleId String    @unique
  playlist   Playlist? @relation(fields: [playlistId], references: [id])
  playlistId String?
}
