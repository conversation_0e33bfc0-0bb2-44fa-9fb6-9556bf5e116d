enum ContentType {
  SONG
  ARTIST
  ALBUM
  PLAYLIST
  ALL
}

enum PlatformType {
  WEB
  ATV
}

model SearchHistory {
  id         String               @id @default(cuid())
  Property   Property?            @relation(fields: [propertyId], references: [id])
  propertyId String?
  keyword    String?
  type       String?
  createdAt  DateTime?            @default(now())
  tracks     SearchHistoryTrack[]
}

model SearchHistoryTrack {
  searchHistory   SearchHistory @relation(fields: [searchHistoryId], references: [id])
  searchHistoryId String
  track           Track         @relation(fields: [trackId], references: [id])
  trackId         String

  @@id([searchHistoryId, trackId])
}

model Track {
  id            String               @id @default(cuid())
  key           String               @unique @db.String(50)
  source        Json?                @db.JsonB
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt()
  count         BigInt?              @default(0)
  histories     PlayHistory[]
  playlist      TrackOnPlaylist[]
  tsv           String?              @db.String
  like          TrackLike[]
  Queue         Queue[]
  QueueTrack    QueueTrack[]
  searchTrack   SearchHistoryTrack[]
  currentPlayer CurrentPlayer[]

  @@index([key])
  @@index([key, tsv])
}

model TrackOnPlaylist {
  playlist   Playlist? @relation(fields: [playlistId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  playlistId String
  track      Track?    @relation(fields: [trackId], references: [id])
  trackId    String
  order      Int       @default(0)

  @@id([playlistId, trackId])
}

model TrackLike {
  track        Track?     @relation(fields: [trackId], references: [id])
  trackId      String
  property     Property?  @relation(fields: [propertyId], references: [id])
  propertyId   String
  activationId String
  activation   Activation @relation(fields: [activationId], references: [id])
  createdAt    DateTime   @default(now())

  @@id([trackId, propertyId, activationId])
}

model PlayHistory {
  id           String     @id @default(cuid())
  playAt       DateTime?  @default(now())
  endAt        DateTime?
  duration     Int
  durationType String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt()
  track        Track?     @relation(fields: [trackId], references: [id])
  trackId      String?
  property     Property?  @relation(fields: [propertyId], references: [id])
  propertyId   String?
  activation   Activation @relation(fields: [activationId], references: [id])
  activationId String
  ip           String?    @db.String(60)
  isp          String?    @db.String(100)
  os           String?    @db.String(30)
  geo          String?    @db.String(40)
  country      String?    @db.String(40)
  macAddress   String?    @db.String(40)
  region       String?    @db.String(120)
  city         String?    @db.String(120)
  zip          String?    @db.String(20)
  flag         String?    @db.String(50)
  timeZone     String?    @db.String(120)
  userAgent    String?
  playerType   String?

  @@index([activationId])
  @@index([activationId, createdAt])
}

model CurrentPlayer {
  id           String     @id @default(uuid())
  activation   Activation @relation(fields: [activationId], references: [id])
  activationId String
  track        Track?     @relation(fields: [trackId], references: [id])
  trackId      String?
  duration     Int?       @default(0)
  counter      Int?       @default(1)
  durationType String?
  ip           String?    @db.String(60)
  isp          String?    @db.String(100)
  os           String?    @db.String(30)
  geo          String?    @db.String(40)
  country      String?    @db.String(40)
  region       String?    @db.String(120)
  city         String?    @db.String(120)
  zip          String?    @db.String(20)
  timeZone     String?    @db.String(120)
  macAddress   String?    @db.String(40)
  userAgent    String?
  paused       Boolean    @default(false)
  property     Property?  @relation(fields: [propertyId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  propertyId   String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt()

  @@index([activationId])
  @@index([trackId])
}

model Content {
  id        String       @id @default(cuid())
  type      ContentType
  platform  PlatformType
  order     Int          @default(0)
  itemId    String?      @db.String(50)
  published Boolean      @default(true)
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt()

  @@index([platform, published, type])
}
