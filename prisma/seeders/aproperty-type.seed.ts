import { PrismaClient } from '@prisma/client';
import slugify from '../../libs/common/src/helpers/slug.helper';

const TypePropertyData = [
  { name: 'hospitality', children: [{ name: 'hotel' }, { name: 'apartment' }] },
  {
    name: 'corporate',
    children: [{ name: 'office' }, { name: 'shopping mall' }],
  },
  {
    name: 'healthcare',
    children: [{ name: 'hospital' }, { name: 'clinics' }],
  },
  {
    name: 'f&b',
    children: [{ name: 'cafe' }, { name: 'bar' }, { name: 'club' }],
  },
  { name: 'karaoke' },
  {
    name: 'public transportation',
    children: [
      { name: 'in flight' },
      { name: 'bus' },
      { name: 'ship' },
      { name: 'train' },
    ],
  },
  { name: 'recreation' },
  { name: 'cinema' },
  {
    name: 'telco',
    children: [{ name: 'ring back tone' }],
  },
  {
    name: 'broadcasting',
    children: [{ name: 'radio' }, { name: 'television' }],
  },
];

export default async function seedFunction(prisma: PrismaClient) {
  await prisma.propertyType.deleteMany({});

  for (let idx = 0; idx < TypePropertyData.length; idx++) {
    const element = TypePropertyData[idx];
    const created = await prisma.propertyType.create({
      data: { name: element.name, slug: slugify(element.name) },
    });
    if (element.hasOwnProperty('children')) {
      const childrens = element.children;
      await prisma.propertyType.createMany({
        data: childrens.map((chd) => {
          return {
            name: chd.name,
            parentId: created.id,
            slug: slugify(chd.name),
          };
        }),
      });
    }
  }
}
