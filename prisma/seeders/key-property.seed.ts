import { PrismaClient, Prisma } from '@prisma/client';

export default async function seedFunction(prisma: PrismaClient) {
  const properties = await prisma.property.findMany({ select: { id: true } });

  if (properties.length === 0) {
    console.error('Tidak ada data di tabel property.');
    return;
  }

  for (const property of properties) {
    const existingConfig = await prisma.configuration.findUnique({
      where: { propertyId: property.id },
      select: { options: true },
    });

    const currentOptions: Prisma.JsonObject =
      existingConfig?.options && !Array.isArray(existingConfig.options)
        ? (existingConfig.options as Prisma.JsonObject)
        : {};

    const newOptions: Prisma.JsonObject = {
      ...currentOptions,
      settingKey: Math.floor(100000 + Math.random() * 900000).toString(),
    };

    await prisma.configuration.upsert({
      where: { propertyId: property.id },
      update: { options: newOptions },
      create: { propertyId: property.id, options: newOptions },
    });

    console.log(`Konfigurasi untuk propertyId ${property.id} diproses.`);
  }

  console.log('Seeder berhasil dijalankan.');
}
