import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { PermissionModule } from '../../libs/common/src/constants/permission.constant';

export default async function seedFunction(prisma: PrismaClient) {
  await prisma.permissionOnRole.deleteMany({});
  await prisma.permission.deleteMany({});
  const modules = Object.values(PermissionModule);
  const permissionIds = [];
  for (let idx = 0; idx < modules.length; idx++) {
    const element = modules[idx];
    const item = await prisma.permission.create({ data: { module: element } });
    permissionIds.push(item.id);
  }

  await prisma.role.deleteMany({});
  const role = await prisma.role.create({
    data: {
      name: 'super admin',
      permission: {
        createMany: {
          data: permissionIds.map((id) => {
            return {
              permissionId: id,
              create: true,
              read: true,
              delete: true,
              update: true,
              manage: true,
            };
          }),
        },
      },
    },
  });

  await prisma.userSession.deleteMany({});
  await prisma.property.deleteMany({});
  await prisma.user.deleteMany({});
  await prisma.profile.deleteMany({});
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      phoneNumber: '085727283283',
      activationCode: '333',
      password: bcrypt.hashSync('12345', 10),
      username: 'xxx',
      resetPasswordCode: '5555',
      type: 'internal',
      provider: 'local',
      role: {
        connect: {
          id: role.id,
        },
      },
      profile: {
        create: {
          address: 'jakarta',
          firstName: 'cdf',
          lastName: 'kambing',
        },
      },
    },
  });
}
