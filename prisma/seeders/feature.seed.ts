import { PrismaClient } from '@prisma/client';
const features = [
  { name: 'license for business use', valueType: 'boolean', childs: [] },
  { name: 'player node', valueType: 'number', childs: [] },
  { name: 'schedules', valueType: 'boolean', childs: [] },
  { name: 'song quota', valueType: 'number', childs: [] },
  { name: 'playlist', valueType: 'boolean', childs: [] },
  { name: 'play offline', valueType: 'boolean', childs: [] },
  { name: 'premier support', valueType: 'boolean', childs: [] },
  { name: 'explicit lyrics filter', valueType: 'boolean', childs: [] },
  { name: 'centralized music control', valueType: 'boolean', childs: [] },
  { name: 'onboarding assistance', valueType: 'boolean', childs: [] },
  { name: 'dedicated account manager', valueType: 'boolean', childs: [] },
  { 
    name: 'jingle',
    valueType: 'boolean',
    childs: [
      {
        name: 'jingle library',
        valueType: 'number'
      },
      {
        name: 'jingle playlist',
        valueType: 'number'
      },
      {
        name: 'jingle group device',
        valueType: 'number'
      },
      {
        name: 'jingle schedule',
        valueType: 'number'
      },
    ]
  },
];

export default async function seedFunction(prisma: PrismaClient) {
  await prisma.feature.deleteMany({});
  const featuresId = [];
  let fIdx = 0;

  for (let idx = 0; idx < features.length; idx++) {
    const element = features[idx];
    const startId = fIdx + 1;
    const fid = `f-${startId.toString().padStart(3, '0')}`;
    const createFeature = await prisma.feature.create({
      data: { id: fid, name: element.name, valueType: element.valueType },
    });
    featuresId.push(createFeature.id);

    if (element.childs.length > 0) {
      for (let i of element.childs) {
        const childFeature = await prisma.feature.create({
          data: { id: fid, name: i.name, valueType: i.valueType, parentId: createFeature.id },
        });
        featuresId.push(childFeature.id);
      }
    }
  }
  const propertyTypes = await prisma.propertyType.findMany();
  //await prisma.plan.create({
  //  data: {
  //    name: 'basic',
  //    description: 'basic plan',
  //    features: {
  //      createMany: {
  //        data: featuresId.map((fts) => {
  //          return { featureId: fts };
  //        }),
  //      },
  //    },
  //    price: {
  //      createMany: {
  //        data: [
  //          {
  //            duration: 'hourly',
  //            price: '1000.100',
  //            isDefault: false,
  //          },
  //          {
  //            duration: 'daily',
  //            price: '30000.20',
  //            isDefault: false,
  //          },
  //          {
  //            duration: 'monthly',
  //            price: '500000.20',
  //            isDefault: false,
  //          },
  //          {
  //            duration: 'yearly',
  //            price: '1000000.20',
  //            isDefault: false,
  //          },
  //        ],
  //      },
  //    },
  //    type: {
  //      createMany: {
  //        data: propertyTypes.map((pt) => {
  //          return { propertyTypeId: pt.id };
  //        }),
  //      },
  //    },
  //  },
  //});
}
