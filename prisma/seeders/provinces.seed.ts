import { PrismaClient } from '@prisma/client';
import { readFileSync } from 'fs';
import { join } from 'path';

export default async function seedFunction(prisma: PrismaClient) {
  const master = JSON.parse(
    readFileSync(join(__dirname, '../postal.json'), 'utf-8'),
  );
  await prisma.provinces.deleteMany({});
  await prisma.postal.deleteMany({});
  const provinces = Object.values(master.province);

  for (let province = 0; province < provinces.length; province++) {
    const element = provinces[province];
    const code = element['province_code'];
    const postals = master.postal[code];
    await prisma.provinces.create({
      data: {
        name: element['province_name'],
        postal: {
          createMany: {
            data: postals.map((postal) => {
              return {
                city: postal['city'],
                code: postal['postal_code'],
                district: postal['sub_district'],
                urban: postal['urban'],
              };
            }),
          },
        },
      },
    });
  }
}
