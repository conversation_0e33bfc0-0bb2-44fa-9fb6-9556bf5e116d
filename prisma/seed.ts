import { PrismaClient } from '@prisma/client';

import { readdirSync } from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();
async function main() {
  const seedFilesPath = join(__dirname, 'seeders');
  const seedFiles = readdirSync(seedFilesPath).filter((file) =>
    file.endsWith('.seed.ts'),
  );

  for (const seedFile of seedFiles) {
    const seedFilePath = join(seedFilesPath, seedFile);

    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const { default: seedFunction } = require(seedFilePath);
    await seedFunction(prisma);
  }
}
main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  });
